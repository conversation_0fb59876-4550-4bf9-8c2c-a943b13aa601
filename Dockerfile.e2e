# E2E Test Runner Dockerfile - mirrors CI environment exactly
FROM node:20-alpine

# Install system dependencies needed for Playwright
RUN apk add --no-cache \
    chromium \
    firefox \
    webkit2gtk-4.1 \
    curl \
    postgresql-client \
    bash \
    git

# Set Playwright to use system browsers
ENV PLAYWRIGHT_BROWSERS_PATH=/ms-playwright
ENV PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies (including dev dependencies for testing)
RUN npm ci

# Install Playwright and browsers
RUN npx playwright install --with-deps

# Copy source code
COPY . .

# Create a health check script
RUN echo '#!/bin/sh\necho "E2E runner ready"' > /health.sh && chmod +x /health.sh

# Default command (will be overridden by docker-compose)
CMD ["echo", "E2E runner ready"]

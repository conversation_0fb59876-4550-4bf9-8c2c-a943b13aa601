name: CI/CD

on:
  push:
    branches: [ main, master ]
  pull_request:

permissions:
  contents: read
  packages: write

concurrency:
  group: ci-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build-app-and-test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: benefitlens
          POSTGRES_USER: benefitlens_user
          POSTGRES_PASSWORD: benefitlens_password
        ports:
          - 5432:5432
        options: >-
          --health-cmd="pg_isready -U benefitlens_user -d benefitlens"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=10

    env:
      # Required environment variables for build and tests
      DATABASE_URL: postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
      CACHE_TYPE: postgresql
      NEXT_PUBLIC_APP_URL: http://localhost:3000
      SESSION_SECRET: ci_session_secret_for_build_only
      NODE_ENV: production
      USE_LOCAL_AUTH: true

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: package-lock.json
      - name: Install dependencies
        run: npm ci --legacy-peer-deps --include=dev

      - name: Install PostgreSQL client
        run: |
          sudo apt-get update
          sudo apt-get install -y postgresql-client

      - name: Wait for Postgres
        env:
          PGPASSWORD: benefitlens_password
        run: |
          for i in {1..30}; do
            if pg_isready -h localhost -p 5432 -U benefitlens_user -d benefitlens; then
              echo "Postgres is ready"; break
            fi
            echo "Waiting for Postgres... ($i)"; sleep 2
          done

      - name: Initialize database schema
        env:
          PGPASSWORD: benefitlens_password
        run: |
          psql -h localhost -U benefitlens_user -d benefitlens -f database/init/01-init.sql

      # - name: Run linting
      #   run: npm run lint
      - name: Build application
        run: npm run build --if-present
      # - name: Run tests
      #   run: npm test -- --run

  # Call test pipeline to ensure all tests pass before building image
  run-test-pipeline:
    needs: build-app-and-test
    uses: ./.github/workflows/test-pipeline.yml
    secrets: inherit
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'

  # Build and deploy job (only on main/master branch and after tests pass)
  build-image-and-deploy:
    needs: [build-app-and-test, run-test-pipeline]
    runs-on: ubuntu-latest
    environment: production
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'

    steps:
      - uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/arm64  # For Raspberry Pi 5
          push: true
          tags: |
            ${{ secrets.DOCKER_USERNAME }}/benefitlens:latest
            ${{ secrets.DOCKER_USERNAME }}/benefitlens:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Deploy to Raspberry Pi (self-hosted runner)
  deploy:
    needs: [build-image-and-deploy, run-test-pipeline]
    environment: production
    runs-on: self-hosted
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'

    steps:
      - uses: actions/checkout@v4

      - name: Create production environment file
        run: |
          cp .env.prod.example .env.prod
          sed -i "s/your_secure_postgres_password_here/${{ secrets.POSTGRES_PASSWORD }}/g" .env.prod
          sed -i "s/your_random_session_secret_here/${{ secrets.SESSION_SECRET }}/g" .env.prod
          # sed -i "s|http://localhost:3000|${{ secrets.APP_URL }}|g" .env.prod
          # sed -i "s/<EMAIL>/${{ secrets.SMTP_USER }}/g" .env.prod
          sed -i "s/your_mailgun_api_key_here/${{ secrets.SMTP_PASS }}/g" .env.prod

      - name: Pull latest Docker image
        run: docker pull ${{ secrets.DOCKER_USERNAME }}/benefitlens:latest

      - name: Deploy with Docker Compose
        run: |
          docker-compose -p benefitlens -f docker-compose.prod.yml --env-file .env.prod up -d --remove-orphans

      - name: Wait for application to be ready
        run: |
          echo "Waiting for application to start..."
          for i in {1..30}; do
            if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
              echo "Application is ready!"
              break
            fi
            echo "Waiting... ($i/30)"
            sleep 10
          done

      - name: Clean up old Docker images
        run: docker image prune -f


-- Migration: Update activity log event types (CORRECTED)
-- This migration updates the activity log table to include new event types while preserving existing ones

-- Drop the existing check constraint
ALTER TABLE activity_log DROP CONSTRAINT IF EXISTS activity_log_event_type_check;

-- Add the updated check constraint with ALL event types (existing + new)
ALTER TABLE activity_log ADD CONSTRAINT activity_log_event_type_check 
CHECK (event_type IN (
  'benefit_automatically_removed',
  'benefit_disputed',
  'benefit_removal_dispute_approved',
  'benefit_removal_dispute_cancelled',
  'benefit_removal_dispute_rejected',
  'benefit_removal_dispute_submitted',
  'benefit_verified',
  'cache_refresh',
  'company_added',
  'session_cleanup',
  'user_registered',
  'user_deleted'
));

-- Update the comment to reflect all event types
COMMENT ON COLUMN activity_log.event_type IS 'Type of event: benefit_automatically_removed, benefit_disputed, benefit_removal_dispute_approved, benefit_removal_dispute_cancelled, benefit_removal_dispute_rejected, benefit_removal_dispute_submitted, benefit_verified, cache_refresh, company_added, session_cleanup, user_registered, user_deleted';

-- Log this migration
INSERT INTO migration_log (migration_name, description) 
VALUES ('015-update-activity-log-event-types-fixed', 'Update activity log table to include user_deleted event type while preserving all existing event types');
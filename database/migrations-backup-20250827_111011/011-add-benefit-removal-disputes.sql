-- Migration: Add benefit removal disputes table
-- This migration adds support for democratic benefit removal through disputes

-- Benefit removal disputes table
CREATE TABLE benefit_removal_disputes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_benefit_id UUID NOT NULL REFERENCES company_benefits(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL, -- User ID from auth system
    reason TEXT NOT NULL, -- Reason for requesting removal
    status VARCHAR(50) CHECK (status IN ('pending', 'approved', 'rejected')) DEFAULT 'pending',
    admin_user_id VARCHAR(255), -- Admin who approved/rejected
    admin_comment TEXT, -- Admin's comment on the decision
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    -- Ensure one dispute per user per benefit
    UNIQUE(company_benefit_id, user_id)
);

-- Indexes for better performance
CREATE INDEX idx_benefit_removal_disputes_company_benefit_id ON benefit_removal_disputes(company_benefit_id);
CREATE INDEX idx_benefit_removal_disputes_user_id ON benefit_removal_disputes(user_id);
CREATE INDEX idx_benefit_removal_disputes_status ON benefit_removal_disputes(status);
CREATE INDEX idx_benefit_removal_disputes_created_at ON benefit_removal_disputes(created_at);

-- Trigger for updated_at
CREATE TRIGGER update_benefit_removal_disputes_updated_at BEFORE UPDATE ON benefit_removal_disputes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security
ALTER TABLE benefit_removal_disputes ENABLE ROW LEVEL SECURITY;

-- Benefit removal disputes are viewable by admins and the users who created them
CREATE POLICY "Benefit removal disputes are viewable by admins" ON benefit_removal_disputes
    FOR SELECT USING (true); -- Will be restricted by application logic

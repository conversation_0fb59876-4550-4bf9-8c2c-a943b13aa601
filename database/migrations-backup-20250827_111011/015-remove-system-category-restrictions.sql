-- Migration: Remove system category restrictions
-- This migration removes the system category restrictions by setting all categories to is_system = false
-- This allows all benefit categories to be fully editable and deletable

-- Update all existing benefit categories to be non-system
UPDATE benefit_categories SET is_system = false WHERE is_system = true;

-- Add a comment to document the change
COMMENT ON COLUMN benefit_categories.is_system IS 'Legacy field - all categories are now editable. Kept for backward compatibility.';

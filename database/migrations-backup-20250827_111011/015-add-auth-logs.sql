-- Migration: Add authentication logs table
-- This table stores detailed logs of authentication events for admin review

CREATE TABLE auth_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN (
        'sign_in_request', 
        'sign_up_request', 
        'magic_link_verification', 
        'session_creation', 
        'rate_limit_hit'
    )),
    status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'failure')),
    email VARCHAR(255),
    error_type VARCHAR(100),
    error_message TEXT,
    ip_address INET,
    user_agent TEXT,
    token_used VARCHAR(20), -- Only store partial token for security
    failure_reason TEXT,
    additional_context JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for efficient querying
CREATE INDEX idx_auth_logs_created_at ON auth_logs(created_at);
CREATE INDEX idx_auth_logs_status ON auth_logs(status);
CREATE INDEX idx_auth_logs_event_type ON auth_logs(event_type);
CREATE INDEX idx_auth_logs_email ON auth_logs(email);
CREATE INDEX idx_auth_logs_ip_address ON auth_logs(ip_address);
CREATE INDEX idx_auth_logs_status_created_at ON auth_logs(status, created_at);

-- Composite index for admin dashboard queries
CREATE INDEX idx_auth_logs_failure_stats ON auth_logs(status, event_type, created_at) 
WHERE status = 'failure';

-- Add comment for documentation
COMMENT ON TABLE auth_logs IS 'Detailed logs of authentication events for security monitoring and admin review';
COMMENT ON COLUMN auth_logs.event_type IS 'Type of authentication event (sign_in_request, sign_up_request, etc.)';
COMMENT ON COLUMN auth_logs.status IS 'Success or failure status of the authentication event';
COMMENT ON COLUMN auth_logs.error_type IS 'Categorized error type for failed events';
COMMENT ON COLUMN auth_logs.failure_reason IS 'Human-readable explanation of why the authentication failed';
COMMENT ON COLUMN auth_logs.token_used IS 'Partial magic link token (first 8 chars) for tracking';
COMMENT ON COLUMN auth_logs.additional_context IS 'Additional context data in JSON format';

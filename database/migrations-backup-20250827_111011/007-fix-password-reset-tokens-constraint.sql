-- Migration: Fix password_reset_tokens foreign key constraint
-- This migration fixes the password_reset_tokens table to have proper CASCADE behavior
-- Date: 2025-01-31

-- Check if password_reset_tokens table exists and fix its foreign key constraint
DO $$
BEGIN
    -- Check if the table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'password_reset_tokens') THEN
        
        -- Drop the existing foreign key constraint if it exists
        IF EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE table_name = 'password_reset_tokens' 
            AND constraint_name = 'password_reset_tokens_user_id_fkey'
        ) THEN
            ALTER TABLE password_reset_tokens DROP CONSTRAINT password_reset_tokens_user_id_fkey;
        END IF;
        
        -- Add the foreign key constraint with CASCADE delete
        ALTER TABLE password_reset_tokens 
        ADD CONSTRAINT password_reset_tokens_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Fixed password_reset_tokens foreign key constraint with CASCADE delete';
        
    ELSE
        RAISE NOTICE 'password_reset_tokens table does not exist - no action needed';
    END IF;
END $$;

-- Add to migration log
INSERT INTO migration_log (migration_name, description, applied_at) 
VALUES (
  '007-fix-password-reset-tokens-constraint', 
  'Fixed password_reset_tokens foreign key constraint to use CASCADE delete',
  NOW()
) ON CONFLICT (migration_name) DO NOTHING;

-- Create migration_log table if it doesn't exist (in case previous migration didn't run)
CREATE TABLE IF NOT EXISTS migration_log (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Migration: Remove company verified field
-- Since only admins can add companies now, the company verification status is no longer needed

-- Remove the index on the verified field
DROP INDEX IF EXISTS idx_companies_verified;

-- Remove the verified column from companies table
ALTER TABLE companies DROP COLUMN IF EXISTS verified;

-- Update the comment on the companies table
COMMENT ON TABLE companies IS 'Companies table - all companies are managed by admins, no verification status needed';

-- Add migration log entry
INSERT INTO migration_log (migration_name, description, applied_at) 
VALUES (
  '008-remove-company-verified-field', 
  'Removed verified field from companies table since only admins can add companies',
  NOW()
) ON CONFLICT (migration_name) DO NOTHING;

-- Create migration_log table if it doesn't exist (safety check)
CREATE TABLE IF NOT EXISTS migration_log (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

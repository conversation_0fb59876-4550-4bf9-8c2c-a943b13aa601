-- Migration: Remove benefits category check constraint
-- This migration removes the check constraint that restricted the category column 
-- to hardcoded values, allowing dynamic category names

-- Drop the constraint that restricted category values
ALTER TABLE benefits DROP CONSTRAINT IF EXISTS benefits_category_check;

-- Add a comment to document the change
COMMENT ON COLUMN benefits.category IS 'Legacy string category field - now allows any value to match benefit_categories.name. Use category_id for foreign key relationships.';

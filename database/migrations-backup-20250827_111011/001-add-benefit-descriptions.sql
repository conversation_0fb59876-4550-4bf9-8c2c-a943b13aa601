-- Migration: Add description field to benefits table
-- This migration adds a description field to store detailed information about benefits,
-- particularly useful for German-specific benefits with context explanations.

-- Add description column to benefits table
ALTER TABLE benefits ADD COLUMN description TEXT;

-- Add index for description field for search functionality
CREATE INDEX idx_benefits_description ON benefits USING gin(to_tsvector('english', description));

-- Update existing German benefits with descriptions
UPDATE benefits SET description = 'Mandatory health insurance coverage as required by German law' 
WHERE name = 'Statutory Health Insurance (Gesetzliche Krankenversicherung)';

UPDATE benefits SET description = 'Enhanced private health insurance coverage' 
WHERE name = 'Private Health Insurance (Private Krankenversicherung)';

UPDATE benefits SET description = 'Additional dental coverage beyond statutory insurance' 
WHERE name = 'Dental Insurance Plus (Zahnzusatzversicherung)';

UPDATE benefits SET description = 'Regular preventive health examinations provided by company' 
WHERE name = 'Company Health Check-ups (Betriebsärztliche Untersuchungen)';

UPDATE benefits SET description = 'Employee assistance programs and mental health counseling' 
WHERE name = 'Mental Health Support (Psychologische Betreuung)';

UPDATE benefits SET description = 'Workplace health and safety medical services' 
WHERE name = 'Occupational Health Services (Arbeitsmedizin)';

UPDATE benefits SET description = '13th month salary typically paid in November/December' 
WHERE name = 'Christmas Bonus (Weihnachtsgeld)';

UPDATE benefits SET description = 'Additional payment for vacation expenses' 
WHERE name = 'Holiday Bonus (Urlaubsgeld)';

UPDATE benefits SET description = 'Employer-sponsored retirement savings plan' 
WHERE name = 'Company Pension Scheme (Betriebliche Altersvorsorge)';

UPDATE benefits SET description = 'Employer contributions to employee savings plans' 
WHERE name = 'Capital-Forming Benefits (Vermögenswirksame Leistungen)';

UPDATE benefits SET description = 'Share in company profits distributed to employees' 
WHERE name = 'Profit Sharing (Gewinnbeteiligung)';

UPDATE benefits SET description = 'Opportunity to purchase company shares at discounted rates' 
WHERE name = 'Employee Stock Purchase Plan (Mitarbeiterbeteiligung)';

UPDATE benefits SET description = 'Minimum 24-30 working days of paid vacation per year' 
WHERE name = 'Statutory Vacation (Gesetzlicher Urlaub)';

UPDATE benefits SET description = 'Additional vacation days beyond statutory minimum' 
WHERE name = 'Extended Vacation Days (Zusätzliche Urlaubstage)';

UPDATE benefits SET description = 'Continued salary payment during illness up to 6 weeks' 
WHERE name = 'Sick Leave (Lohnfortzahlung im Krankheitsfall)';

UPDATE benefits SET description = 'Up to 3 years of parental leave with job protection' 
WHERE name = 'Parental Leave (Elternzeit)';

UPDATE benefits SET description = 'Extended leave for personal development or travel' 
WHERE name = 'Sabbatical Leave (Sabbatjahr)';

UPDATE benefits SET description = 'Additional days off for special occasions (wedding, moving, etc.)' 
WHERE name = 'Special Leave Days (Sonderurlaub)';

UPDATE benefits SET description = 'Access to fitness studios, pools, and sports activities across Germany' 
WHERE name = 'Urban Sports Club Membership';

UPDATE benefits SET description = 'Digital fitness and wellness platform with gym access' 
WHERE name = 'EGYM Wellpass';

UPDATE benefits SET description = 'Corporate fitness program with nationwide gym access' 
WHERE name = 'Hansefit Membership';

UPDATE benefits SET description = 'Organized company sports activities and teams' 
WHERE name = 'Company Sports Teams (Betriebssport)';

UPDATE benefits SET description = 'On-site massage therapy services' 
WHERE name = 'Massage Services (Massagen am Arbeitsplatz)';

UPDATE benefits SET description = 'Ergonomic furniture and equipment for healthy working' 
WHERE name = 'Ergonomic Workplace Setup (Ergonomischer Arbeitsplatz)';

UPDATE benefits SET description = 'Flexible start and end times within core hours' 
WHERE name = 'Flexible Working Hours (Gleitzeit)';

UPDATE benefits SET description = 'Option to work from home or other remote locations' 
WHERE name = 'Remote Work (Homeoffice)';

UPDATE benefits SET description = 'Reduced working hours with proportional salary' 
WHERE name = 'Part-Time Work Options (Teilzeitarbeit)';

UPDATE benefits SET description = 'Sharing one full-time position between two employees' 
WHERE name = 'Job Sharing (Arbeitsplatz-Teilung)';

UPDATE benefits SET description = 'Working full hours in fewer days per week' 
WHERE name = 'Compressed Work Week (Verdichtete Arbeitszeit)';

UPDATE benefits SET description = 'Annual budget for professional development and training' 
WHERE name = 'Training Budget (Weiterbildungsbudget)';

UPDATE benefits SET description = 'Company-sponsored language courses and learning programs' 
WHERE name = 'Language Learning Support (Sprachkurse)';

UPDATE benefits SET description = 'Paid attendance at industry conferences and events' 
WHERE name = 'Conference Attendance (Konferenz-Teilnahme)';

UPDATE benefits SET description = 'Company-organized training and skill development programs' 
WHERE name = 'Internal Training Programs (Interne Schulungen)';

UPDATE benefits SET description = 'Structured mentoring and coaching programs' 
WHERE name = 'Mentoring Programs (Mentoring-Programme)';

UPDATE benefits SET description = 'Paid leave for educational purposes and skill development' 
WHERE name = 'Study Leave (Bildungsurlaub)';

UPDATE benefits SET description = 'Subsidized public transportation pass' 
WHERE name = 'Job Ticket (Jobticket)';

UPDATE benefits SET description = 'Company vehicle for business and private use' 
WHERE name = 'Company Car (Dienstwagen)';

UPDATE benefits SET description = 'Leased bicycles or e-bikes for commuting' 
WHERE name = 'Bike Leasing (Dienstfahrrad)';

UPDATE benefits SET description = 'Subsidized or free parking at workplace' 
WHERE name = 'Parking Allowance (Parkplatz-Zuschuss)';

UPDATE benefits SET description = 'Reimbursement for business travel expenses' 
WHERE name = 'Travel Allowance (Reisekostenzuschuss)';

UPDATE benefits SET description = 'Vouchers for meals at restaurants or company cafeteria' 
WHERE name = 'Meal Vouchers (Essensgutscheine)';

UPDATE benefits SET description = 'Subsidized meals in company cafeteria' 
WHERE name = 'Company Cafeteria (Betriebskantine)';

UPDATE benefits SET description = 'Complimentary beverages and snacks at workplace' 
WHERE name = 'Free Coffee & Snacks (Kostenlose Verpflegung)';

UPDATE benefits SET description = 'Regular catered meals or lunch delivery service' 
WHERE name = 'Catered Meals (Catering-Service)';

UPDATE benefits SET description = 'Company daycare or childcare subsidies' 
WHERE name = 'Childcare Support (Kinderbetreuung)';

UPDATE benefits SET description = 'Company events and activities for employees and families' 
WHERE name = 'Family Events (Familienfeste)';

UPDATE benefits SET description = 'Emergency childcare services for unexpected situations' 
WHERE name = 'Emergency Childcare (Notfall-Kinderbetreuung)';

UPDATE benefits SET description = 'Company mobile phone or monthly allowance' 
WHERE name = 'Mobile Phone Allowance (Handy-Zuschuss)';

UPDATE benefits SET description = 'Equipment and furniture for home office setup' 
WHERE name = 'Home Office Equipment (Homeoffice-Ausstattung)';

UPDATE benefits SET description = 'Reimbursement for home internet costs' 
WHERE name = 'Internet Allowance (Internet-Zuschuss)';

-- Add descriptions for existing benefits that didn't have them
UPDATE benefits SET description = 'Comprehensive health insurance coverage' 
WHERE name = 'Health Insurance' AND description IS NULL;

UPDATE benefits SET description = 'Dental care insurance coverage' 
WHERE name = 'Dental Insurance' AND description IS NULL;

UPDATE benefits SET description = 'Vision and eye care insurance coverage' 
WHERE name = 'Vision Insurance' AND description IS NULL;

UPDATE benefits SET description = 'Mental health support and counseling services' 
WHERE name = 'Mental Health Support' AND description IS NULL;

UPDATE benefits SET description = 'Access to gym facilities and fitness programs' 
WHERE name = 'Gym Membership' AND description IS NULL;

UPDATE benefits SET description = 'Wellness and sports program membership' 
WHERE name = 'Wellpass' AND description IS NULL;

UPDATE benefits SET description = 'Flexible working hours and schedule' 
WHERE name = 'Flexible Working Hours' AND description IS NULL;

UPDATE benefits SET description = 'Remote work and home office options' 
WHERE name = 'Remote Work' AND description IS NULL;

UPDATE benefits SET description = 'Extended leave for personal development' 
WHERE name = 'Sabbatical Leave' AND description IS NULL;

UPDATE benefits SET description = 'Unlimited paid time off policy' 
WHERE name = 'Unlimited PTO' AND description IS NULL;

UPDATE benefits SET description = 'Parental leave for new parents' 
WHERE name = 'Parental Leave' AND description IS NULL;

UPDATE benefits SET description = 'Employee stock options and equity' 
WHERE name = 'Stock Options' AND description IS NULL;

UPDATE benefits SET description = 'Company retirement and pension plan' 
WHERE name = 'Retirement Plan' AND description IS NULL;

UPDATE benefits SET description = 'Budget for learning and professional development' 
WHERE name = 'Learning Budget' AND description IS NULL;

UPDATE benefits SET description = 'Paid attendance at industry conferences' 
WHERE name = 'Conference Attendance' AND description IS NULL;

UPDATE benefits SET description = 'Free meals and lunch at workplace' 
WHERE name = 'Free Lunch' AND description IS NULL;

UPDATE benefits SET description = 'Company car for business and personal use' 
WHERE name = 'Company Car' AND description IS NULL;

UPDATE benefits SET description = 'Bike-to-work scheme and cycling benefits' 
WHERE name = 'Bike to Work Scheme' AND description IS NULL;

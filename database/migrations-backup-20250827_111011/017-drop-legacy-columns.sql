-- Migration: Drop legacy columns after benefit category refactoring
-- This migration removes columns that are no longer needed after implementing
-- the new benefit category system with proper foreign key relationships

-- Drop the legacy category string column from benefits table
-- This column is redundant since we now use category_id foreign key
ALTER TABLE benefits DROP COLUMN IF EXISTS category;

-- Drop the is_system column from benefit_categories table  
-- This column is no longer needed since all categories are now editable
ALTER TABLE benefit_categories DROP COLUMN IF EXISTS is_system;

-- Drop related indexes that are no longer needed
DROP INDEX IF EXISTS idx_benefits_category;

-- Add comments to document the cleanup
COMMENT ON TABLE benefits IS 'Benefits table - now uses category_id foreign key for category relationships';
COMMENT ON TABLE benefit_categories IS 'Benefit categories table - all categories are now fully editable and manageable';

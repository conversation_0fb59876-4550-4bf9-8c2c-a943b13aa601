-- Migration: Add custom benefit categories system
-- This migration creates a benefit_categories table and updates the benefits table
-- to use foreign key references instead of hardcoded category strings

-- Create benefit_categories table
CREATE TABLE benefit_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_system BOOLEAN DEFAULT FALSE, -- System categories cannot be deleted
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_benefit_categories_name ON benefit_categories(name);
CREATE INDEX idx_benefit_categories_sort_order ON benefit_categories(sort_order);
CREATE INDEX idx_benefit_categories_is_active ON benefit_categories(is_active);

-- Insert default system categories (matching existing hardcoded categories)
INSERT INTO benefit_categories (name, display_name, description, icon, sort_order, is_system) VALUES
('health', 'Health & Medical', 'Health insurance, medical benefits, and healthcare-related perks', '🏥', 1, true),
('time_off', 'Time Off', 'Vacation days, sick leave, parental leave, and other time-off benefits', '🏖️', 2, true),
('financial', 'Financial', 'Retirement plans, stock options, bonuses, and financial benefits', '💰', 3, true),
('development', 'Development', 'Learning budgets, training programs, conference attendance, and professional development', '📚', 4, true),
('wellness', 'Wellness', 'Gym memberships, wellness programs, mental health support, and fitness benefits', '💪', 5, true),
('work_life', 'Work-Life Balance', 'Flexible hours, remote work, sabbaticals, and work-life balance benefits', '⚖️', 6, true),
('other', 'Other', 'Miscellaneous benefits that don\'t fit into other categories', '🎁', 7, true);

-- Add new category_id column to benefits table
ALTER TABLE benefits ADD COLUMN category_id UUID REFERENCES benefit_categories(id);

-- Update existing benefits to use the new category_id
UPDATE benefits SET category_id = (
    SELECT id FROM benefit_categories WHERE name = benefits.category
);

-- Make category_id NOT NULL after data migration
ALTER TABLE benefits ALTER COLUMN category_id SET NOT NULL;

-- Add index for the new foreign key
CREATE INDEX idx_benefits_category_id ON benefits(category_id);

-- Add trigger to update updated_at timestamp on benefit_categories
CREATE OR REPLACE FUNCTION update_benefit_categories_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_benefit_categories_updated_at
    BEFORE UPDATE ON benefit_categories
    FOR EACH ROW
    EXECUTE FUNCTION update_benefit_categories_updated_at();

-- Add constraint to prevent deletion of categories with benefits
-- This will be enforced at the application level for better error handling

-- Log this migration
INSERT INTO migration_log (migration_name, description) 
VALUES ('010-add-custom-benefit-categories', 'Add custom benefit categories system with benefit_categories table and foreign key relationships');

-- Create migration_log table if it doesn't exist
CREATE TABLE IF NOT EXISTS migration_log (
    id SERIAL PRIMARY KEY,
    migration_name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- BenefitLens Database Seed Data
-- This file contains sample data for development and testing
-- Updated to match current schema structure and actual database backup

-- Ensure benefit categories exist before inserting benefits
INSERT INTO benefit_categories (name, display_name, description, icon, sort_order) VALUES
('health', 'Health & Medical', 'Health insurance, medical benefits, and healthcare-related perks', '🏥', 1),
('time_off', 'Time Off', 'Vacation days, sick leave, parental leave, and other time-off benefits', '🏖️', 2),
('financial', 'Financial', 'Retirement plans, stock options, bonuses, and financial benefits', '💰', 3),
('development', 'Development', 'Learning budgets, training programs, conference attendance, and professional development', '📚', 4),
('wellness', 'Wellness', 'Gym memberships, wellness programs, mental health support, and fitness benefits', '💪', 5),
('work_life', 'Work-Life Balance', 'Flexible hours, remote work, sabbaticals, and work-life balance benefits', '⚖️', 6),
('fitness', 'Fitness', 'Gym memberships, sports programs, and fitness-related benefits', '🏃‍♂️', 7),
('other', 'Other', 'Miscellaneous benefits that don''t fit into other categories', '🎁', 8)
ON CONFLICT (name) DO NOTHING;

-- Clear existing data to avoid conflicts
DELETE FROM company_benefits;
DELETE FROM user_benefit_rankings;
DELETE FROM saved_companies;
DELETE FROM activity_log;
DELETE FROM company_locations;
DELETE FROM companies;
DELETE FROM benefits;
DELETE FROM users;

-- Insert sample benefits with proper category_id references matching backup data
INSERT INTO benefits (name, icon, description, category_id) VALUES
-- Financial benefits
('Corporate Benefits', '🛍️', 'Corporate benefits and perks', (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Stock Options', '📈', 'Employee stock option program', (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Retirement Plan', '💰', 'Company retirement savings plan', (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Holiday Bonus (Urlaubsgeld)', '🏖️', 'Additional payment for vacation expenses', (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Company Pension Scheme (Betriebliche Altersvorsorge)', '💰', 'Employer-sponsored pension plan', (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Profit Sharing (Gewinnbeteiligung)', '📈', 'Share in company profits distributed to employees', (SELECT id FROM benefit_categories WHERE name = 'financial')),
('Employee Stock Purchase Plan (Mitarbeiterbeteiligung)', '📊', 'Discounted company stock purchase program', (SELECT id FROM benefit_categories WHERE name = 'financial')),

-- Other benefits (matching backup data)
('Gym Membership - ClassPass', '🏋️‍♀️', 'Access to ClassPass gym membership', (SELECT id FROM benefit_categories WHERE name = 'other')),
('Free Lunch', '🍽️', 'Complimentary meals provided by company', (SELECT id FROM benefit_categories WHERE name = 'other')),
('Pet-Friendly Office', '🐕', 'Pets allowed in the workplace', (SELECT id FROM benefit_categories WHERE name = 'other')),

-- Time off benefits
('Unlimited PTO', '🏖️', 'Unlimited paid time off policy', (SELECT id FROM benefit_categories WHERE name = 'time_off')),
('Extended Vacation Days (Zusätzliche Urlaubstage)', '🌴', 'Additional vacation days beyond legal minimum', (SELECT id FROM benefit_categories WHERE name = 'time_off')),
('Parental Leave (Elternzeit)', '👶', 'Enhanced parental leave beyond legal requirements', (SELECT id FROM benefit_categories WHERE name = 'time_off')),
('Sabbatical Leave (Sabbatjahr)', '🎒', 'Extended leave for personal development or rest', (SELECT id FROM benefit_categories WHERE name = 'time_off')),
('Special Leave Days (Sonderurlaub)', '📅', 'Special leave days for personal matters', (SELECT id FROM benefit_categories WHERE name = 'time_off')),

-- Development benefits
('Learning Budget', '📚', 'Annual budget for courses, conferences, and training', (SELECT id FROM benefit_categories WHERE name = 'development')),
('Training Budget (Weiterbildungsbudget)', '📚', 'Budget for professional development and training', (SELECT id FROM benefit_categories WHERE name = 'development')),
('Language Learning Support (Sprachkurse)', '🗣️', 'Support for language learning courses', (SELECT id FROM benefit_categories WHERE name = 'development')),
('Conference Attendance (Konferenz-Teilnahme)', '🎤', 'Paid attendance at industry conferences and events', (SELECT id FROM benefit_categories WHERE name = 'development')),
('Internal Training Programs (Interne Schulungen)', '🎓', 'Company-provided internal training programs', (SELECT id FROM benefit_categories WHERE name = 'development')),
('Mentoring Programs (Mentoring-Programme)', '👨‍🏫', 'Structured mentorship and career development program', (SELECT id FROM benefit_categories WHERE name = 'development')),

-- Health benefits
('Dental Insurance Plus (Zahnzusatzversicherung)', '🦷', 'Additional dental insurance coverage beyond statutory requirements', (SELECT id FROM benefit_categories WHERE name = 'health')),
('Company Health Check-ups (Betriebsärztliche Untersuchungen)', '🩺', 'Regular health screenings and medical check-ups provided by company', (SELECT id FROM benefit_categories WHERE name = 'health')),
('Mental Health Support (Psychologische Betreuung)', '🧠', 'Access to mental health counseling and psychological support services', (SELECT id FROM benefit_categories WHERE name = 'health')),
('Occupational Health Services (Arbeitsmedizin)', '⚕️', 'Workplace health and safety medical services', (SELECT id FROM benefit_categories WHERE name = 'health')),

-- Work-life balance benefits
('Flexible Working Hours (Gleitzeit)', '⏰', 'Flexible working hours and core time', (SELECT id FROM benefit_categories WHERE name = 'work_life')),
('Remote Work (Homeoffice)', '🏠', 'Flexible remote work options', (SELECT id FROM benefit_categories WHERE name = 'work_life')),
('Part-Time Work Options (Teilzeitarbeit)', '⏱️', 'Flexible part-time work arrangements', (SELECT id FROM benefit_categories WHERE name = 'work_life')),
('Job Sharing (Arbeitsplatz-Teilung)', '👥', 'Job sharing arrangements with colleagues', (SELECT id FROM benefit_categories WHERE name = 'work_life')),

-- Fitness benefits
('Gym Membership (Fitnessstudio-Mitgliedschaft)', '💪', 'Company-sponsored gym membership', (SELECT id FROM benefit_categories WHERE name = 'fitness')),
('Sports Programs (Sportprogramme)', '🏃‍♂️', 'Company sports and fitness programs', (SELECT id FROM benefit_categories WHERE name = 'fitness')),
('Wellness Programs (Wellness-Programme)', '🌱', 'Comprehensive wellness and health programs', (SELECT id FROM benefit_categories WHERE name = 'fitness')),
('Bike Leasing (Dienstradleasing)', '🚲', 'Company bike leasing program for commuting', (SELECT id FROM benefit_categories WHERE name = 'fitness'));

-- Insert sample companies (matching backup data structure)
INSERT INTO companies (name, size, industry, description, domain, career_url, website, founded_year) VALUES
('Test Corp', 'medium', 'Technology', 'Test technology company for development', 'testcorp.com', 'https://careers.testcorp.com', 'https://www.testcorp.com', 2020),
('Test Startup', 'startup', 'Technology', 'Test startup company', 'teststartup.com', 'https://careers.teststartup.com', 'https://www.teststartup.com', 2022),
('Test Industries', 'large', 'Manufacturing', 'Test manufacturing company', 'testindustries.com', 'https://careers.testindustries.com', 'https://www.testindustries.com', 2015),
('SAP', 'enterprise', 'Technology', 'Global leader in enterprise software solutions', 'sap.com', 'https://jobs.sap.com', 'https://www.sap.com', 1972),
('Deutsche Bank', 'enterprise', 'Financial Services', 'Leading global investment bank and financial services company', 'db.com', 'https://careers.db.com', 'https://www.db.com', 1870),
('Accenture', 'enterprise', 'Consulting', 'Global professional services company with leading capabilities in digital, cloud and security', 'accenture.com', 'https://www.accenture.com/careers', 'https://www.accenture.com', 1989),
('Siemens', 'enterprise', 'Technology', 'Global technology company focused on industry, infrastructure, transport, and healthcare', 'siemens.com', 'https://jobs.siemens.com', 'https://www.siemens.com', 1847),
('Spotify', 'large', 'Technology', 'Audio streaming and media services provider', 'spotify.com', 'https://www.lifeatspotify.com', 'https://www.spotify.com', 2006),
('N26', 'medium', 'Financial Services', 'Digital bank offering mobile banking solutions', 'n26.com', 'https://n26.com/en/careers', 'https://n26.com', 2013),
('Zalando', 'large', 'E-commerce', 'European online fashion and lifestyle platform', 'zalando.com', 'https://jobs.zalando.com', 'https://www.zalando.com', 2008);

-- Insert company locations (new table for multi-location support)
INSERT INTO company_locations (company_id, location_raw, location_normalized, city, country, country_code, is_primary, is_headquarters, location_type) VALUES
-- Test companies locations
((SELECT id FROM companies WHERE name = 'Test Corp'), 'Berlin, Germany', 'Berlin, Germany', 'Berlin', 'Germany', 'DE', true, true, 'headquarters'),
((SELECT id FROM companies WHERE name = 'Test Startup'), 'Munich, Germany', 'Munich, Germany', 'Munich', 'Germany', 'DE', true, true, 'headquarters'),
((SELECT id FROM companies WHERE name = 'Test Industries'), 'Frankfurt, Germany', 'Frankfurt, Germany', 'Frankfurt', 'Germany', 'DE', true, true, 'headquarters'),

-- SAP locations
((SELECT id FROM companies WHERE name = 'SAP'), 'Walldorf, Germany', 'Walldorf, Germany', 'Walldorf', 'Germany', 'DE', true, true, 'headquarters'),
((SELECT id FROM companies WHERE name = 'SAP'), 'Berlin, Germany', 'Berlin, Germany', 'Berlin', 'Germany', 'DE', false, false, 'office'),

-- Deutsche Bank locations
((SELECT id FROM companies WHERE name = 'Deutsche Bank'), 'Frankfurt, Germany', 'Frankfurt, Germany', 'Frankfurt', 'Germany', 'DE', true, true, 'headquarters'),

-- Accenture locations
((SELECT id FROM companies WHERE name = 'Accenture'), 'Dublin, Ireland', 'Dublin, Ireland', 'Dublin', 'Ireland', 'IE', true, true, 'headquarters'),
((SELECT id FROM companies WHERE name = 'Accenture'), 'Munich, Germany', 'Munich, Germany', 'Munich', 'Germany', 'DE', false, false, 'office'),

-- Siemens locations
((SELECT id FROM companies WHERE name = 'Siemens'), 'Munich, Germany', 'Munich, Germany', 'Munich', 'Germany', 'DE', true, true, 'headquarters'),
((SELECT id FROM companies WHERE name = 'Siemens'), 'Berlin, Germany', 'Berlin, Germany', 'Berlin', 'Germany', 'DE', false, false, 'office'),

-- Spotify locations
((SELECT id FROM companies WHERE name = 'Spotify'), 'Stockholm, Sweden', 'Stockholm, Sweden', 'Stockholm', 'Sweden', 'SE', true, true, 'headquarters'),
((SELECT id FROM companies WHERE name = 'Spotify'), 'Berlin, Germany', 'Berlin, Germany', 'Berlin', 'Germany', 'DE', false, false, 'office'),

-- N26 locations
((SELECT id FROM companies WHERE name = 'N26'), 'Berlin, Germany', 'Berlin, Germany', 'Berlin', 'Germany', 'DE', true, true, 'headquarters'),

-- Zalando locations
((SELECT id FROM companies WHERE name = 'Zalando'), 'Berlin, Germany', 'Berlin, Germany', 'Berlin', 'Germany', 'DE', true, true, 'headquarters');

-- Insert sample company benefits (linking companies with benefits)
-- This creates realistic benefit associations for the sample companies
INSERT INTO company_benefits (company_id, benefit_id, is_verified)
SELECT
    c.id,
    b.id,
    true
FROM companies c
CROSS JOIN benefits b
WHERE
    -- Test companies (basic benefits for testing)
    (c.name = 'Test Corp' AND b.name IN ('Remote Work (Homeoffice)', 'Learning Budget', 'Stock Options', 'Mental Health Support (Psychologische Betreuung)', 'Free Lunch')) OR
    (c.name = 'Test Startup' AND b.name IN ('Flexible Working Hours (Gleitzeit)', 'Stock Options', 'Free Lunch')) OR
    (c.name = 'Test Industries' AND b.name IN ('Company Pension Scheme (Betriebliche Altersvorsorge)', 'Company Health Check-ups (Betriebsärztliche Untersuchungen)', 'Mental Health Support (Psychologische Betreuung)')) OR

    -- Large tech companies (comprehensive benefits)
    (c.name = 'SAP' AND b.name IN ('Gym Membership (Fitnessstudio-Mitgliedschaft)', 'Learning Budget', 'Stock Options', 'Mental Health Support (Psychologische Betreuung)', 'Remote Work (Homeoffice)', 'Flexible Working Hours (Gleitzeit)', 'Company Pension Scheme (Betriebliche Altersvorsorge)', 'Bike Leasing (Dienstradleasing)')) OR
    (c.name = 'Siemens' AND b.name IN ('Learning Budget', 'Stock Options', 'Gym Membership (Fitnessstudio-Mitgliedschaft)', 'Mental Health Support (Psychologische Betreuung)', 'Occupational Health Services (Arbeitsmedizin)', 'Company Pension Scheme (Betriebliche Altersvorsorge)', 'Flexible Working Hours (Gleitzeit)', 'Conference Attendance (Konferenz-Teilnahme)')) OR

    -- Financial services (traditional benefits)
    (c.name = 'Deutsche Bank' AND b.name IN ('Retirement Plan', 'Stock Options', 'Gym Membership (Fitnessstudio-Mitgliedschaft)', 'Free Lunch', 'Company Pension Scheme (Betriebliche Altersvorsorge)', 'Dental Insurance Plus (Zahnzusatzversicherung)')) OR

    -- Consulting firms (learning focused)
    (c.name = 'Accenture' AND b.name IN ('Learning Budget', 'Mental Health Support (Psychologische Betreuung)', 'Gym Membership (Fitnessstudio-Mitgliedschaft)', 'Profit Sharing (Gewinnbeteiligung)', 'Conference Attendance (Konferenz-Teilnahme)', 'Remote Work (Homeoffice)', 'Flexible Working Hours (Gleitzeit)')) OR

    -- Modern tech companies (progressive benefits)
    (c.name = 'Spotify' AND b.name IN ('Remote Work (Homeoffice)', 'Flexible Working Hours (Gleitzeit)', 'Unlimited PTO', 'Learning Budget', 'Mental Health Support (Psychologische Betreuung)', 'Gym Membership (Fitnessstudio-Mitgliedschaft)', 'Free Lunch', 'Stock Options', 'Wellness Programs (Wellness-Programme)')) OR
    (c.name = 'N26' AND b.name IN ('Remote Work (Homeoffice)', 'Flexible Working Hours (Gleitzeit)', 'Learning Budget', 'Stock Options', 'Mental Health Support (Psychologische Betreuung)', 'Gym Membership (Fitnessstudio-Mitgliedschaft)', 'Free Lunch', 'Bike Leasing (Dienstradleasing)')) OR
    (c.name = 'Zalando' AND b.name IN ('Remote Work (Homeoffice)', 'Flexible Working Hours (Gleitzeit)', 'Learning Budget', 'Stock Options', 'Gym Membership (Fitnessstudio-Mitgliedschaft)', 'Free Lunch', 'Mental Health Support (Psychologische Betreuung)', 'Wellness Programs (Wellness-Programme)', 'Pet-Friendly Office'));

-- Insert sample admin user (for testing purposes)
INSERT INTO users (email, first_name, last_name, email_verified, role, payment_status) VALUES
('<EMAIL>', 'Admin', 'User', true, 'admin', 'paying'),
('<EMAIL>', 'Admin', 'User', true, 'admin', 'free');

-- Insert sample regular users with company associations
INSERT INTO users (email, first_name, last_name, email_verified, role, payment_status, company_id) VALUES
-- Test company employees
('<EMAIL>', 'John', 'Doe', true, 'user', 'paying', (SELECT id FROM companies WHERE name = 'Test Corp')),
('<EMAIL>', 'Jane', 'Smith', true, 'user', 'free', (SELECT id FROM companies WHERE name = 'Test Corp')),
('<EMAIL>', 'Bob', 'Johnson', true, 'user', 'paying', (SELECT id FROM companies WHERE name = 'Test Startup')),

-- SAP employees
('<EMAIL>', 'Maria', 'Garcia', true, 'user', 'free', (SELECT id FROM companies WHERE name = 'SAP')),

-- Deutsche Bank employees
('<EMAIL>', 'Thomas', 'Mueller', true, 'user', 'paying', (SELECT id FROM companies WHERE name = 'Deutsche Bank')),

-- Tech company employees
('<EMAIL>', 'Alex', 'Chen', true, 'user', 'paying', (SELECT id FROM companies WHERE name = 'Spotify')),
('<EMAIL>', 'Lisa', 'Weber', true, 'user', 'paying', (SELECT id FROM companies WHERE name = 'N26')),
('<EMAIL>', 'David', 'Brown', true, 'user', 'free', (SELECT id FROM companies WHERE name = 'Zalando')),

-- Users without company association (job seekers)
('<EMAIL>', 'Robert', 'Wilson', true, 'user', 'free', NULL),
('<EMAIL>', 'Sophie', 'Taylor', true, 'user', 'paying', NULL);

-- Insert sample user benefit rankings (for testing analytics)
INSERT INTO user_benefit_rankings (user_id, benefit_id, ranking) VALUES
-- John Doe's rankings (Test Corp employee)
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Remote Work (Homeoffice)'), 1),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Learning Budget'), 2),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Stock Options'), 3),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Mental Health Support (Psychologische Betreuung)'), 4),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Free Lunch'), 5),

-- Jane Smith's rankings (Test Corp employee)
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Company Pension Scheme (Betriebliche Altersvorsorge)'), 1),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Flexible Working Hours (Gleitzeit)'), 2),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Free Lunch'), 3),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Learning Budget'), 4),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Mental Health Support (Psychologische Betreuung)'), 5),

-- Alex Chen's rankings (Spotify employee)
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Unlimited PTO'), 1),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Remote Work (Homeoffice)'), 2),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Stock Options'), 3),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Learning Budget'), 4),
((SELECT id FROM users WHERE email = '<EMAIL>'), (SELECT id FROM benefits WHERE name = 'Wellness Programs (Wellness-Programme)'), 5);

-- Insert some saved companies (user bookmarks)
INSERT INTO saved_companies (user_id, company_id) VALUES
-- Job seekers saving companies they're interested in
('<EMAIL>', (SELECT id FROM companies WHERE name = 'Test Corp')),
('<EMAIL>', (SELECT id FROM companies WHERE name = 'Spotify')),
('<EMAIL>', (SELECT id FROM companies WHERE name = 'N26')),
('<EMAIL>', (SELECT id FROM companies WHERE name = 'Zalando')),
('<EMAIL>', (SELECT id FROM companies WHERE name = 'SAP'));

-- Insert some sample activity log entries
INSERT INTO activity_log (event_type, event_description, user_email, user_name, company_name, benefit_name, created_at) VALUES
('user_registered', 'New user registered', '<EMAIL>', 'John Doe', NULL, NULL, NOW() - INTERVAL '7 days'),
('user_registered', 'New user registered', '<EMAIL>', 'Jane Smith', NULL, NULL, NOW() - INTERVAL '5 days'),
('benefit_verified', 'User verified company benefit', '<EMAIL>', 'John Doe', 'Test Corp', 'Remote Work (Homeoffice)', NOW() - INTERVAL '3 days'),
('benefit_verified', 'User verified company benefit', '<EMAIL>', 'Jane Smith', 'Test Corp', 'Free Lunch', NOW() - INTERVAL '2 days'),
('user_registered', 'New user registered', '<EMAIL>', 'Alex Chen', NULL, NULL, NOW() - INTERVAL '1 day');

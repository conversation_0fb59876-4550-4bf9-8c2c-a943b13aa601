version: '3.8'

services:
  # PostgreSQL Database for E2E tests
  postgres-e2e:
    image: postgres:15
    container_name: benefitlens-postgres-e2e
    environment:
      POSTGRES_DB: benefitlens
      POSTGRES_USER: benefitlens_user
      POSTGRES_PASSWORD: benefitlens_password
    ports:
      - "5433:5432"  # Different port to avoid conflicts with dev DB
    volumes:
      - postgres_e2e_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U benefitlens_user -d benefitlens"]
      interval: 10s
      timeout: 5s
      retries: 10
    networks:
      - e2e-network

  # E2E Test Runner - mirrors CI environment exactly
  e2e-runner:
    build:
      context: .
      dockerfile: Dockerfile.e2e
    container_name: benefitlens-e2e-runner
    environment:
      # Database connection
      DATABASE_URL: ********************************************************************/benefitlens
      PGPASSWORD: benefitlens_password
      
      # App configuration
      E2E_BASE_URL: http://app-e2e:3000
      NODE_ENV: test
      
      # Authentication secrets
      NEXTAUTH_SECRET: e2e-test-secret-for-ci-testing
      NEXTAUTH_URL: http://app-e2e:3000
      SESSION_SECRET: e2e-session-secret-for-ci-testing
      
      # E2E specific settings
      DISABLE_RATE_LIMITING: "true"
      CI: "true"
      
      # Playwright settings
      PLAYWRIGHT_BROWSERS_PATH: /ms-playwright
      
      # Test file parameter (can be overridden)
      TEST_FILE: ${TEST_FILE:-src/__tests__/e2e/essential-tests.spec.ts}
      TEST_CONFIG: ${TEST_CONFIG:-playwright-conservative.config.ts}
      TEST_PROJECT: ${TEST_PROJECT:-chromium}
      
    volumes:
      - .:/app
      - /app/node_modules
      - playwright_cache:/ms-playwright
    working_dir: /app
    depends_on:
      postgres-e2e:
        condition: service_healthy
      app-e2e:
        condition: service_healthy
    networks:
      - e2e-network
    command: >
      sh -c "
        echo '🎭 Starting E2E test runner...' &&
        echo 'Test file: ${TEST_FILE}' &&
        echo 'Test config: ${TEST_CONFIG}' &&
        echo 'Test project: ${TEST_PROJECT}' &&
        
        # Wait for app to be ready
        echo '⏳ Waiting for app to be ready...' &&
        timeout 60 sh -c 'until curl -f http://app-e2e:3000/api/health; do sleep 2; done' &&
        
        # Run database setup
        echo '🗄️ Setting up E2E database...' &&
        npm run db:setup:e2e &&
        
        # Install Playwright browsers if needed
        echo '🎭 Installing Playwright browsers...' &&
        npx playwright install --with-deps &&
        
        # Run the specified test
        echo '🧪 Running E2E tests...' &&
        npx playwright test --config=${TEST_CONFIG} --project=${TEST_PROJECT} ${TEST_FILE}
      "

  # Application instance for E2E testing
  app-e2e:
    build:
      context: .
      dockerfile: Dockerfile.e2e-app
    container_name: benefitlens-app-e2e
    environment:
      # Database connection
      DATABASE_URL: ********************************************************************/benefitlens
      
      # App configuration
      NODE_ENV: test
      PORT: 3000
      
      # Authentication secrets
      NEXTAUTH_SECRET: e2e-test-secret-for-ci-testing
      NEXTAUTH_URL: http://app-e2e:3000
      SESSION_SECRET: e2e-session-secret-for-ci-testing
      
      # E2E specific settings
      DISABLE_RATE_LIMITING: "true"
      
    ports:
      - "3001:3000"  # Different port to avoid conflicts with dev app
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    working_dir: /app
    depends_on:
      postgres-e2e:
        condition: service_healthy
    networks:
      - e2e-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 30s
    command: >
      sh -c "
        echo '🚀 Starting E2E app instance...' &&
        
        # Wait for database
        echo '⏳ Waiting for database...' &&
        timeout 60 sh -c 'until pg_isready -h postgres-e2e -p 5432 -U benefitlens_user; do sleep 2; done' &&
        
        # Run database migrations
        echo '🗄️ Running database migrations...' &&
        npm run db:migrate &&
        
        # Build the app
        echo '🔨 Building application...' &&
        npm run build &&
        
        # Start the app
        echo '▶️ Starting application...' &&
        npm start
      "

volumes:
  postgres_e2e_data:
  playwright_cache:

networks:
  e2e-network:
    driver: bridge

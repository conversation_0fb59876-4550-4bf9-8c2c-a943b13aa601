import { defineConfig, devices } from '@playwright/test';

/**
 * Optimized Playwright configuration for fast local development
 * This config uses multiple workers and parallel execution for speed
 */
export default defineConfig({
  testDir: './src/__tests__/e2e',
  /* Run tests in files in parallel */
  fullyParallel: true, // Enable parallel execution for speed
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 2,
  /* Use multiple workers for faster execution */
  workers: process.env.CI ? 2 : 2, // 2 workers on CI, 2 locally for faster execution
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['list'],
    ['html', { outputFolder: 'playwright-report-conservative' }]
  ],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: 'http://localhost:3000',

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',
    
    /* Take screenshot on failure */
    screenshot: 'only-on-failure',

    /* Optimized timeouts for faster execution */
    actionTimeout: 15000, // Reduced from 30s
    navigationTimeout: 15000, // Reduced from 30s
  },

  /* Configure projects for major browsers - only working browsers */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    /* Test against mobile viewports */
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    /* Test against iPad devices */
    {
      name: 'iPad Pro',
      use: { ...devices['iPad Pro'] },
    },
    /* Note: iPhone 14 and MacBook Pro disabled due to missing system dependencies:
     * libicudata.so.66, libicui18n.so.66, libicuuc.so.66, libwebp.so.6, libffi.so.7
     * These can be re-enabled after installing the dependencies with:
     * sudo npx playwright install-deps
     */
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'npm run dev:no-turbo',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    timeout: 120 * 1000,
  },

  /* Global setup and teardown */
  globalSetup: './src/__tests__/e2e/global-setup.ts',
  globalTeardown: './src/__tests__/e2e/global-teardown.ts',

  /* Test timeout - optimized for speed */
  timeout: 60 * 1000, // 1 minute per test (reduced from 2 minutes)

  /* Expect timeout - optimized for speed */
  expect: {
    timeout: 15 * 1000, // 15 seconds for assertions (reduced from 30s)
  },
});

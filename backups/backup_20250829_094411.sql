--
-- PostgreSQL database dump
--

-- Dumped from database version 15.13 (Debian 15.13-1.pgdg120+1)
-- Dumped by pg_dump version 15.13 (Debian 15.13-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: cleanup_expired_cache(); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.cleanup_expired_cache() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cache_store WHERE expires_at < NOW();
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Also cleanup expired CSRF tokens
    DELETE FROM csrf_tokens WHERE expires_at < NOW();
    
    -- Cleanup expired rate limits
    DELETE FROM rate_limits WHERE expires_at < NOW();
    
    RETURN deleted_count;
END;
$$;


ALTER FUNCTION public.cleanup_expired_cache() OWNER TO benefitlens_user;

--
-- Name: cleanup_expired_magic_links(); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.cleanup_expired_magic_links() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    DELETE FROM magic_link_tokens WHERE expires_at < NOW();
    DELETE FROM magic_link_rate_limits WHERE window_start < NOW() - INTERVAL '1 hour';
END;
$$;


ALTER FUNCTION public.cleanup_expired_magic_links() OWNER TO benefitlens_user;

--
-- Name: cleanup_expired_sessions(); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.cleanup_expired_sessions() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions 
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup operation
    INSERT INTO activity_log (
        event_type,
        event_description,
        metadata,
        created_at
    ) VALUES (
        'session_cleanup',
        'Automated cleanup of expired sessions',
        jsonb_build_object('deleted_sessions', deleted_count),
        NOW()
    );
    
    RETURN deleted_count;
END;
$$;


ALTER FUNCTION public.cleanup_expired_sessions() OWNER TO benefitlens_user;

--
-- Name: clear_cache_pattern(character varying); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.clear_cache_pattern(pattern character varying) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cache_store WHERE cache_key LIKE pattern;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;


ALTER FUNCTION public.clear_cache_pattern(pattern character varying) OWNER TO benefitlens_user;

--
-- Name: delete_all_user_sessions(uuid); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.delete_all_user_sessions(target_user_id uuid) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM user_sessions 
    WHERE user_id = target_user_id;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the operation
    INSERT INTO activity_log (
        event_type,
        event_description,
        user_id,
        metadata,
        created_at
    ) VALUES (
        'user_sessions_deleted',
        'All sessions deleted for user',
        target_user_id::VARCHAR,
        jsonb_build_object('deleted_sessions', deleted_count),
        NOW()
    );
    
    RETURN deleted_count;
END;
$$;


ALTER FUNCTION public.delete_all_user_sessions(target_user_id uuid) OWNER TO benefitlens_user;

--
-- Name: delete_cache(character varying); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.delete_cache(key character varying) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM cache_store WHERE cache_key = key;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count > 0;
END;
$$;


ALTER FUNCTION public.delete_cache(key character varying) OWNER TO benefitlens_user;

--
-- Name: get_active_session_stats(); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.get_active_session_stats() RETURNS TABLE(total_sessions integer, expired_sessions integer, active_sessions integer, oldest_session timestamp with time zone, newest_session timestamp with time zone)
    LANGUAGE plpgsql
    AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::INTEGER as total_sessions,
        COUNT(*) FILTER (WHERE expires_at < NOW())::INTEGER as expired_sessions,
        COUNT(*) FILTER (WHERE expires_at >= NOW())::INTEGER as active_sessions,
        MIN(created_at) as oldest_session,
        MAX(created_at) as newest_session
    FROM user_sessions;
END;
$$;


ALTER FUNCTION public.get_active_session_stats() OWNER TO benefitlens_user;

--
-- Name: get_cache(character varying); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.get_cache(key character varying) RETURNS jsonb
    LANGUAGE plpgsql
    AS $$
DECLARE
    result JSONB;
BEGIN
    SELECT cache_value INTO result
    FROM cache_store
    WHERE cache_key = key AND expires_at > NOW();
    
    RETURN result;
END;
$$;


ALTER FUNCTION public.get_cache(key character varying) OWNER TO benefitlens_user;

--
-- Name: FUNCTION get_cache(key character varying); Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON FUNCTION public.get_cache(key character varying) IS 'Returns JSONB data that is automatically parsed by pg library - no additional JSON.parse() needed in JavaScript';


--
-- Name: get_csrf_token(character varying); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.get_csrf_token(session_id character varying) RETURNS character varying
    LANGUAGE plpgsql
    AS $$
DECLARE
    result VARCHAR;
BEGIN
    SELECT token INTO result
    FROM csrf_tokens
    WHERE session_id = session_id AND expires_at > NOW();
    
    RETURN result;
END;
$$;


ALTER FUNCTION public.get_csrf_token(session_id character varying) OWNER TO benefitlens_user;

--
-- Name: get_session_config(character varying); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.get_session_config(config_name character varying) RETURNS text
    LANGUAGE plpgsql
    AS $$
DECLARE
    config_value TEXT;
BEGIN
    SELECT setting_value INTO config_value
    FROM session_config
    WHERE setting_name = config_name;
    
    RETURN config_value;
END;
$$;


ALTER FUNCTION public.get_session_config(config_name character varying) OWNER TO benefitlens_user;

--
-- Name: log_session_activity(character varying, character varying, inet, text); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.log_session_activity(token character varying, activity character varying, ip inet DEFAULT NULL::inet, agent text DEFAULT NULL::text) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO session_activity (session_token, activity_type, ip_address, user_agent)
    VALUES (token, activity, ip, agent);
END;
$$;


ALTER FUNCTION public.log_session_activity(token character varying, activity character varying, ip inet, agent text) OWNER TO benefitlens_user;

--
-- Name: refresh_cache_views(); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.refresh_cache_views() RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    -- Refresh materialized views
    REFRESH MATERIALIZED VIEW CONCURRENTLY companies_with_benefits_cache;
    REFRESH MATERIALIZED VIEW CONCURRENTLY benefits_with_categories_cache;
    
    -- Log the refresh
    INSERT INTO activity_log (
        event_type,
        event_description,
        created_at
    ) VALUES (
        'cache_refresh',
        'Materialized views refreshed',
        NOW()
    );
END;
$$;


ALTER FUNCTION public.refresh_cache_views() OWNER TO benefitlens_user;

--
-- Name: set_cache(character varying, jsonb, integer); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.set_cache(key character varying, value jsonb, ttl_seconds integer DEFAULT 3600) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO cache_store (cache_key, cache_value, expires_at)
    VALUES (key, value, NOW() + (ttl_seconds || ' seconds')::INTERVAL)
    ON CONFLICT (cache_key) 
    DO UPDATE SET 
        cache_value = EXCLUDED.cache_value,
        expires_at = EXCLUDED.expires_at,
        updated_at = NOW();
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$;


ALTER FUNCTION public.set_cache(key character varying, value jsonb, ttl_seconds integer) OWNER TO benefitlens_user;

--
-- Name: set_csrf_token(character varying, character varying, integer); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.set_csrf_token(session_id character varying, token character varying, ttl_seconds integer DEFAULT 3600) RETURNS boolean
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO csrf_tokens (session_id, token, expires_at)
    VALUES (session_id, token, NOW() + (ttl_seconds || ' seconds')::INTERVAL)
    ON CONFLICT (session_id)
    DO UPDATE SET 
        token = EXCLUDED.token,
        expires_at = EXCLUDED.expires_at,
        created_at = NOW();
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$;


ALTER FUNCTION public.set_csrf_token(session_id character varying, token character varying, ttl_seconds integer) OWNER TO benefitlens_user;

--
-- Name: update_company_analytics_summary(uuid, date); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.update_company_analytics_summary(target_company_id uuid, target_date date DEFAULT CURRENT_DATE) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO company_analytics_summary (
        company_id,
        date,
        page_views,
        unique_visitors,
        benefit_interactions,
        search_appearances
    )
    SELECT
        target_company_id,
        target_date,
        COALESCE(views.page_views, 0),
        COALESCE(views.unique_visitors, 0),
        COALESCE(interactions.benefit_interactions, 0),
        COALESCE(appearances.search_appearances, 0)
    FROM (
        SELECT
            COUNT(*) as page_views,
            COUNT(DISTINCT COALESCE(user_id::text, session_id)) as unique_visitors
        FROM company_page_views
        WHERE company_id = target_company_id
        AND DATE(created_at) = target_date
    ) views
    CROSS JOIN (
        SELECT
            COUNT(*) as benefit_interactions
        FROM benefit_search_interactions
        WHERE company_id = target_company_id
        AND DATE(created_at) = target_date
    ) interactions
    CROSS JOIN (
        SELECT
            COUNT(DISTINCT sq.id) as search_appearances
        FROM search_queries sq
        JOIN benefit_search_interactions bsi ON sq.id = bsi.search_query_id
        WHERE bsi.company_id = target_company_id
        AND DATE(sq.created_at) = target_date
    ) appearances
    ON CONFLICT (company_id, date) DO UPDATE SET
        page_views = EXCLUDED.page_views,
        unique_visitors = EXCLUDED.unique_visitors,
        benefit_interactions = EXCLUDED.benefit_interactions,
        search_appearances = EXCLUDED.search_appearances,
        updated_at = NOW();
END;
$$;


ALTER FUNCTION public.update_company_analytics_summary(target_company_id uuid, target_date date) OWNER TO benefitlens_user;

--
-- Name: update_daily_analytics_summary(date); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.update_daily_analytics_summary(target_date date DEFAULT CURRENT_DATE) RETURNS void
    LANGUAGE plpgsql
    AS $$
BEGIN
    INSERT INTO daily_analytics_summary (
        date,
        total_company_views,
        total_searches,
        total_benefit_interactions,
        unique_visitors,
        unique_searchers,
        top_searched_benefits,
        top_viewed_companies
    )
    SELECT
        target_date,
        COALESCE(company_views.total_views, 0),
        COALESCE(searches.total_searches, 0),
        COALESCE(interactions.total_interactions, 0),
        COALESCE(company_views.unique_visitors, 0),
        COALESCE(searches.unique_searchers, 0),
        COALESCE(top_benefits.benefits, '[]'::jsonb),
        COALESCE(top_companies.companies, '[]'::jsonb)
    FROM (
        -- Company views for the day
        SELECT
            COUNT(*) as total_views,
            COUNT(DISTINCT COALESCE(user_id::text, session_id)) as unique_visitors
        FROM company_page_views
        WHERE DATE(created_at) = target_date
    ) company_views
    CROSS JOIN (
        -- Searches for the day
        SELECT
            COUNT(*) as total_searches,
            COUNT(DISTINCT COALESCE(user_id::text, session_id)) as unique_searchers
        FROM search_queries
        WHERE DATE(created_at) = target_date
    ) searches
    CROSS JOIN (
        -- Benefit interactions for the day
        SELECT
            COUNT(*) as total_interactions
        FROM benefit_search_interactions
        WHERE DATE(created_at) = target_date
    ) interactions
    CROSS JOIN (
        -- Top searched benefits
        SELECT
            COALESCE(jsonb_agg(
                jsonb_build_object(
                    'benefit_id', b.id,
                    'benefit_name', b.name,
                    'search_count', benefit_searches.search_count
                )
                ORDER BY benefit_searches.search_count DESC
            ) FILTER (WHERE benefit_searches.search_count > 0), '[]'::jsonb) as benefits
        FROM (
            SELECT
                bsi.benefit_id,
                COUNT(*) as search_count
            FROM benefit_search_interactions bsi
            JOIN search_queries sq ON bsi.search_query_id = sq.id
            WHERE DATE(sq.created_at) = target_date
            GROUP BY bsi.benefit_id
            ORDER BY search_count DESC
            LIMIT 10
        ) benefit_searches
        JOIN benefits b ON benefit_searches.benefit_id = b.id
    ) top_benefits
    CROSS JOIN (
        -- Top viewed companies
        SELECT
            COALESCE(jsonb_agg(
                jsonb_build_object(
                    'company_id', c.id,
                    'company_name', c.name,
                    'view_count', company_views.view_count
                )
                ORDER BY company_views.view_count DESC
            ) FILTER (WHERE company_views.view_count > 0), '[]'::jsonb) as companies
        FROM (
            SELECT
                cpv.company_id,
                COUNT(*) as view_count
            FROM company_page_views cpv
            WHERE DATE(cpv.created_at) = target_date
            GROUP BY cpv.company_id
            ORDER BY view_count DESC
            LIMIT 10
        ) company_views
        JOIN companies c ON company_views.company_id = c.id
    ) top_companies
    ON CONFLICT (date) DO UPDATE SET
        total_company_views = EXCLUDED.total_company_views,
        total_searches = EXCLUDED.total_searches,
        total_benefit_interactions = EXCLUDED.total_benefit_interactions,
        unique_visitors = EXCLUDED.unique_visitors,
        unique_searchers = EXCLUDED.unique_searchers,
        top_searched_benefits = EXCLUDED.top_searched_benefits,
        top_viewed_companies = EXCLUDED.top_viewed_companies,
        updated_at = NOW();
END;
$$;


ALTER FUNCTION public.update_daily_analytics_summary(target_date date) OWNER TO benefitlens_user;

--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: benefitlens_user
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_updated_at_column() OWNER TO benefitlens_user;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: activity_log; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.activity_log (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    event_type character varying(50) NOT NULL,
    event_description text NOT NULL,
    user_id character varying(255),
    user_email character varying(255),
    user_name character varying(255),
    company_id uuid,
    company_name character varying(255),
    benefit_id uuid,
    benefit_name character varying(255),
    metadata jsonb,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT activity_log_event_type_check CHECK (((event_type)::text = ANY ((ARRAY['benefit_automatically_removed'::character varying, 'benefit_disputed'::character varying, 'benefit_removal_dispute_approved'::character varying, 'benefit_removal_dispute_cancelled'::character varying, 'benefit_removal_dispute_rejected'::character varying, 'benefit_removal_dispute_submitted'::character varying, 'benefit_verified'::character varying, 'cache_refresh'::character varying, 'company_added'::character varying, 'session_cleanup'::character varying, 'user_deleted'::character varying, 'user_registered'::character varying])::text[])))
);


ALTER TABLE public.activity_log OWNER TO benefitlens_user;

--
-- Name: TABLE activity_log; Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON TABLE public.activity_log IS 'Tracks all system activities for admin dashboard and audit purposes';


--
-- Name: COLUMN activity_log.event_type; Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON COLUMN public.activity_log.event_type IS 'Type of event: benefit_automatically_removed, benefit_disputed, benefit_removal_dispute_approved, benefit_removal_dispute_cancelled, benefit_removal_dispute_rejected, benefit_removal_dispute_submitted, benefit_verified, cache_refresh, company_added, session_cleanup, user_deleted, user_registered';


--
-- Name: COLUMN activity_log.event_description; Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON COLUMN public.activity_log.event_description IS 'Human-readable description of the event for display in admin dashboard';


--
-- Name: COLUMN activity_log.metadata; Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON COLUMN public.activity_log.metadata IS 'Additional event-specific data stored as JSON';


--
-- Name: auth_logs; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.auth_logs (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    event_type character varying(50) NOT NULL,
    status character varying(20) NOT NULL,
    email character varying(255),
    error_type character varying(100),
    error_message text,
    ip_address inet,
    user_agent text,
    token_used character varying(20),
    failure_reason text,
    additional_context jsonb,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT auth_logs_event_type_check CHECK (((event_type)::text = ANY ((ARRAY['sign_in_request'::character varying, 'sign_up_request'::character varying, 'magic_link_verification'::character varying, 'session_creation'::character varying, 'rate_limit_hit'::character varying])::text[]))),
    CONSTRAINT auth_logs_status_check CHECK (((status)::text = ANY ((ARRAY['success'::character varying, 'failure'::character varying])::text[])))
);


ALTER TABLE public.auth_logs OWNER TO benefitlens_user;

--
-- Name: TABLE auth_logs; Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON TABLE public.auth_logs IS 'Detailed logs of authentication events for security monitoring and admin review';


--
-- Name: COLUMN auth_logs.event_type; Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON COLUMN public.auth_logs.event_type IS 'Type of authentication event (sign_in_request, sign_up_request, etc.)';


--
-- Name: COLUMN auth_logs.status; Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON COLUMN public.auth_logs.status IS 'Success or failure status of the authentication event';


--
-- Name: COLUMN auth_logs.error_type; Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON COLUMN public.auth_logs.error_type IS 'Categorized error type for failed events';


--
-- Name: COLUMN auth_logs.token_used; Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON COLUMN public.auth_logs.token_used IS 'Partial magic link token (first 8 chars) for tracking';


--
-- Name: COLUMN auth_logs.failure_reason; Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON COLUMN public.auth_logs.failure_reason IS 'Human-readable explanation of why the authentication failed';


--
-- Name: COLUMN auth_logs.additional_context; Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON COLUMN public.auth_logs.additional_context IS 'Additional context data in JSON format';


--
-- Name: benefit_categories; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.benefit_categories (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(100) NOT NULL,
    display_name character varying(255) NOT NULL,
    description text,
    icon character varying(50),
    sort_order integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.benefit_categories OWNER TO benefitlens_user;

--
-- Name: TABLE benefit_categories; Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON TABLE public.benefit_categories IS 'Benefit categories table - active status is now determined dynamically based on benefit count';


--
-- Name: benefit_removal_disputes; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.benefit_removal_disputes (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_benefit_id uuid NOT NULL,
    user_id uuid NOT NULL,
    reason text NOT NULL,
    status character varying(50) DEFAULT 'pending'::character varying,
    admin_user_id uuid,
    admin_comment text,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT benefit_removal_disputes_status_check CHECK (((status)::text = ANY (ARRAY[('pending'::character varying)::text, ('approved'::character varying)::text, ('rejected'::character varying)::text, ('cancelled'::character varying)::text])))
);


ALTER TABLE public.benefit_removal_disputes OWNER TO benefitlens_user;

--
-- Name: benefit_search_interactions; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.benefit_search_interactions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    search_query_id uuid,
    benefit_id uuid NOT NULL,
    company_id uuid NOT NULL,
    interaction_type character varying(50) NOT NULL,
    user_id uuid,
    session_id character varying(255),
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT benefit_search_interactions_interaction_type_check CHECK (((interaction_type)::text = ANY (ARRAY[('view'::character varying)::text, ('click'::character varying)::text, ('verify'::character varying)::text, ('dispute'::character varying)::text])))
);


ALTER TABLE public.benefit_search_interactions OWNER TO benefitlens_user;

--
-- Name: benefit_verifications; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.benefit_verifications (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_benefit_id uuid NOT NULL,
    user_id character varying(255) NOT NULL,
    status character varying(50) NOT NULL,
    comment text,
    created_at timestamp with time zone DEFAULT now(),
    CONSTRAINT benefit_verifications_status_check CHECK (((status)::text = ANY (ARRAY[('confirmed'::character varying)::text, ('disputed'::character varying)::text])))
);


ALTER TABLE public.benefit_verifications OWNER TO benefitlens_user;

--
-- Name: benefits; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.benefits (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    icon character varying(255),
    created_at timestamp with time zone DEFAULT now(),
    description text,
    category_id uuid NOT NULL
);


ALTER TABLE public.benefits OWNER TO benefitlens_user;

--
-- Name: companies; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.companies (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    name character varying(255) NOT NULL,
    size character varying(50) NOT NULL,
    industry character varying(255) NOT NULL,
    description text,
    domain character varying(255),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    career_url character varying(500),
    website character varying(500),
    logo_url character varying(500),
    founded_year integer,
    CONSTRAINT companies_size_check CHECK (((size)::text = ANY (ARRAY[('startup'::character varying)::text, ('small'::character varying)::text, ('medium'::character varying)::text, ('large'::character varying)::text, ('enterprise'::character varying)::text])))
);


ALTER TABLE public.companies OWNER TO benefitlens_user;

--
-- Name: COLUMN companies.website; Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON COLUMN public.companies.website IS 'Company website URL';


--
-- Name: COLUMN companies.logo_url; Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON COLUMN public.companies.logo_url IS 'URL to company logo image';


--
-- Name: COLUMN companies.founded_year; Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON COLUMN public.companies.founded_year IS 'Year the company was founded';


--
-- Name: company_benefits; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.company_benefits (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    benefit_id uuid NOT NULL,
    added_by character varying(255),
    is_verified boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.company_benefits OWNER TO benefitlens_user;

--
-- Name: benefits_with_categories_cache; Type: MATERIALIZED VIEW; Schema: public; Owner: benefitlens_user
--

CREATE MATERIALIZED VIEW public.benefits_with_categories_cache AS
 SELECT b.id,
    b.name,
    b.category_id,
    b.icon,
    b.description,
    b.created_at,
    bc.name AS category,
    bc.display_name AS category_display_name,
    bc.icon AS category_icon,
    bc.description AS category_description,
    count(cb.company_id) AS company_count
   FROM (((public.benefits b
     LEFT JOIN public.benefit_categories bc ON ((b.category_id = bc.id)))
     LEFT JOIN public.company_benefits cb ON ((b.id = cb.benefit_id)))
     LEFT JOIN public.companies c ON ((cb.company_id = c.id)))
  GROUP BY b.id, b.name, b.category_id, b.icon, b.description, b.created_at, bc.name, bc.display_name, bc.icon, bc.description
  ORDER BY b.name
  WITH NO DATA;


ALTER TABLE public.benefits_with_categories_cache OWNER TO benefitlens_user;

--
-- Name: cache_store; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.cache_store (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    cache_key character varying(255) NOT NULL,
    cache_value jsonb NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.cache_store OWNER TO benefitlens_user;

--
-- Name: company_locations; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.company_locations (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    location_raw character varying(255) NOT NULL,
    location_normalized character varying(255) NOT NULL,
    city character varying(100),
    country character varying(100),
    country_code character varying(2),
    latitude numeric(10,8),
    longitude numeric(11,8),
    is_primary boolean DEFAULT false,
    is_headquarters boolean DEFAULT false,
    location_type character varying(50) DEFAULT 'office'::character varying,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT company_locations_location_type_check CHECK (((location_type)::text = ANY (ARRAY[('office'::character varying)::text, ('headquarters'::character varying)::text, ('branch'::character varying)::text, ('remote'::character varying)::text])))
);


ALTER TABLE public.company_locations OWNER TO benefitlens_user;

--
-- Name: companies_with_benefits_cache; Type: MATERIALIZED VIEW; Schema: public; Owner: benefitlens_user
--

CREATE MATERIALIZED VIEW public.companies_with_benefits_cache AS
 SELECT c.id,
    c.name,
    c.description,
    c.website,
    c.logo_url,
    c.size,
    c.industry,
    c.founded_year,
    c.created_at,
    c.updated_at,
    COALESCE(json_agg(DISTINCT jsonb_build_object('id', cb.id, 'benefit_id', cb.benefit_id, 'is_verified', cb.is_verified, 'added_by', cb.added_by, 'created_at', cb.created_at, 'benefit', jsonb_build_object('id', b.id, 'name', b.name, 'category_id', b.category_id, 'icon', b.icon, 'category', jsonb_build_object('id', bc.id, 'name', bc.name, 'display_name', bc.display_name, 'icon', bc.icon, 'description', bc.description)))) FILTER (WHERE (cb.id IS NOT NULL)), '[]'::json) AS company_benefits,
    COALESCE(json_agg(DISTINCT jsonb_build_object('id', cl.id, 'location_type', cl.location_type, 'city', cl.city, 'country', cl.country, 'country_code', cl.country_code, 'is_primary', cl.is_primary, 'is_headquarters', cl.is_headquarters, 'location_raw', cl.location_raw, 'location_normalized', cl.location_normalized)) FILTER (WHERE (cl.id IS NOT NULL)), '[]'::json) AS locations
   FROM ((((public.companies c
     LEFT JOIN public.company_benefits cb ON ((c.id = cb.company_id)))
     LEFT JOIN public.benefits b ON ((cb.benefit_id = b.id)))
     LEFT JOIN public.benefit_categories bc ON ((b.category_id = bc.id)))
     LEFT JOIN public.company_locations cl ON ((c.id = cl.company_id)))
  GROUP BY c.id, c.name, c.description, c.website, c.logo_url, c.size, c.industry, c.founded_year, c.created_at, c.updated_at
  WITH NO DATA;


ALTER TABLE public.companies_with_benefits_cache OWNER TO benefitlens_user;

--
-- Name: company_analytics_summary; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.company_analytics_summary (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    date date NOT NULL,
    page_views integer DEFAULT 0,
    unique_visitors integer DEFAULT 0,
    benefit_interactions integer DEFAULT 0,
    search_appearances integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.company_analytics_summary OWNER TO benefitlens_user;

--
-- Name: company_page_views; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.company_page_views (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    user_id uuid,
    session_id character varying(255),
    ip_address inet,
    user_agent text,
    referrer text,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.company_page_views OWNER TO benefitlens_user;

--
-- Name: company_users; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.company_users (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    company_id uuid NOT NULL,
    email character varying(255) NOT NULL,
    is_verified boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.company_users OWNER TO benefitlens_user;

--
-- Name: company_verification_tokens; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.company_verification_tokens (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    token character varying(255) NOT NULL,
    user_id uuid NOT NULL,
    user_email character varying(255) NOT NULL,
    company_id uuid NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    used_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.company_verification_tokens OWNER TO benefitlens_user;

--
-- Name: csrf_tokens; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.csrf_tokens (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    session_id character varying(255) NOT NULL,
    token character varying(255) NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.csrf_tokens OWNER TO benefitlens_user;

--
-- Name: daily_analytics_summary; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.daily_analytics_summary (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    date date NOT NULL,
    total_company_views integer DEFAULT 0,
    total_searches integer DEFAULT 0,
    total_benefit_interactions integer DEFAULT 0,
    unique_visitors integer DEFAULT 0,
    unique_searchers integer DEFAULT 0,
    top_searched_benefits jsonb,
    top_viewed_companies jsonb,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.daily_analytics_summary OWNER TO benefitlens_user;

--
-- Name: magic_link_rate_limits; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.magic_link_rate_limits (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    email character varying(255) NOT NULL,
    request_count integer DEFAULT 1,
    window_start timestamp with time zone DEFAULT now(),
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.magic_link_rate_limits OWNER TO benefitlens_user;

--
-- Name: magic_link_tokens; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.magic_link_tokens (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    token character varying(255) NOT NULL,
    email character varying(255) NOT NULL,
    user_data jsonb,
    expires_at timestamp with time zone NOT NULL,
    used_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.magic_link_tokens OWNER TO benefitlens_user;

--
-- Name: migration_log; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.migration_log (
    id integer NOT NULL,
    migration_name character varying(255) NOT NULL,
    description text,
    applied_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.migration_log OWNER TO benefitlens_user;

--
-- Name: migration_log_id_seq; Type: SEQUENCE; Schema: public; Owner: benefitlens_user
--

CREATE SEQUENCE public.migration_log_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.migration_log_id_seq OWNER TO benefitlens_user;

--
-- Name: migration_log_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: benefitlens_user
--

ALTER SEQUENCE public.migration_log_id_seq OWNED BY public.migration_log.id;


--
-- Name: missing_company_reports; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.missing_company_reports (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_email character varying(255) NOT NULL,
    email_domain character varying(255) NOT NULL,
    first_name character varying(255),
    last_name character varying(255),
    status character varying(50) DEFAULT 'pending'::character varying,
    admin_notes text,
    company_id uuid,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT missing_company_reports_status_check CHECK (((status)::text = ANY (ARRAY[('pending'::character varying)::text, ('reviewed'::character varying)::text, ('added'::character varying)::text, ('rejected'::character varying)::text])))
);


ALTER TABLE public.missing_company_reports OWNER TO benefitlens_user;

--
-- Name: rate_limits; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.rate_limits (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    identifier character varying(255) NOT NULL,
    window_start timestamp with time zone NOT NULL,
    request_count integer DEFAULT 1,
    request_timestamps jsonb DEFAULT '[]'::jsonb,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.rate_limits OWNER TO benefitlens_user;

--
-- Name: saved_companies; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.saved_companies (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id character varying(255) NOT NULL,
    company_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.saved_companies OWNER TO benefitlens_user;

--
-- Name: search_queries; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.search_queries (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    query_text text NOT NULL,
    user_id uuid,
    session_id character varying(255),
    results_count integer DEFAULT 0,
    filters_applied jsonb,
    ip_address inet,
    user_agent text,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.search_queries OWNER TO benefitlens_user;

--
-- Name: session_activity; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.session_activity (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    session_token character varying(255) NOT NULL,
    activity_type character varying(50) NOT NULL,
    ip_address inet,
    user_agent text,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.session_activity OWNER TO benefitlens_user;

--
-- Name: session_config; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.session_config (
    id integer NOT NULL,
    setting_name character varying(100) NOT NULL,
    setting_value text NOT NULL,
    description text,
    updated_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.session_config OWNER TO benefitlens_user;

--
-- Name: session_config_id_seq; Type: SEQUENCE; Schema: public; Owner: benefitlens_user
--

CREATE SEQUENCE public.session_config_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.session_config_id_seq OWNER TO benefitlens_user;

--
-- Name: session_config_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: benefitlens_user
--

ALTER SEQUENCE public.session_config_id_seq OWNED BY public.session_config.id;


--
-- Name: user_sessions; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.user_sessions (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    session_token character varying(255) NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now()
);


ALTER TABLE public.user_sessions OWNER TO benefitlens_user;

--
-- Name: users; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.users (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    email character varying(255) NOT NULL,
    first_name character varying(255),
    last_name character varying(255),
    email_verified boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    role character varying(50) DEFAULT 'user'::character varying,
    payment_status character varying(50) DEFAULT 'free'::character varying,
    company_id uuid,
    CONSTRAINT users_payment_status_check CHECK (((payment_status)::text = ANY (ARRAY[('free'::character varying)::text, ('paying'::character varying)::text]))),
    CONSTRAINT users_role_check CHECK (((role)::text = ANY (ARRAY[('user'::character varying)::text, ('admin'::character varying)::text])))
);


ALTER TABLE public.users OWNER TO benefitlens_user;

--
-- Name: COLUMN users.company_id; Type: COMMENT; Schema: public; Owner: benefitlens_user
--

COMMENT ON COLUMN public.users.company_id IS 'Explicit company association for user - takes precedence over email domain matching';


--
-- Name: session_monitoring; Type: VIEW; Schema: public; Owner: benefitlens_user
--

CREATE VIEW public.session_monitoring AS
 SELECT u.email,
    u.first_name,
    u.last_name,
    us.session_token,
    us.created_at,
    us.expires_at,
        CASE
            WHEN (us.expires_at < now()) THEN 'expired'::text
            ELSE 'active'::text
        END AS status,
    (EXTRACT(epoch FROM (us.expires_at - now())) / (3600)::numeric) AS hours_until_expiry
   FROM (public.user_sessions us
     JOIN public.users u ON ((us.user_id = u.id)))
  ORDER BY us.created_at DESC;


ALTER TABLE public.session_monitoring OWNER TO benefitlens_user;

--
-- Name: user_benefit_rankings; Type: TABLE; Schema: public; Owner: benefitlens_user
--

CREATE TABLE public.user_benefit_rankings (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    benefit_id uuid NOT NULL,
    ranking integer NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT user_benefit_rankings_ranking_check CHECK (((ranking >= 1) AND (ranking <= 10)))
);


ALTER TABLE public.user_benefit_rankings OWNER TO benefitlens_user;

--
-- Name: migration_log id; Type: DEFAULT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.migration_log ALTER COLUMN id SET DEFAULT nextval('public.migration_log_id_seq'::regclass);


--
-- Name: session_config id; Type: DEFAULT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.session_config ALTER COLUMN id SET DEFAULT nextval('public.session_config_id_seq'::regclass);


--
-- Data for Name: activity_log; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.activity_log (id, event_type, event_description, user_id, user_email, user_name, company_id, company_name, benefit_id, benefit_name, metadata, created_at) FROM stdin;
\.


--
-- Data for Name: auth_logs; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.auth_logs (id, event_type, status, email, error_type, error_message, ip_address, user_agent, token_used, failure_reason, additional_context, created_at) FROM stdin;
\.


--
-- Data for Name: benefit_categories; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.benefit_categories (id, name, display_name, description, icon, sort_order, created_at, updated_at) FROM stdin;
cf50a8f3-a570-4b64-a278-ec7f233ee631	health	Health & Medical	Health insurance, medical benefits, and healthcare-related perks	🏥	1	2025-07-31 16:59:59.633775+00	2025-07-31 16:59:59.633775+00
481d5577-6965-42d4-8d49-f981262f16d5	time_off	Time Off	Vacation days, sick leave, parental leave, and other time-off benefits	🏖️	2	2025-07-31 16:59:59.633775+00	2025-07-31 16:59:59.633775+00
5530c93f-4125-49d1-851f-1df1e6a0f131	financial	Financial	Retirement plans, stock options, bonuses, and financial benefits	💰	3	2025-07-31 16:59:59.633775+00	2025-07-31 16:59:59.633775+00
bf4aa570-5652-47e6-82a9-16964e72e50f	development	Development	Learning budgets, training programs, conference attendance, and professional development	📚	4	2025-07-31 16:59:59.633775+00	2025-07-31 16:59:59.633775+00
835b768e-feee-40a8-905a-d05aa32c647e	work_life	Work-Life Balance	Flexible hours, remote work, sabbaticals, and work-life balance benefits	⚖️	6	2025-07-31 16:59:59.633775+00	2025-07-31 16:59:59.633775+00
a89e85b9-8a77-4aa4-afb5-2ab2605bb822	other	Other	Miscellaneous benefits that don't fit into other categories	🎁	7	2025-07-31 16:59:59.633775+00	2025-07-31 16:59:59.633775+00
34de83db-d8d3-41ea-9e68-d625b3435fce	fitness	Fitness	Gym memberships, wellness programs, mental health support, and fitness benefits	💪	5	2025-07-31 16:59:59.633775+00	2025-08-11 11:44:03.794576+00
\.


--
-- Data for Name: benefit_removal_disputes; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.benefit_removal_disputes (id, company_benefit_id, user_id, reason, status, admin_user_id, admin_comment, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: benefit_search_interactions; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.benefit_search_interactions (id, search_query_id, benefit_id, company_id, interaction_type, user_id, session_id, created_at) FROM stdin;
\.


--
-- Data for Name: benefit_verifications; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.benefit_verifications (id, company_benefit_id, user_id, status, comment, created_at) FROM stdin;
\.


--
-- Data for Name: benefits; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.benefits (id, name, icon, created_at, description, category_id) FROM stdin;
38e4427f-8af5-4ca1-a7b6-91e320663b3c	Corporate Benefits	🛍️	2025-08-06 13:07:10.083484+00	\N	5530c93f-4125-49d1-851f-1df1e6a0f131
89c16d9e-3c4d-427c-9246-5e896789168d	Gym Membership - ClassPass	🏋️‍♀️	2025-08-06 13:39:15.662613+00	\N	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
3e037e16-c9fe-4cc8-ace5-eabb6d235266	Unlimited PTO	🏖️	2025-07-26 20:13:06.682043+00	\N	481d5577-6965-42d4-8d49-f981262f16d5
647c2c6b-89c0-46ca-845b-9b2d43020d10	Stock Options	📈	2025-07-26 20:13:06.682043+00	\N	5530c93f-4125-49d1-851f-1df1e6a0f131
18ce8268-56c4-4c32-8948-7623b046d789	Retirement Plan	💰	2025-07-26 20:13:06.682043+00	\N	5530c93f-4125-49d1-851f-1df1e6a0f131
7265cf9b-3342-4072-952e-f3dca64021b0	Learning Budget	📚	2025-07-26 20:13:06.682043+00	\N	bf4aa570-5652-47e6-82a9-16964e72e50f
ca47af64-8cc1-44c4-bde6-3335c8e831ae	Free Lunch	🍽️	2025-07-26 20:13:06.682043+00	\N	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
0f7800e1-78b0-4b7d-aec2-d5ff9b6576ef	Pet-Friendly Office	🐕	2025-07-26 20:13:06.682043+00	\N	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
1daca593-7cfa-46c9-a36a-30f33958744a	Dental Insurance Plus (Zahnzusatzversicherung)	🦷	2025-07-29 20:44:21.73927+00	\N	cf50a8f3-a570-4b64-a278-ec7f233ee631
a5bfa352-04a5-4a20-ad0d-29a40cfbc153	Company Health Check-ups (Betriebsärztliche Untersuchungen)	🩺	2025-07-29 20:44:21.740555+00	\N	cf50a8f3-a570-4b64-a278-ec7f233ee631
4123b2ca-714b-43e0-a10f-09592bd7508e	Mental Health Support (Psychologische Betreuung)	🧠	2025-07-29 20:44:21.742171+00	\N	cf50a8f3-a570-4b64-a278-ec7f233ee631
57aa217c-6ec5-41eb-9a54-5f57aa4f492c	Occupational Health Services (Arbeitsmedizin)	⚕️	2025-07-29 20:44:21.743569+00	\N	cf50a8f3-a570-4b64-a278-ec7f233ee631
2b08f080-a7b7-4c9f-8581-5ed0d32d21ad	Holiday Bonus (Urlaubsgeld)	🏖️	2025-07-29 20:44:21.745937+00	\N	5530c93f-4125-49d1-851f-1df1e6a0f131
49f0624a-3f4d-4f22-8149-b2eb8cae3adc	Company Pension Scheme (Betriebliche Altersvorsorge)	💰	2025-07-29 20:44:21.747098+00	\N	5530c93f-4125-49d1-851f-1df1e6a0f131
7d100bf6-49c2-4b30-b202-3fec0f95c5f8	Profit Sharing (Gewinnbeteiligung)	📈	2025-07-29 20:44:21.749579+00	\N	5530c93f-4125-49d1-851f-1df1e6a0f131
3bc932ec-43a9-41d1-b780-0bbd176ff6a1	Employee Stock Purchase Plan (Mitarbeiterbeteiligung)	📊	2025-07-29 20:44:21.750555+00	\N	5530c93f-4125-49d1-851f-1df1e6a0f131
c7a54683-2c31-426e-ad68-a5fe417c6ba2	Extended Vacation Days (Zusätzliche Urlaubstage)	🌴	2025-07-29 20:44:21.75235+00	\N	481d5577-6965-42d4-8d49-f981262f16d5
2b26e80d-dd6b-4b3e-bec0-a840174cba2f	Parental Leave (Elternzeit)	👶	2025-07-29 20:44:21.754027+00	\N	481d5577-6965-42d4-8d49-f981262f16d5
a560f4c3-f1f4-485a-8136-cbbb0601916d	Sabbatical Leave (Sabbatjahr)	🎒	2025-07-29 20:44:21.754801+00	\N	481d5577-6965-42d4-8d49-f981262f16d5
e2b12709-01f8-4eca-b87d-4cef4a6c5691	Special Leave Days (Sonderurlaub)	📅	2025-07-29 20:44:21.75608+00	\N	481d5577-6965-42d4-8d49-f981262f16d5
1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	Flexible Working Hours (Gleitzeit)	⏰	2025-07-29 20:44:21.76161+00	\N	835b768e-feee-40a8-905a-d05aa32c647e
bf579b38-41ac-4f0c-a499-c1f2b13953d9	Remote Work (Homeoffice)	🏠	2025-07-29 20:44:21.76254+00	\N	835b768e-feee-40a8-905a-d05aa32c647e
b4b04287-9cb6-4bab-bc7f-cc6ad94d3b52	Part-Time Work Options (Teilzeitarbeit)	⏱️	2025-07-29 20:44:21.763423+00	\N	835b768e-feee-40a8-905a-d05aa32c647e
bf1eee61-637b-4e96-aca9-b9411e7b8e74	Job Sharing (Arbeitsplatz-Teilung)	👥	2025-07-29 20:44:21.764248+00	\N	835b768e-feee-40a8-905a-d05aa32c647e
d1bf45a0-90cd-4ac7-aa33-1c59f7d86acb	Training Budget (Weiterbildungsbudget)	📚	2025-07-29 20:44:21.765888+00	\N	bf4aa570-5652-47e6-82a9-16964e72e50f
f2ed4187-22ff-49f2-a440-26144cb4df6f	Language Learning Support (Sprachkurse)	🗣️	2025-07-29 20:44:21.7667+00	\N	bf4aa570-5652-47e6-82a9-16964e72e50f
1ae5d945-4638-4201-9590-53bb1ff76c43	Conference Attendance (Konferenz-Teilnahme)	🎤	2025-07-29 20:44:21.767462+00	\N	bf4aa570-5652-47e6-82a9-16964e72e50f
67afd4c0-d070-4f7c-b6fc-f29292b42019	Internal Training Programs (Interne Schulungen)	🎓	2025-07-29 20:44:21.768079+00	\N	bf4aa570-5652-47e6-82a9-16964e72e50f
6ee2858c-0284-4b26-818c-2bb2d1f6b212	Mentoring Programs (Mentoring-Programme)	👨‍🏫	2025-07-29 20:44:21.768929+00	\N	bf4aa570-5652-47e6-82a9-16964e72e50f
0803de65-cfff-4be8-987d-2e45fc65a35a	Study Leave (Bildungsurlaub)	📖	2025-07-29 20:44:21.769816+00	\N	bf4aa570-5652-47e6-82a9-16964e72e50f
c62d7cb4-6445-4e3e-82d7-3dbe178715c1	Company Car (Dienstwagen)	🚗	2025-07-29 20:44:21.771564+00	\N	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
30ba3f2f-059a-41c7-a9a4-808c7b80fd8b	Parking Allowance (Parkplatz-Zuschuss)	🅿️	2025-07-29 20:44:21.773302+00	\N	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
85bf05ea-7a28-4803-a984-9b7657b7152d	Travel Allowance (Reisekostenzuschuss)	✈️	2025-07-29 20:44:21.774056+00	\N	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
d3a57221-e781-4ea3-956c-9e96b67f857d	Meal Vouchers (Essensgutscheine)	🍽️	2025-07-29 20:44:21.774917+00	\N	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
edb05410-10b4-4810-9587-c60c0d4bec18	Company Cafeteria (Betriebskantine)	🍱	2025-07-29 20:44:21.775641+00	\N	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
c6740995-5fb4-4f3c-9825-f94535fda107	Free Coffee & Snacks (Kostenlose Verpflegung)	☕	2025-07-29 20:44:21.776491+00	\N	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
e8a2fc2d-3f34-4b27-ae7b-634e2c9aa3fa	Catered Meals (Catering-Service)	🥗	2025-07-29 20:44:21.777374+00	\N	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
8941a853-c940-4107-98d5-366b6161a351	Childcare Support (Kinderbetreuung)	👨‍👩‍👧‍👦	2025-07-29 20:44:21.778199+00	\N	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
20e9cfca-2a1b-4cda-8fe4-46e59a8d7f10	Family Events (Familienfeste)	🎉	2025-07-29 20:44:21.77902+00	\N	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
524af4ec-4188-4d96-bb57-a073feed02e9	Emergency Childcare (Notfall-Kinderbetreuung)	🆘	2025-07-29 20:44:21.779998+00	\N	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
b8bb2b8b-3c40-4fa8-9a7c-6ca901ede3e2	Mobile Phone Allowance (Handy-Zuschuss)	📱	2025-07-29 20:44:21.780949+00	\N	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
602cb9fa-e3af-4b8e-872f-1e5226647586	Home Office Equipment (Homeoffice-Ausstattung)	💻	2025-07-29 20:44:21.781805+00	\N	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
f39b3744-b94b-4bdf-8e4a-bb15c2ab4359	Internet Allowance (Internet-Zuschuss)	🌐	2025-07-29 20:44:21.782593+00	\N	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
add96f67-8b49-44da-b5fd-7aa6f8650f55	Christmas Bonus (Weihnachtsgeld)	🎄	2025-07-29 20:44:21.74473+00	13th month salary typically paid in November/December	5530c93f-4125-49d1-851f-1df1e6a0f131
a738057d-6928-4070-a0a1-205f7b14cde3	Job Ticket (Jobticket)	🚊	2025-07-29 20:44:21.770729+00	Subsidized public transportation pass	a89e85b9-8a77-4aa4-afb5-2ab2605bb822
a040740f-c14f-40c7-b293-bc86bf9d6c40	Viertage Woche (4-Day Work Week)	📅	2025-07-29 20:53:00.891187+00	Four-day work week with full salary - modern work-life balance approach	835b768e-feee-40a8-905a-d05aa32c647e
d93e061b-a870-451e-8a0d-f584e3d2df8b	Capital-Forming Benefits (Vermögenswirksame Leistungen)	💎	2025-07-29 20:44:21.7484+00	\N	5530c93f-4125-49d1-851f-1df1e6a0f131
0489acf5-5801-4226-95a9-f117265a2840	Gym Membership - Other	🏋️‍♀️	2025-07-26 20:13:06.682043+00	\N	34de83db-d8d3-41ea-9e68-d625b3435fce
0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	Gym Membership - Wellhub	🏋️‍♀️	2025-08-06 13:37:43.843016+00	\N	34de83db-d8d3-41ea-9e68-d625b3435fce
6f4eb6e3-678e-4ed4-b2b0-b2cc245af0ed	Company Sports Teams (Betriebssport)	⚽	2025-07-29 20:44:21.759369+00	\N	34de83db-d8d3-41ea-9e68-d625b3435fce
ebba64a0-0c05-4f0e-a1c2-6fd9abf0a737	Bike Leasing (Dienstfahrrad) - Bikeleasing	🚲	2025-08-11 11:30:47.719627+00	\N	34de83db-d8d3-41ea-9e68-d625b3435fce
4ecdd3e6-396a-4291-8728-6565f772e27d	Bike Leasing (Dienstfahrrad) - Deutsche Dienstrad	🚲	2025-08-06 13:11:52.289258+00	\N	34de83db-d8d3-41ea-9e68-d625b3435fce
38486edf-4126-4ce2-8e3a-debd7774255e	Bike Leasing (Dienstfahrrad) - JobRad	🚲	2025-08-06 13:34:53.267458+00	\N	34de83db-d8d3-41ea-9e68-d625b3435fce
2e52a361-3ac6-472e-b052-74ab8666fb39	Bike Leasing (Dienstfahrrad) - Other	🚲	2025-08-06 13:38:41.682593+00	\N	34de83db-d8d3-41ea-9e68-d625b3435fce
3aba7e66-800d-4a91-9dc9-1294d83f6f94	30 Days Vacation	🏖️	2025-07-29 20:44:21.751524+00	\N	481d5577-6965-42d4-8d49-f981262f16d5
268b111d-44b5-4d74-8865-230c40c1e677	Gym Membership - Hansefit	🏋️‍♀️	2025-07-29 20:44:21.758561+00	\N	34de83db-d8d3-41ea-9e68-d625b3435fce
0da39480-235b-4362-b9af-0490c9c1160f	Gym Membership - Urban Sports Club	🏋️‍♀️	2025-08-06 13:37:19.357789+00	\N	34de83db-d8d3-41ea-9e68-d625b3435fce
310688c7-3853-4ba4-9c6d-cd4677a916b6	Gym Membership - EGYM Wellpass	🏋️‍♀️	2025-07-29 20:44:21.757807+00	\N	34de83db-d8d3-41ea-9e68-d625b3435fce
b404d132-885c-4633-be8e-2e289bd9ef19	Ergonomic Workplace Setup (Ergonomischer Arbeitsplatz)	🪑	2025-07-29 20:44:21.760866+00	\N	34de83db-d8d3-41ea-9e68-d625b3435fce
14848486-00da-4b9a-b8e0-35d3ddf9d3d9	Massage Services (Massagen am Arbeitsplatz)	💆‍♀️	2025-07-29 20:44:21.76011+00	\N	34de83db-d8d3-41ea-9e68-d625b3435fce
\.


--
-- Data for Name: cache_store; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.cache_store (id, cache_key, cache_value, expires_at, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: companies; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.companies (id, name, size, industry, description, domain, created_at, updated_at, career_url, website, logo_url, founded_year) FROM stdin;
6b09eed5-2cc0-4f8d-8f93-4d22d09ef433	Fraport AG	medium	Airport operator	German airport operator, operates Frankfurt Airport and other airports worldwide, member of MDAX	fraport.com	2025-08-12 14:38:40.040066+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
a46168a4-1aa0-4f63-98cd-0708ff01c082	Covestro AG	large	Chemicals	German company that produces specialty chemicals, spun off from Bayer, member of DAX 40	covestro.com	2025-08-12 14:38:39.808786+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
e7ebb734-da29-4f54-92fa-b30a41de94f8	Redcare Pharmacy N.V.	medium	Pharmacy	Dutch online pharmacy company, operates e-pharmacy platforms in Europe, member of MDAX	redcare-pharmacy.com	2025-08-12 14:38:40.20163+00	2025-08-12 14:48:16.202958+00	\N	\N	\N	\N
c5d86c5f-f4d7-41de-a885-5ecaa99382f6	Scout24 AG	medium	Online-market	German digital marketplace company, operates online platforms for real estate and automotive markets, member of MDAX	scout24.com	2025-08-12 14:38:40.220355+00	2025-08-12 14:48:16.223094+00	\N	\N	\N	\N
c60b72aa-e461-4455-aea2-54351bc248c3	Siltronic AG	medium	Semiconductor industry	German semiconductor wafer manufacturer, produces silicon wafers for the semiconductor industry, member of MDAX	siltronic.com	2025-08-12 14:38:40.22838+00	2025-08-12 14:48:16.233525+00	\N	\N	\N	\N
4b78798f-5911-4d42-b960-d0dc745d8e1b	STABILUS SE	medium	Automotive	German automotive supplier, manufactures gas springs, dampers and electromechanical drives, member of MDAX	stabilus.com	2025-08-12 14:38:40.236802+00	2025-08-12 14:48:16.241076+00	\N	\N	\N	\N
566454f3-e954-4ccf-bf17-5159f56eae8e	TAG Immobilien AG	medium	Real estate	German real estate company, owns and manages residential properties across Germany, member of MDAX	tag-ag.com	2025-08-12 14:38:40.254714+00	2025-08-12 14:48:16.257059+00	\N	\N	\N	\N
da45afe9-76e7-4dfb-be53-857c2c5b763b	Talanx AG	medium	Insurance	German insurance group, provides insurance and reinsurance services worldwide, member of MDAX	talanx.com	2025-08-12 14:38:40.262491+00	2025-08-12 14:48:16.26349+00	\N	\N	\N	\N
5bf80e46-5c91-499c-89cf-916709d774dd	ThyssenKrupp AG	medium	Conglomerate	German multinational conglomerate, operates in industrial engineering, steel production and materials services, member of MDAX	thyssenkrupp.com	2025-08-12 14:38:40.273717+00	2025-08-12 14:48:16.281544+00	\N	\N	\N	\N
271cf44a-7115-4cbc-929a-6ac11cd2eee3	Traton SE	medium	Automotive	German commercial vehicle manufacturer, subsidiary of Volkswagen Group, member of MDAX	traton.com	2025-08-12 14:38:40.287668+00	2025-08-12 14:48:16.319078+00	\N	\N	\N	\N
58087702-006c-4c7c-8abf-48324b64977b	TUI AG	medium	Tourism	German multinational travel and tourism company, one of the world's largest tourism groups, member of MDAX	tuigroup.com	2025-08-12 14:38:40.295085+00	2025-08-12 14:48:16.325893+00	\N	\N	\N	\N
ab9f370d-3dbc-40e0-9366-ec154aa2a6b3	United Internet AG	medium	Telecommunications	German internet service provider, operates web hosting, cloud services and telecommunications, member of MDAX	united-internet.de	2025-08-12 14:38:40.30895+00	2025-08-12 14:48:16.340569+00	\N	\N	\N	\N
37e9e641-643b-43af-a3f0-75bce47526be	Wacker Chemie AG	medium	Chemistry	German chemical company, produces silicones, polymers, and polysilicon for various industries, member of MDAX	wacker.com	2025-08-12 14:38:40.319149+00	2025-08-12 14:48:16.348787+00	\N	\N	\N	\N
164a02bf-58de-4d70-90ed-54a17641c1a1	Deutsche Beteiligungs AG	small	Private Equity	German private equity company, invests in mid-market companies in German-speaking countries, member of SDAX	deutsche-beteiligung.de	2025-08-12 14:38:40.427802+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	Deutsche Lufthansa AG	medium	Airlines	German airline and aviation company, one of the world's largest airline groups, member of MDAX	lufthansa.com	2025-07-26 20:13:06.688547+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
0fdc87d9-5f64-48ee-9410-8a25cf12e550	Dürr AG	small	Automotive	German mechanical and plant engineering company, provides automation solutions for automotive industry, member of SDAX	durr.com	2025-08-12 14:38:40.481768+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
e9de360f-c376-465e-8517-dc3c37bcd9dd	Jungheinrich AG	medium	Intralogistics, Mechanical engineering	German manufacturer of forklift trucks, warehouse technology and intralogistics solutions, member of MDAX	jungheinrich.com	2025-08-12 14:38:40.137119+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
fa2034b0-c512-4be7-a1bc-700d1559a554	N26 GmbH	medium	Financial Services	German neobank headquartered in Berlin	n26.de	2025-07-29 20:22:57.822814+00	2025-08-14 12:57:57.119989+00	https://n26.com/en/careers	\N	\N	\N
f391b88a-6e80-4bf6-acc3-3bf36f7a665c	Schroder Investment Management GmbH	enterprise	Financial Services	Schroders is a leading global asset manager with offices in 37 markets across Europe, North, Central, and South America, Asia-Pacific, and the Middle East.	schroders.com	2025-08-06 10:10:14.086995+00	2025-08-12 12:09:18.957614+00	https://www.schroders.com/en/global/individual/careers/	\N	\N	\N
d3c9a4b2-2eba-49db-9b01-2fa568614b8c	Münchener Rückversicherungs-Gesellschaft AG	large	Financial Services	German multinational insurance company, one of the world's leading reinsurers, member of DAX 40	munichre.com	2025-07-29 20:34:31.097023+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
06cc6caf-acc5-497b-a68a-6a643d7cc66c	Adidas AG	large	Apparel	German multinational corporation that designs and manufactures shoes, clothing and accessories, member of DAX 40	adidas.com	2025-07-29 20:22:57.811764+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
555cf212-1e5d-4a4a-8f9a-8880b3f0e9f2	Eckert & Ziegler Strahlen- und Medizintechnik AG	small	Medical equipment	German company specializing in medical radioisotopes and radiation therapy equipment, member of SDAX	ezag.de	2025-08-12 14:38:40.507037+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
531dd3fb-eea8-4376-9c7f-f6c188810fc9	Adesso SE	small	IT and consulting	German IT consulting and software development company, provides digital transformation services, member of SDAX	adesso.de	2025-08-12 14:38:40.335176+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
9413d6f2-9c16-4489-881a-f5a073632d6a	Dermapharm Holding SE	small	Pharmaceuticals	German pharmaceutical company, develops and manufactures pharmaceutical and healthcare products, member of SDAX	dermapharm.de	2025-08-12 14:38:40.418666+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
e0c95800-8906-4535-9140-c25ee08dedd9	Atoss Software AG	small	Software	German software company, develops workforce management and time tracking solutions, member of SDAX	atoss.com	2025-08-12 14:38:40.342822+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
38afb290-3557-449b-88af-012c141d1b3c	Rational AG	medium	Manufacturing	German manufacturer of professional cooking equipment, produces combi-steamers and other kitchen appliances, member of MDAX	rational-online.com	2025-07-29 20:34:38.089425+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
dc95baae-b25a-4ef5-a400-8894b83d5158	Heidelberg Materials AG	large	Construction Materials	German multinational building materials company, one of the world's largest building materials companies, member of DAX 40	heidelbergmaterials.com	2025-08-12 14:38:39.874156+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
ef1015c5-0570-4c1c-a60e-fc9006b0b513	Deutsche Pfandbriefbank AG	small	Banking	German commercial real estate finance bank, specializes in real estate and public investment finance, member of SDAX	pfandbriefbank.com	2025-08-12 14:38:40.440121+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
b16b8f39-dc19-411c-ab31-bfff59534448	Commerzbank AG	large	Financial Services	German multinational commercial bank, one of Germany's major banks, member of DAX 40	commerzbank.de	2025-07-26 20:13:06.688547+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
1c933b7b-406a-4bbd-abd9-87e7667cc7eb	TeamViewer AG	medium	Software	German technology company, provides remote access and remote control software, member of MDAX	teamviewer.com	2025-07-29 20:22:57.825392+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
bf2c20dc-63ed-4ec4-9985-43ba6e0f5daf	Henkel AG & Co. KGaA	large	Consumer Goods	German multinational chemical and consumer goods company, produces adhesives, sealants, and surface treatments, member of DAX 40	henkel.com	2025-07-29 20:34:38.081474+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
4d92ae8f-991a-4d22-90f9-aab1f6c2512f	Evonik Industries AG	medium	Chemistry	German specialty chemicals company, produces specialty chemicals for various industries, member of MDAX	evonik.com	2025-07-29 20:34:38.085826+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
2e551ef6-f8cb-4601-9032-99c4289712b9	Aurubis AG	medium	Metals	German copper producer and recycler, Europe's largest copper smelter, member of MDAX	aurubis.com	2025-08-12 14:38:39.959107+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
b8bd7b27-b49d-4f71-baea-d3d7b43a3550	Siemens Energy AG	large	Energy technology	German energy technology company, provides products, solutions and services for power generation, transmission and storage, member of DAX 40	siemens-energy.com	2025-07-29 20:34:31.098238+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
417f26fe-f19b-4085-8445-07a7dfb60a48	Deutsche Post AG	large	Logistics	German multinational package delivery and supply chain management company, operates DHL, member of DAX 40	dhl.com	2025-08-12 14:38:39.843017+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
9e2a3af7-68a6-42ea-916d-5a484b9bda9a	Ceconomy AG	small	Retail	German retail company, operates consumer electronics stores including MediaMarkt and Saturn, member of SDAX	ceconomy.de	2025-08-12 14:38:40.393039+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
004a3070-db79-47ab-8872-3c383c05f26c	Hugo Boss AG	medium	Clothing, Accessories	German luxury fashion house, designs and manufactures clothing, accessories, footwear and fragrances, member of MDAX	hugoboss.com	2025-08-12 14:38:40.120279+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
000eb8b4-345d-4e1f-ae98-69318a0e97aa	HelloFresh SE	medium	Meal kit	German meal-kit company, delivers pre-portioned ingredients and recipes to subscribers, member of MDAX	hellofresh.com	2025-07-29 20:22:57.826884+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
37855bda-7840-4253-a111-a337cabdcd25	Befesa S.A.	medium	Waste management	Spanish environmental services company focused on steel dust and aluminum salt slags recycling, member of MDAX	befesa.com	2025-08-12 14:38:39.977927+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
04e50ca5-a627-45f8-b4b7-5d3dc84a4506	Krones AG	medium	Machinery	German packaging and bottling machine manufacturer, provides solutions for beverage and food industries, member of MDAX	krones.com	2025-08-12 14:38:40.154506+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
43667920-619d-482d-8a4f-6ad4cba0558b	Knorr-Bremse AG	medium	Manufacturing	German manufacturer of braking systems for rail and commercial vehicles, member of MDAX	knorr-bremse.com	2025-07-29 20:34:38.08806+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
a32301a7-a61a-4919-8ea2-5258c5b1887d	BMW Group	enterprise	Automotive	German multinational corporation which produces luxury vehicles and motorcycles	bmw.de	2025-07-29 20:22:57.805714+00	2025-08-14 12:57:57.119989+00	https://www.bmwgroup.jobs	\N	\N	\N
8f466bb4-6793-4ebe-a712-dd7689e1a4f8	Nordex SE	medium	Wind energy	German wind turbine manufacturer, develops and manufactures wind energy systems, member of MDAX	nordex-online.com	2025-08-12 14:38:40.179111+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
8abb35a2-ac9f-4716-8f3c-d45071b48958	Fresenius Medical Care AG & Co. KGaA	large	Healthcare	German company that provides dialysis care and related services, world's largest dialysis company, member of DAX 40	freseniusmedicalcare.com	2025-07-29 20:34:38.079175+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
25e7dca3-4f08-414f-baae-7b868102a0a2	BMW AG	large	Automotive	German multinational manufacturer of luxury vehicles and motorcycles, member of DAX 40	bmw.com	2025-08-12 14:38:39.798586+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
8baa1ddd-faef-4602-9f1a-c25c73dff5f8	Rheinmetall AG	large	Aerospace & Defence	German automotive and defence company, produces automotive components and defence technology, member of DAX 40	rheinmetall.com	2025-07-29 20:34:31.101205+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
0dcad781-92e5-45b8-9b85-7e559303c3af	FinanceFlow	medium	Financial Services	Modern fintech company providing digital banking solutions	financeflow.com	2025-07-26 20:13:06.688547+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
efd876d9-7e26-45c2-9cb5-f89006ccf201	E.ON SE	large	Utilities	German electric utility company, focuses on energy networks and customer solutions, member of DAX 40	eon.com	2025-08-12 14:38:39.853883+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
********-badb-4198-8762-266d4a3b216a	Beiersdorf AG	large	Consumer goods	German multinational company that manufactures personal care products, member of DAX 40	beiersdorf.com	2025-07-29 20:34:38.082634+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
0a9e280f-8e34-4ab5-ae80-88f60ecd0d77	1&1 AG	small	Telecommunications	German telecommunications company, provides internet, mobile and fixed-line services, member of SDAX	1und1.de	2025-08-12 14:38:40.326747+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
3da642a8-2604-4b14-b5c2-e54cbea674ba	Deutsche Telekom AG	large	Telecommunication	German telecommunications company, one of the world's largest telecommunications companies, member of DAX 40	telekom.com	2025-07-29 20:22:57.816785+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
3ce8e31c-010b-4edf-83c3-a1adc682509e	Airbus SE	large	Aerospace & Defence	European multinational aerospace corporation, designs, manufactures and sells civil and military aerospace products, member of DAX 40	airbus.com	2025-08-12 14:38:39.693185+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
0fdfd0c6-3f68-4d4d-b7c4-2973998104be	MTU Aero Engines AG	large	Aerospace & Defence	German aircraft engine manufacturer, develops, manufactures and provides service support for commercial and military aircraft engines, member of DAX 40	mtu.de	2025-08-12 14:38:39.887472+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
d0355ee2-baba-454d-9b84-ae8ba8088c64	MeinAuto GmbH	medium	e-Commerce		meinauto.de	2025-07-30 09:28:00.166839+00	2025-08-14 13:09:39.2288+00	\N	\N	\N	\N
00138327-c6fe-463b-8fce-30c76bd4c099	Bayer AG	large	Pharmaceuticals	German multinational pharmaceutical and biotechnology company, member of DAX 40	bayer.com	2025-07-29 20:22:57.815065+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	BASF SE	large	Chemicals	German multinational chemical company, the largest chemical producer in the world, member of DAX 40	basf.com	2025-07-29 20:22:57.813575+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
e7c38b02-bbf3-4b1a-9de6-2866de2c11e5	LEG Immobilien AG	medium	Real estate	German real estate company, owns and manages residential properties primarily in North Rhine-Westphalia, member of MDAX	leg-wohnen.de	2025-08-12 14:38:40.17132+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
c91cf384-36cc-412f-bcab-6fda76e641c3	Delivery Hero SE	medium	Delivery service	German multinational online food delivery company, operates in over 40 countries, member of MDAX	deliveryhero.com	2025-07-29 20:22:57.8214+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
832184e3-ca5f-41c8-9140-0339c333164f	Carl Zeiss Meditec AG	medium	Medical technology	German medical technology company, develops and manufactures systems for ophthalmology and microsurgery, member of MDAX	zeiss.com	2025-08-12 14:38:39.999736+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
a2313897-00a7-4bf7-a0fd-fcaa26eff747	Aroundtown S.A.	medium	Real estate	Luxembourg-based real estate company focused on commercial properties in Germany and the Netherlands, member of MDAX	aroundtown.de	2025-08-12 14:38:39.947366+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
77c0d9e5-7d2c-409f-a75d-273b066d5f3e	Bosch	enterprise	Technology	German multinational engineering and technology company	bosch.de	2025-07-29 20:22:57.809443+00	2025-08-14 12:57:57.119989+00	https://www.bosch.de/karriere	\N	\N	\N
6b8523ca-0c34-4495-ae7c-58b7c187f8dd	Auto1 Group SE	small	E-commerce	German digital automotive platform, operates online marketplaces for used cars, member of SDAX	auto1-group.com	2025-08-12 14:38:40.351146+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
5c519372-e81d-4c55-aaac-b77d113d48ca	Zalando SE	large	E-Commerce	German multinational e-commerce company, operates online fashion and lifestyle platform, member of DAX 40	zalando.com	2025-07-29 20:22:57.819792+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
ac9d5b5e-dd53-438f-8582-1b2d36b8705f	Brenntag SE	large	Distribution	German chemical distribution company, the global market leader in chemical and ingredients distribution, member of DAX 40	brenntag.com	2025-07-29 20:34:38.08387+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
dc33601c-f4a9-464e-9e78-f76e83290a5e	Jenoptik AG	medium	Optics	German optical systems company, develops and manufactures products for photonics applications, member of MDAX	jenoptik.com	2025-08-12 14:38:40.127983+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
4fe21b42-ee3b-4914-9d3f-50458ac26597	Porsche AG	large	Automotive	German automobile manufacturer specializing in high-performance sports cars, SUVs and sedans, member of DAX 40	porsche.com	2025-07-29 20:34:31.092261+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
abd65218-3a9c-4b44-9591-6176c5243664	Daimler Truck Holding AG	large	Automotive	German multinational automotive corporation, focuses on commercial vehicles, member of DAX 40	daimlertruck.com	2025-08-12 14:38:39.81869+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
02836b5c-32a3-446a-b7e2-82a1ec01b8cb	Bechtle AG	medium	IT services	German IT system house and IT e-commerce company, provides IT services and solutions, member of MDAX	bechtle.com	2025-08-12 14:38:39.967558+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
601b5439-4f73-43cf-920d-f760941427d5	Fuchs Petrolub SE	medium	Chemistry	German lubricants manufacturer, develops and produces lubricants and related specialties, member of MDAX	fuchs.com	2025-08-12 14:38:40.064541+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
6b081fa3-3deb-4064-a681-638103a77d3c	Porsche Automobil Holding SE	large	Automotive	German holding company with investments in the automotive industry, controls Volkswagen Group, member of DAX 40	porsche-se.com	2025-08-12 14:38:39.895849+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
59f19980-1594-4133-9195-baa612e3dd79	Hannover Rück SE	large	Insurance	German reinsurance company, one of the world's largest reinsurers, member of DAX 40	hannover-re.com	2025-08-12 14:38:39.863307+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
0bf1feef-964a-4f18-9566-3fffb1de71e6	Hensoldt AG	medium	Defence	German defense electronics company, develops sensor solutions for defense and security applications, member of MDAX	hensoldt.net	2025-08-12 14:38:40.099612+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
065ba8b7-61bb-4300-a38e-e8c327cb8971	PwC	large	Consulting	One of the Big Four accounting firms providing audit, tax and consulting services	pwc.com	2025-07-26 20:13:06.688547+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
1f8509ef-6130-4303-a173-6506ec830047	Vonovia SE	large	Real Estate	German residential real estate company, one of Europe's largest residential real estate companies, member of DAX 40	vonovia.de	2025-08-12 14:38:39.926839+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
94b7a6f8-805d-4f54-8c25-fa7fe1b0c238	Deutsche Bank AG	large	Financial Services	German multinational investment bank and financial services company, member of DAX 40	db.com	2025-07-26 20:13:06.688547+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
710df751-8fd6-41db-8245-8b2bf039ed85	ATLAS Dienstleistungen für Vermögensberatung GmbH	large	Financial Services	ATLAS Dienstleistungen für Vermögensberatung GmbH is a limited liability company under German law with its headquarters in Frankfurt am Main. It is registered in the commercial register of the Frankfurt am Main Local Court under number HRB 38158	dvag.com	2025-08-06 12:59:55.929331+00	2025-08-14 12:57:57.119989+00	https://www.karriere-atlas.de/stellenangebote	\N	\N	\N
00ce236d-d595-4b94-885f-902083b3b055	Borussia Dortmund GmbH & Co. KGaA	small	Sports	German professional football club, operates in the Bundesliga and various commercial activities, member of SDAX	bvb.de	2025-08-12 14:38:40.37355+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
0e50c593-a32d-4420-93ae-7e76fbfa0674	Deutsche Börse AG	large	Financial Services	German marketplace organizer for the trading of shares and other securities, operates Frankfurt Stock Exchange, member of DAX 40	deutsche-boerse.com	2025-08-12 14:38:39.828414+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
9bc051c4-8ea2-46a7-88bc-c81ae2b04a32	Kion Group AG	medium	Handling equipment	German manufacturer of forklift trucks and warehouse automation technology, member of MDAX	kiongroup.com	2025-07-29 20:34:38.086785+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
456f9891-6e24-4136-8368-a6e576d2d53a	Encavis AG	medium	Renewable energy	German renewable energy company, acquires and operates solar parks and wind farms, member of MDAX	encavis.com	2025-08-12 14:38:40.019243+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
8d626f73-5fc4-46e1-92b7-91e21dd5b70c	DWS Group GmbH & Co. KGaA	small	Investment management	German asset management company, provides investment solutions for institutional and retail clients, member of SDAX	dws.com	2025-08-12 14:38:40.494389+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
a0dfffa1-90f9-496b-9a19-9d1db99d197b	Merck KGaA	large	Pharmaceuticals	German multinational science and technology company, operates in healthcare, life sciences, and electronics, member of DAX 40	merckgroup.com	2025-07-29 20:34:38.080411+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
37441a48-1255-417f-a513-cac9b1853e23	GEA Group AG	medium	Machinery	German engineering company, supplies technology for food processing and other industries, member of MDAX	gea.com	2025-08-12 14:38:40.074286+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
8aeefd4a-cc05-402c-89af-12b8783431e5	Aixtron SE	medium	Semiconductor industry	German semiconductor equipment manufacturer, provides deposition equipment for semiconductor industry, member of MDAX	aixtron.com	2025-08-12 14:38:39.939304+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
9f82f663-4a3c-48e5-83d9-fc2b1a5bd69d	McDermott Will & Emery LLP	enterprise	Legal Services	Global law firm with more than 1,400 lawyers...	mwe.com	2025-07-31 11:44:42.352914+00	2025-08-14 12:57:57.119989+00	https://careers.mwe.com	\N	\N	\N
81319234-71ef-4f21-9d02-8bf7fbd2ecd4	CompuGroup Medical SE & Co. KGaA	small	Healthcare	German healthcare IT company, develops software solutions for healthcare providers, member of SDAX	cgm.com	2025-07-29 20:34:38.092387+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
647b7f13-9573-4673-9d03-922935a9fc9f	Sartorius AG	large	Medical Technology	German pharmaceutical and laboratory equipment supplier, provides equipment and services for biopharmaceutical research and production, member of DAX 40	sartorius.com	2025-07-29 20:34:38.069136+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
1fe135c6-0c7e-426e-9e33-b8419c772fdb	Volkswagen AG	large	Automotive	German multinational automotive manufacturer, one of the world's largest automakers, member of DAX 40	volkswagen.com	2025-07-29 20:22:57.802743+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
c2b0593e-73f3-4970-8bb3-4a510827181a	RWE AG	large	Utilities	German multinational energy company, focuses on renewable energy, energy trading and electricity generation, member of DAX 40	rwe.com	2025-08-12 14:38:39.912977+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
c6e8582e-0915-41c3-a633-ae0826192a24	Bilfinger SE	medium	Engineering and Services	German industrial services company, provides engineering, maintenance and construction services, member of MDAX	bilfinger.com	2025-08-12 14:38:39.988508+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
b5780bae-a178-45db-aaea-52670c16f19f	Siemens AG	large	Industrials	German multinational conglomerate, focuses on industry, energy, healthcare and infrastructure, member of DAX 40	siemens.com	2025-07-29 20:34:31.088053+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
b28367d9-2016-4a4b-a162-5b865cb44118	Evotec SE	medium	Biotechnology	German drug discovery and development company, provides drug discovery services to pharmaceutical companies, member of MDAX	evotec.com	2025-08-12 14:38:40.029757+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
4bd5e470-ca13-4562-9f0f-ed7b830576c0	Drägerwerk AG & Co. KGaA	small	Engineering, medical technology	German company that makes breathing apparatus, gas detection and analysis systems, member of SDAX	draeger.com	2025-08-12 14:38:40.472997+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
5c691a85-1101-4ea0-810f-f7fe2ed0bb90	Allianz SE	large	Financial Services	German multinational financial services company, one of the world's largest insurers and asset managers, member of DAX 40	allianz.com	2025-07-29 20:22:57.818383+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
c837dbee-de4f-4726-b325-175809ad737b	TechStart GmbH	startup	Technology	Innovative startup focusing on AI and machine learning solutions	techstart.de	2025-07-26 20:13:06.688547+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
efb1372f-7edf-4f62-8c3c-d8538dfeeb9b	Cancom SE	small	IT	German IT services company, provides cloud solutions and IT infrastructure services, member of SDAX	cancom.com	2025-08-12 14:38:40.381935+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
25498a55-e29f-4a93-aab8-752cd1f2d5fc	Infineon Technologies AG	large	Technology	German semiconductor manufacturer, designs and manufactures semiconductors and system solutions, member of DAX 40	infineon.com	2025-07-29 20:34:31.100316+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
a4359e21-ce42-43cd-95b5-a72df59597bb	Cewe Stiftung & Co. KGaA	small	Printing	German photo service company, provides photo printing and personalized photo products, member of SDAX	cewe.de	2025-08-12 14:38:40.405761+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
f3f2bb21-94cc-42de-abcf-123f837bcd2f	Hochtief AG	medium	Construction	German construction company, one of the world's largest construction companies, member of MDAX	hochtief.com	2025-08-12 14:38:40.110379+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
674b216a-eb1b-4a8f-9543-495009109b70	HELLA GmbH & Co. KGaA	medium	Automotive	German automotive parts supplier, specializes in lighting technology and electronics, member of MDAX	hella.com	2025-08-12 14:38:40.090941+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
3c629f04-0cb3-44f1-ab2b-8f2481fd2a7f	Fielmann AG	small	Retail, Optics	German optical retail company, operates eyewear stores across Europe, member of SDAX	fielmann.com	2025-08-12 14:38:40.526843+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
2c09a41b-b047-4b54-86be-d7b09997f9ec	DZ Bank	large	Financial Services	Central institution for more than 800 cooperative banks in Germany	dzbank.de	2025-07-26 20:13:06.688547+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
b12de1f1-114b-4e30-b6aa-432e5398c97b	Gerresheimer AG	medium	Packaging	German manufacturer of glass and plastic products for the pharmaceutical and healthcare industries, member of MDAX	gerresheimer.com	2025-08-12 14:38:40.082392+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
d7adcc64-bfee-4ef3-999f-659dd00e2175	Mercedes-Benz Group AG	large	Automotive	German multinational automotive corporation, produces luxury vehicles, buses, coaches, and trucks, member of DAX 40	mercedes-benz.com	2025-07-29 20:22:57.807594+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
5624c6d5-ca41-4386-84bb-334cb64e5f69	Elmos Semiconductor SE	small	Electrical engineering	German semiconductor company, develops and manufactures semiconductors for automotive applications, member of SDAX	elmos.com	2025-08-12 14:38:40.516072+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
74e1fc43-8fc7-4894-9ac6-319eb6ecda91	Nemetschek SE	medium	Software	German software company, develops software solutions for architecture, engineering and construction industries, member of MDAX	nemetschek.com	2025-07-29 20:34:38.09042+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
dbeb0b10-a459-416d-bc6e-5bd84421b248	BayWa AG	small	Retail and agribusiness	German agricultural trading and energy company, operates in agriculture, building materials and energy, member of SDAX	baywa.com	2025-08-12 14:38:40.365729+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
83c7307b-b03a-4d10-b0c8-4d3676699dd0	Fresenius SE & Co. KGaA	large	Healthcare	German multinational health care company, provides products and services for dialysis, hospitals and outpatient medical care, member of DAX 40	fresenius.com	2025-07-29 20:34:38.077538+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
7ab34c9b-1578-4f2e-99e3-878b5149f0c4	PUMA SE	medium	Sports equipment	German multinational corporation that designs and manufactures athletic and casual footwear, apparel and accessories, member of MDAX	puma.com	2025-08-12 14:38:40.188185+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
9a3e818d-bcf7-4681-83d5-e89b225832a8	Continental AG	large	Automotive	German multinational automotive parts manufacturing company, specializes in tires, brake systems, and automotive safety, member of DAX 40	continental.com	2025-07-29 20:34:31.102041+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
bb2fd4de-5722-4e72-b9ae-19e265a8633d	K+S AG	medium	Mining (fertilizer and salt)	German chemical company, produces potash and salt for agricultural, industrial and consumer applications, member of MDAX	k-plus-s.com	2025-08-12 14:38:40.14496+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
997a91da-3e54-4ae5-8b8b-7130d4de2728	Freenet AG	medium	Telecommunications	German telecommunications company, provides mobile and fixed-line telecommunications services, member of MDAX	freenet.de	2025-08-12 14:38:40.054187+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
59c64665-a162-48c4-bcb6-927a78ed5e69	Siemens Healthineers AG	large	Medical Equipment	German medical technology company, develops and manufactures medical devices and healthcare IT solutions, member of DAX 40	siemens-healthineers.com	2025-07-29 20:34:31.099324+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
c704a431-a900-4ccf-bd2b-a979f1c40ce0	Rocket Internet SE	medium	Technology	German Internet company that builds and invests in internet and technology companies	rocket-internet.de	2025-07-29 20:22:57.824127+00	2025-08-14 12:57:57.119989+00	https://www.rocket-internet.com/careers	\N	\N	\N
329ca019-134c-40ab-b40b-0d6a79b65460	SAP SE	large	Technology	German multinational software corporation, creates enterprise software to manage business operations and customer relations, member of DAX 40	sap.com	2025-07-26 20:13:06.688547+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
b6b1eb84-5f0e-4457-a6c8-0a3743233791	Deutsche Wohnen SE	small	Real Estate	German residential real estate company, owns and manages residential properties primarily in Berlin, member of SDAX	deutsche-wohnen.com	2025-08-12 14:38:40.454971+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
f70e5abe-a9a1-4a33-95dc-2216279bda8c	CTS Eventim AG & Co. KGaA	medium	Leisure-events	German ticketing company, one of the leading international providers of ticketing services and live entertainment, member of MDAX	eventim.de	2025-08-12 14:38:40.008994+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
8b73480a-face-4af2-98f3-79264cfca659	Symrise AG	large	Chemicals	German chemical company that produces fragrances, flavorings, cosmetic active ingredients and raw materials, member of DAX 40	symrise.com	2025-07-29 20:34:38.084851+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
942f7063-15f7-4841-87bb-ee567d71d9ae	Software AG	medium	Technology	MDAX-listed | Stock: SOW | MDAX-listed German multinational software corporation	softwareag.com	2025-07-29 20:34:38.091315+00	2025-08-14 12:57:57.119989+00	https://www.softwareag.com/en_corporate/careers.html	\N	\N	\N
0585eae4-daf5-4b72-82f5-2dc2031e8c01	Accenture	enterprise	Consulting	Global professional services company with leading capabilities in digital, cloud and security	accenture.com	2025-07-26 20:13:06.688547+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
8bd8f26a-b36d-4d8a-9e40-57a9711303a5	Qiagen N.V.	large	Biotechnology	German multinational provider of sample and assay technologies for molecular diagnostics, applied testing, academic and pharmaceutical research, member of DAX 40	qiagen.com	2025-08-12 14:38:39.903973+00	2025-08-14 12:57:57.119989+00	\N	\N	\N	\N
\.


--
-- Data for Name: company_analytics_summary; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.company_analytics_summary (id, company_id, date, page_views, unique_visitors, benefit_interactions, search_appearances, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: company_benefits; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.company_benefits (id, company_id, benefit_id, added_by, is_verified, created_at) FROM stdin;
b5205469-b2df-4fc3-8131-78c635d170c3	38afb290-3557-449b-88af-012c141d1b3c	3aba7e66-800d-4a91-9dc9-1294d83f6f94	system	f	2025-07-29 20:50:08.055061+00
37fe066e-e1ac-4e2a-89da-e9aae9d9b405	38afb290-3557-449b-88af-012c141d1b3c	2b26e80d-dd6b-4b3e-bec0-a840174cba2f	system	f	2025-07-29 20:50:08.056577+00
28ccafb3-1dc3-4eb5-85b1-51b4074a82b8	38afb290-3557-449b-88af-012c141d1b3c	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	system	f	2025-07-29 20:50:08.057435+00
532d6ad0-cb8e-402c-8cb9-18548fbd081e	38afb290-3557-449b-88af-012c141d1b3c	bf579b38-41ac-4f0c-a499-c1f2b13953d9	system	f	2025-07-29 20:50:08.058269+00
14fdd962-04dc-4cf6-8148-21d5d2ef7b0d	c837dbee-de4f-4726-b325-175809ad737b	647c2c6b-89c0-46ca-845b-9b2d43020d10	\N	f	2025-07-26 20:13:06.691865+00
f245b39d-cd61-4bcc-80be-1f85079f801d	c837dbee-de4f-4726-b325-175809ad737b	ca47af64-8cc1-44c4-bde6-3335c8e831ae	\N	f	2025-07-26 20:13:06.691865+00
219fb93a-d286-40a4-9f4b-e6c07bacae1a	942f7063-15f7-4841-87bb-ee567d71d9ae	3aba7e66-800d-4a91-9dc9-1294d83f6f94	system	f	2025-07-29 20:50:08.074703+00
921613cb-640d-41ee-93ce-7bedd1b9947c	942f7063-15f7-4841-87bb-ee567d71d9ae	2b26e80d-dd6b-4b3e-bec0-a840174cba2f	system	f	2025-07-29 20:50:08.075988+00
33ef049b-2397-4f42-873d-d7b0cb741e7f	065ba8b7-61bb-4300-a38e-e8c327cb8971	7265cf9b-3342-4072-952e-f3dca64021b0	\N	f	2025-07-26 20:13:06.691865+00
86e84b7a-1503-405e-8c0b-22eb19fbeb4a	fa2034b0-c512-4be7-a1bc-700d1559a554	3aba7e66-800d-4a91-9dc9-1294d83f6f94	system	f	2025-07-29 20:50:08.021613+00
61bbd9db-6e79-414f-a1ea-7c3bb3687c41	fa2034b0-c512-4be7-a1bc-700d1559a554	2b26e80d-dd6b-4b3e-bec0-a840174cba2f	system	f	2025-07-29 20:50:08.023403+00
6391453f-bc37-4cff-a1f1-5c096fcfd005	329ca019-134c-40ab-b40b-0d6a79b65460	0f7800e1-78b0-4b7d-aec2-d5ff9b6576ef	\N	f	2025-07-26 20:13:06.700933+00
e8a36b86-4039-41bc-bee0-6479bee19d98	c837dbee-de4f-4726-b325-175809ad737b	0f7800e1-78b0-4b7d-aec2-d5ff9b6576ef	\N	f	2025-07-26 20:13:06.700933+00
c37ecc64-cff0-42a9-a097-65b805908373	329ca019-134c-40ab-b40b-0d6a79b65460	18ce8268-56c4-4c32-8948-7623b046d789	2be5dade-8d85-43ff-930f-52381d144389	f	2025-07-27 09:55:17.390819+00
cf87f2ee-3258-4ac7-a8a8-70e00bd97fb5	329ca019-134c-40ab-b40b-0d6a79b65460	ca47af64-8cc1-44c4-bde6-3335c8e831ae	2be5dade-8d85-43ff-930f-52381d144389	f	2025-07-27 10:12:17.625861+00
d9413a04-7479-4514-b651-f375f2e9dd23	fa2034b0-c512-4be7-a1bc-700d1559a554	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	system	f	2025-07-29 20:50:08.024333+00
c2847823-c4f5-4e47-9cb0-98afd195f979	fa2034b0-c512-4be7-a1bc-700d1559a554	bf579b38-41ac-4f0c-a499-c1f2b13953d9	system	f	2025-07-29 20:50:08.025299+00
35a44d73-8d5c-4512-b2ed-b633f840039c	c837dbee-de4f-4726-b325-175809ad737b	3e037e16-c9fe-4cc8-ace5-eabb6d235266	\N	f	2025-07-26 20:13:06.700933+00
f61eef04-d8c6-41ae-a93b-a4198b5c3ad8	2c09a41b-b047-4b54-86be-d7b09997f9ec	7265cf9b-3342-4072-952e-f3dca64021b0	\N	f	2025-07-26 20:13:06.691865+00
18878a40-e9af-41a5-91f0-757159cde3ad	2c09a41b-b047-4b54-86be-d7b09997f9ec	18ce8268-56c4-4c32-8948-7623b046d789	\N	f	2025-07-26 20:13:06.691865+00
e994b463-45d8-44cf-adbb-d6a9f22a2f3f	0585eae4-daf5-4b72-82f5-2dc2031e8c01	7265cf9b-3342-4072-952e-f3dca64021b0	\N	f	2025-07-26 20:13:06.691865+00
409cde0e-3ed1-4a08-97c9-0c5328b80819	0dcad781-92e5-45b8-9b85-7e559303c3af	7265cf9b-3342-4072-952e-f3dca64021b0	\N	f	2025-07-26 20:13:06.691865+00
66815453-f86e-4cff-9f9b-c89963ddc0eb	c704a431-a900-4ccf-bd2b-a979f1c40ce0	3aba7e66-800d-4a91-9dc9-1294d83f6f94	system	f	2025-07-29 20:50:08.064374+00
fd332466-cce8-4779-9cde-e27609d28929	c704a431-a900-4ccf-bd2b-a979f1c40ce0	2b26e80d-dd6b-4b3e-bec0-a840174cba2f	system	f	2025-07-29 20:50:08.066267+00
abe2cd5c-7d60-4ef6-aaab-dd9fa98f63db	c704a431-a900-4ccf-bd2b-a979f1c40ce0	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	system	f	2025-07-29 20:50:08.066978+00
bc456c6c-a999-461b-8395-3e8fb6749f65	c704a431-a900-4ccf-bd2b-a979f1c40ce0	bf579b38-41ac-4f0c-a499-c1f2b13953d9	system	f	2025-07-29 20:50:08.067693+00
e542c507-fcae-4f70-ba96-eb114ae33fc0	329ca019-134c-40ab-b40b-0d6a79b65460	7265cf9b-3342-4072-952e-f3dca64021b0	\N	f	2025-07-26 20:13:06.691865+00
dab088d1-20ad-4607-b478-2c8601c64044	fa2034b0-c512-4be7-a1bc-700d1559a554	add96f67-8b49-44da-b5fd-7aa6f8650f55	system	f	2025-07-29 20:50:08.02628+00
253a48bb-c4c3-40d8-a2a4-3fd6773d6ded	fa2034b0-c512-4be7-a1bc-700d1559a554	d1bf45a0-90cd-4ac7-aa33-1c59f7d86acb	system	f	2025-07-29 20:50:08.027254+00
b8f1a763-3dda-4c0c-8856-54fd1e1957bc	fa2034b0-c512-4be7-a1bc-700d1559a554	a738057d-6928-4070-a0a1-205f7b14cde3	system	f	2025-07-29 20:50:08.0281+00
be935936-03f3-4215-a749-082a64fdb34b	fa2034b0-c512-4be7-a1bc-700d1559a554	3bc932ec-43a9-41d1-b780-0bbd176ff6a1	system	f	2025-07-29 20:50:08.02966+00
d8bad5ad-dbc5-45fd-9f10-b5b95af6a285	fa2034b0-c512-4be7-a1bc-700d1559a554	7d100bf6-49c2-4b30-b202-3fec0f95c5f8	system	f	2025-07-29 20:50:08.030432+00
664fd548-18db-4ada-8453-2615abbec443	329ca019-134c-40ab-b40b-0d6a79b65460	647c2c6b-89c0-46ca-845b-9b2d43020d10	\N	f	2025-07-26 20:13:06.691865+00
9f1c60b2-3eb8-4407-928c-3a4f823227db	0a9e280f-8e34-4ab5-ae80-88f60ecd0d77	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.570436+00
60f9a062-467f-4471-a22e-2a55981aa816	38afb290-3557-449b-88af-012c141d1b3c	add96f67-8b49-44da-b5fd-7aa6f8650f55	system	f	2025-07-29 20:50:08.059624+00
9adfc080-476f-4e41-8f28-7d578de96f44	38afb290-3557-449b-88af-012c141d1b3c	d1bf45a0-90cd-4ac7-aa33-1c59f7d86acb	system	f	2025-07-29 20:50:08.060539+00
68f9bc96-796d-4533-8e32-0dfd0dde82c9	38afb290-3557-449b-88af-012c141d1b3c	a738057d-6928-4070-a0a1-205f7b14cde3	system	f	2025-07-29 20:50:08.061406+00
18c9a9e4-581d-4957-b161-ff38be21e156	c704a431-a900-4ccf-bd2b-a979f1c40ce0	add96f67-8b49-44da-b5fd-7aa6f8650f55	system	f	2025-07-29 20:50:08.068445+00
99120c7c-921c-405d-a69b-d884ccc4a563	c704a431-a900-4ccf-bd2b-a979f1c40ce0	d1bf45a0-90cd-4ac7-aa33-1c59f7d86acb	system	f	2025-07-29 20:50:08.06925+00
69ff661f-4b77-4408-8ab4-16255c5775a8	c704a431-a900-4ccf-bd2b-a979f1c40ce0	a738057d-6928-4070-a0a1-205f7b14cde3	system	f	2025-07-29 20:50:08.070169+00
e779d93d-b382-4599-8931-52392398c8df	c704a431-a900-4ccf-bd2b-a979f1c40ce0	f2ed4187-22ff-49f2-a440-26144cb4df6f	system	f	2025-07-29 20:50:08.071739+00
3ab9be5f-3ff6-4f0b-b490-628bf5a7680e	c704a431-a900-4ccf-bd2b-a979f1c40ce0	1ae5d945-4638-4201-9590-53bb1ff76c43	system	f	2025-07-29 20:50:08.072395+00
31de6467-6180-4c96-9b77-2531dcc10fc3	c704a431-a900-4ccf-bd2b-a979f1c40ce0	602cb9fa-e3af-4b8e-872f-1e5226647586	system	f	2025-07-29 20:50:08.073049+00
250f27ed-9f08-4ee1-93c7-8868d397ac37	942f7063-15f7-4841-87bb-ee567d71d9ae	add96f67-8b49-44da-b5fd-7aa6f8650f55	system	f	2025-07-29 20:50:08.078575+00
31bdf6d6-55b2-419b-8dea-34ed1b6604e5	942f7063-15f7-4841-87bb-ee567d71d9ae	d1bf45a0-90cd-4ac7-aa33-1c59f7d86acb	system	f	2025-07-29 20:50:08.079381+00
93aaeaea-d9a2-454b-892d-7a25ae934545	942f7063-15f7-4841-87bb-ee567d71d9ae	a738057d-6928-4070-a0a1-205f7b14cde3	system	f	2025-07-29 20:50:08.080124+00
0de8f2f7-b49c-4cde-b172-b51a28ed2ff5	942f7063-15f7-4841-87bb-ee567d71d9ae	f2ed4187-22ff-49f2-a440-26144cb4df6f	system	f	2025-07-29 20:50:08.081551+00
44ebbebb-4019-4973-8799-14f7ed876c2b	942f7063-15f7-4841-87bb-ee567d71d9ae	1ae5d945-4638-4201-9590-53bb1ff76c43	system	f	2025-07-29 20:50:08.082317+00
9a08cc9f-cf37-4012-a4a8-de57c7c4d70e	942f7063-15f7-4841-87bb-ee567d71d9ae	602cb9fa-e3af-4b8e-872f-1e5226647586	system	f	2025-07-29 20:50:08.083377+00
80368b44-113c-44b0-b54d-ad948677151c	1c933b7b-406a-4bbd-abd9-87e7667cc7eb	add96f67-8b49-44da-b5fd-7aa6f8650f55	system	f	2025-07-29 20:50:08.090346+00
641e682d-c6b0-4b65-b197-ddd5e3812b71	1c933b7b-406a-4bbd-abd9-87e7667cc7eb	d1bf45a0-90cd-4ac7-aa33-1c59f7d86acb	system	f	2025-07-29 20:50:08.091148+00
fd1bc438-e30b-4e5a-8c46-a1f28b9fde66	1c933b7b-406a-4bbd-abd9-87e7667cc7eb	a738057d-6928-4070-a0a1-205f7b14cde3	system	f	2025-07-29 20:50:08.091938+00
cb6877bc-308d-41d2-8e74-63b64c4b3fa1	1c933b7b-406a-4bbd-abd9-87e7667cc7eb	f2ed4187-22ff-49f2-a440-26144cb4df6f	system	f	2025-07-29 20:50:08.093437+00
20f4135b-2156-4b86-b863-1c41825e4a79	1c933b7b-406a-4bbd-abd9-87e7667cc7eb	1ae5d945-4638-4201-9590-53bb1ff76c43	system	f	2025-07-29 20:50:08.094181+00
38a781bd-f3e4-4c89-befb-65106688aee5	1c933b7b-406a-4bbd-abd9-87e7667cc7eb	602cb9fa-e3af-4b8e-872f-1e5226647586	system	f	2025-07-29 20:50:08.094916+00
4f642672-0e82-4a1a-a3e4-fde2e9066d42	1c933b7b-406a-4bbd-abd9-87e7667cc7eb	3aba7e66-800d-4a91-9dc9-1294d83f6f94	system	f	2025-07-29 20:50:08.086165+00
36e1d972-ef3b-4a78-9080-01561c33a3e1	1c933b7b-406a-4bbd-abd9-87e7667cc7eb	2b26e80d-dd6b-4b3e-bec0-a840174cba2f	system	f	2025-07-29 20:50:08.087806+00
c6a207c6-d1e2-4655-b0eb-5c17c015e698	1c933b7b-406a-4bbd-abd9-87e7667cc7eb	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	system	f	2025-07-29 20:50:08.088604+00
1c080355-362e-442d-8f2e-00d0cdbec4a8	1c933b7b-406a-4bbd-abd9-87e7667cc7eb	bf579b38-41ac-4f0c-a499-c1f2b13953d9	system	f	2025-07-29 20:50:08.089528+00
f269c8c1-bb58-44b2-9502-361e582338f9	942f7063-15f7-4841-87bb-ee567d71d9ae	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	system	f	2025-07-29 20:50:08.076812+00
bd576c05-adf3-469e-b949-569dd6b202af	942f7063-15f7-4841-87bb-ee567d71d9ae	bf579b38-41ac-4f0c-a499-c1f2b13953d9	system	f	2025-07-29 20:50:08.07775+00
b4886dde-e2df-4576-a2e2-aafcdef67e91	a32301a7-a61a-4919-8ea2-5258c5b1887d	bf579b38-41ac-4f0c-a499-c1f2b13953d9	system	f	2025-07-29 20:50:08.311664+00
a1a99d7b-1b85-466d-8893-679c024b2f11	a32301a7-a61a-4919-8ea2-5258c5b1887d	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	system	f	2025-07-29 20:50:08.310696+00
0d09324d-6540-43f0-8391-f6f8539db503	a32301a7-a61a-4919-8ea2-5258c5b1887d	2b26e80d-dd6b-4b3e-bec0-a840174cba2f	system	f	2025-07-29 20:50:08.307765+00
e6ce7322-4dec-4a19-b92d-a2cb38dd6461	a32301a7-a61a-4919-8ea2-5258c5b1887d	add96f67-8b49-44da-b5fd-7aa6f8650f55	system	f	2025-07-29 20:50:08.309634+00
f0dbd900-bc7b-45c0-a65f-54ce60dd4aa7	a32301a7-a61a-4919-8ea2-5258c5b1887d	49f0624a-3f4d-4f22-8149-b2eb8cae3adc	system	f	2025-07-29 20:50:08.30866+00
8b5c1ad8-a418-4d4d-bab7-6922223304ea	a32301a7-a61a-4919-8ea2-5258c5b1887d	3aba7e66-800d-4a91-9dc9-1294d83f6f94	system	f	2025-07-29 20:50:08.305645+00
4359af23-a4c6-43f7-8877-1afba8a549d6	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	add96f67-8b49-44da-b5fd-7aa6f8650f55	system	f	2025-07-29 20:50:08.327856+00
8e0ab600-0022-476a-9f2c-9ba65b1551a5	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	49f0624a-3f4d-4f22-8149-b2eb8cae3adc	system	f	2025-07-29 20:50:08.327135+00
b50ad240-3bc8-4ab8-b7f8-2838bc831970	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	3aba7e66-800d-4a91-9dc9-1294d83f6f94	system	f	2025-07-29 20:50:08.32509+00
2e70b2ec-ae3a-4d93-a2a9-f0e209d1cc49	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	2b26e80d-dd6b-4b3e-bec0-a840174cba2f	system	f	2025-07-29 20:50:08.326386+00
462dbb5b-3954-488c-b28b-55de3ec8a889	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	system	f	2025-07-29 20:50:08.328575+00
5e86c75f-7721-4621-a4c0-416255db63c0	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	bf579b38-41ac-4f0c-a499-c1f2b13953d9	system	f	2025-07-29 20:50:08.33024+00
cd36fa41-1e8e-4ece-8e1e-77cbf6049315	a32301a7-a61a-4919-8ea2-5258c5b1887d	2b08f080-a7b7-4c9f-8581-5ed0d32d21ad	system	f	2025-07-29 20:50:08.313306+00
3554126b-ea8f-49f0-bf9d-7ff2cf5cd23f	a32301a7-a61a-4919-8ea2-5258c5b1887d	c7a54683-2c31-426e-ad68-a5fe417c6ba2	system	f	2025-07-29 20:50:08.314089+00
4bcdf1e4-15dc-41b4-882d-7006fd83f5f4	a32301a7-a61a-4919-8ea2-5258c5b1887d	d93e061b-a870-451e-8a0d-f584e3d2df8b	system	f	2025-07-29 20:50:08.314876+00
639c4e4f-696b-44e6-8712-582028814147	a32301a7-a61a-4919-8ea2-5258c5b1887d	7d100bf6-49c2-4b30-b202-3fec0f95c5f8	system	f	2025-07-29 20:50:08.315545+00
0e4cfb31-4b0f-4aa7-b28f-646a1a9b1cf1	a32301a7-a61a-4919-8ea2-5258c5b1887d	3bc932ec-43a9-41d1-b780-0bbd176ff6a1	system	f	2025-07-29 20:50:08.316279+00
1e20ec02-cc30-41f6-bf75-6f4c05e4c9db	a32301a7-a61a-4919-8ea2-5258c5b1887d	d1bf45a0-90cd-4ac7-aa33-1c59f7d86acb	system	f	2025-07-29 20:50:08.317143+00
7de881ec-32dc-4a8d-a54c-c1e46a579654	a32301a7-a61a-4919-8ea2-5258c5b1887d	f2ed4187-22ff-49f2-a440-26144cb4df6f	system	f	2025-07-29 20:50:08.317954+00
c375d2a9-0172-4ab5-b7cd-de4e5a34a04e	a32301a7-a61a-4919-8ea2-5258c5b1887d	1ae5d945-4638-4201-9590-53bb1ff76c43	system	f	2025-07-29 20:50:08.318922+00
006d7836-0d2b-4ec1-bdc8-63254264f013	a32301a7-a61a-4919-8ea2-5258c5b1887d	a738057d-6928-4070-a0a1-205f7b14cde3	system	f	2025-07-29 20:50:08.319711+00
7d9b797d-7cd7-487d-977f-89bfd8f5850d	a32301a7-a61a-4919-8ea2-5258c5b1887d	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	system	f	2025-07-29 20:50:08.3205+00
059d0c5a-4cca-4f14-88be-8eb38e121133	a32301a7-a61a-4919-8ea2-5258c5b1887d	310688c7-3853-4ba4-9c6d-cd4677a916b6	system	f	2025-07-29 20:50:08.322276+00
9352eb1d-6a88-4660-a5ac-f86a24f07a70	a32301a7-a61a-4919-8ea2-5258c5b1887d	268b111d-44b5-4d74-8865-230c40c1e677	system	f	2025-07-29 20:50:08.323137+00
c67b7dee-7432-4fd1-b616-05ef6c5d18d3	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	2b08f080-a7b7-4c9f-8581-5ed0d32d21ad	system	f	2025-07-29 20:50:08.3322+00
87549df4-c9c4-4e04-ba79-58853456a119	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	c7a54683-2c31-426e-ad68-a5fe417c6ba2	system	f	2025-07-29 20:50:08.333157+00
700f5c0b-c29d-4517-9607-1c38a1a66a93	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	d93e061b-a870-451e-8a0d-f584e3d2df8b	system	f	2025-07-29 20:50:08.334025+00
361c7f9c-5498-4944-a75a-46f5c963e485	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	7d100bf6-49c2-4b30-b202-3fec0f95c5f8	system	f	2025-07-29 20:50:08.335052+00
1285a792-cafd-4955-a309-fc056642a22f	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	3bc932ec-43a9-41d1-b780-0bbd176ff6a1	system	f	2025-07-29 20:50:08.335974+00
0c43d833-17ef-4c43-bb8f-bda50d2d0c34	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	d1bf45a0-90cd-4ac7-aa33-1c59f7d86acb	system	f	2025-07-29 20:50:08.336778+00
3423d342-ef25-4ad1-b07b-264b33d1190d	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	f2ed4187-22ff-49f2-a440-26144cb4df6f	system	f	2025-07-29 20:50:08.337512+00
10a8f9ea-63fa-4284-9a86-7d5433e27fb9	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	1ae5d945-4638-4201-9590-53bb1ff76c43	system	f	2025-07-29 20:50:08.338204+00
20f20818-07ba-4163-a2fa-46f3d590716b	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	a738057d-6928-4070-a0a1-205f7b14cde3	system	f	2025-07-29 20:50:08.338952+00
544d6f4b-b53b-4ae6-b584-b64c578bb4e3	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	system	f	2025-07-29 20:50:08.340166+00
c3934f88-84a2-444c-93d2-dc4aebc059ca	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	310688c7-3853-4ba4-9c6d-cd4677a916b6	system	f	2025-07-29 20:50:08.341842+00
0ff9e57d-eb6e-4ed3-b715-b14b1cbaf87e	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	268b111d-44b5-4d74-8865-230c40c1e677	system	f	2025-07-29 20:50:08.342609+00
21ba11b6-466d-4a0a-86ea-6c5b3bc906e3	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	602cb9fa-e3af-4b8e-872f-1e5226647586	system	f	2025-07-29 20:50:08.343534+00
41277809-64c8-4459-bad3-cde66f535cd2	a0dfffa1-90f9-496b-9a19-9d1db99d197b	add96f67-8b49-44da-b5fd-7aa6f8650f55	system	f	2025-07-29 20:50:08.497841+00
d6559d77-c376-4f95-96bb-052f3cef586e	a0dfffa1-90f9-496b-9a19-9d1db99d197b	49f0624a-3f4d-4f22-8149-b2eb8cae3adc	system	f	2025-07-29 20:50:08.49703+00
f2e2bfa2-afde-4425-a34e-3d3e4f5c68b7	a0dfffa1-90f9-496b-9a19-9d1db99d197b	3aba7e66-800d-4a91-9dc9-1294d83f6f94	system	f	2025-07-29 20:50:08.494659+00
976546a5-768e-4f69-8fd9-878df627ba3a	a0dfffa1-90f9-496b-9a19-9d1db99d197b	2b26e80d-dd6b-4b3e-bec0-a840174cba2f	system	f	2025-07-29 20:50:08.496222+00
921acda3-5a40-4e1d-9107-52900d3b1970	a0dfffa1-90f9-496b-9a19-9d1db99d197b	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	system	f	2025-07-29 20:50:08.498651+00
9aa54a9e-2ce1-45c0-a783-03081dd4e7e2	a0dfffa1-90f9-496b-9a19-9d1db99d197b	bf579b38-41ac-4f0c-a499-c1f2b13953d9	system	f	2025-07-29 20:50:08.50034+00
f8e97b77-31f2-41bd-86cd-6a839ad443c1	a0dfffa1-90f9-496b-9a19-9d1db99d197b	2b08f080-a7b7-4c9f-8581-5ed0d32d21ad	system	f	2025-07-29 20:50:08.501831+00
108360d3-457d-49f2-8db3-320369371310	a0dfffa1-90f9-496b-9a19-9d1db99d197b	c7a54683-2c31-426e-ad68-a5fe417c6ba2	system	f	2025-07-29 20:50:08.502537+00
379e064d-eafc-4876-85cc-a8b8edea1c00	a0dfffa1-90f9-496b-9a19-9d1db99d197b	d93e061b-a870-451e-8a0d-f584e3d2df8b	system	f	2025-07-29 20:50:08.503283+00
fda24ac7-b8cd-4f50-88f4-d71b0167c31d	a0dfffa1-90f9-496b-9a19-9d1db99d197b	7d100bf6-49c2-4b30-b202-3fec0f95c5f8	system	f	2025-07-29 20:50:08.504069+00
07a648aa-9693-4741-9a2d-8726ffb89617	a0dfffa1-90f9-496b-9a19-9d1db99d197b	3bc932ec-43a9-41d1-b780-0bbd176ff6a1	system	f	2025-07-29 20:50:08.505261+00
46556caf-b814-4d6a-93af-cc3e5605d61d	a0dfffa1-90f9-496b-9a19-9d1db99d197b	d1bf45a0-90cd-4ac7-aa33-1c59f7d86acb	system	f	2025-07-29 20:50:08.506147+00
46e54885-7423-48bf-a5f6-cd7880777ff6	a0dfffa1-90f9-496b-9a19-9d1db99d197b	f2ed4187-22ff-49f2-a440-26144cb4df6f	system	f	2025-07-29 20:50:08.507017+00
2dea3032-0c81-43b8-8fd1-485c02b804aa	a0dfffa1-90f9-496b-9a19-9d1db99d197b	1ae5d945-4638-4201-9590-53bb1ff76c43	system	f	2025-07-29 20:50:08.507932+00
f043ebd2-2a58-47fd-aaef-58d1b22fbf52	a0dfffa1-90f9-496b-9a19-9d1db99d197b	a738057d-6928-4070-a0a1-205f7b14cde3	system	f	2025-07-29 20:50:08.508751+00
6dbdbd13-6ff5-4893-aba7-a8270b5c067d	a0dfffa1-90f9-496b-9a19-9d1db99d197b	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	system	f	2025-07-29 20:50:08.509558+00
8d8d50e9-ccd3-4958-b10b-ca7c0815560a	a0dfffa1-90f9-496b-9a19-9d1db99d197b	310688c7-3853-4ba4-9c6d-cd4677a916b6	system	f	2025-07-29 20:50:08.511193+00
1b234b65-b002-4b2b-b9ba-465d1c1cf794	a0dfffa1-90f9-496b-9a19-9d1db99d197b	268b111d-44b5-4d74-8865-230c40c1e677	system	f	2025-07-29 20:50:08.511889+00
ede2e22c-c0d5-4954-9a44-b727726f9ff2	a0dfffa1-90f9-496b-9a19-9d1db99d197b	a5bfa352-04a5-4a20-ad0d-29a40cfbc153	system	f	2025-07-29 20:50:08.512574+00
0ec89020-85b6-4b9a-92ad-298c2460003c	fa2034b0-c512-4be7-a1bc-700d1559a554	a040740f-c14f-40c7-b293-bc86bf9d6c40	system	f	2025-07-29 20:53:59.921778+00
215c8436-7d3e-4d33-8eea-7fbcaf8a41c8	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	a040740f-c14f-40c7-b293-bc86bf9d6c40	system	f	2025-07-29 20:53:59.921778+00
45bd8f47-a113-4e67-9f1b-d0b0720ae725	c704a431-a900-4ccf-bd2b-a979f1c40ce0	a040740f-c14f-40c7-b293-bc86bf9d6c40	system	f	2025-07-29 20:53:59.921778+00
00299814-f95c-447b-b335-5bdfea909390	1c933b7b-406a-4bbd-abd9-87e7667cc7eb	a040740f-c14f-40c7-b293-bc86bf9d6c40	system	f	2025-07-29 20:53:59.921778+00
a1ca5816-24ef-429f-89e2-9d6f53ba6e08	942f7063-15f7-4841-87bb-ee567d71d9ae	a040740f-c14f-40c7-b293-bc86bf9d6c40	system	f	2025-07-29 20:53:59.921778+00
e292e6c6-6038-4829-968e-5c7d7468722a	9f82f663-4a3c-48e5-83d9-fc2b1a5bd69d	e8a2fc2d-3f34-4b27-ae7b-634e2c9aa3fa	6e2a60e2-d0fc-4eac-9f95-e1ba3650556d	f	2025-07-31 13:25:38.64238+00
49f7f0e5-2e85-438f-91c4-5d90521d56f4	d0355ee2-baba-454d-9b84-ae8ba8088c64	a738057d-6928-4070-a0a1-205f7b14cde3	cb9bf81d-7df4-41d5-8a90-9a223151fd4b	t	2025-07-30 09:56:34.199188+00
f649e7dc-bf8b-41c9-9bfe-9c9e57355095	d0355ee2-baba-454d-9b84-ae8ba8088c64	2b26e80d-dd6b-4b3e-bec0-a840174cba2f	cb9bf81d-7df4-41d5-8a90-9a223151fd4b	t	2025-07-31 13:22:56.221771+00
7e35a946-b569-4ea9-9ad7-5eefaf7c3629	9f82f663-4a3c-48e5-83d9-fc2b1a5bd69d	b404d132-885c-4633-be8e-2e289bd9ef19	6e2a60e2-d0fc-4eac-9f95-e1ba3650556d	f	2025-07-31 13:06:55.607416+00
e3a31d6e-5c44-41a6-9400-5aa7be3c553d	9f82f663-4a3c-48e5-83d9-fc2b1a5bd69d	3aba7e66-800d-4a91-9dc9-1294d83f6f94	6e2a60e2-d0fc-4eac-9f95-e1ba3650556d	f	2025-07-31 13:07:27.983806+00
7009927a-e868-4faa-84f3-ca0330af8e8c	9f82f663-4a3c-48e5-83d9-fc2b1a5bd69d	add96f67-8b49-44da-b5fd-7aa6f8650f55	6e2a60e2-d0fc-4eac-9f95-e1ba3650556d	f	2025-07-31 13:07:44.474082+00
8caab0f4-42f4-42f9-adf4-308ddcde8dfb	9f82f663-4a3c-48e5-83d9-fc2b1a5bd69d	20e9cfca-2a1b-4cda-8fe4-46e59a8d7f10	6e2a60e2-d0fc-4eac-9f95-e1ba3650556d	f	2025-07-31 13:08:01.757438+00
79ff3c19-7e0e-4fff-81e2-1713ed79c35b	9f82f663-4a3c-48e5-83d9-fc2b1a5bd69d	d93e061b-a870-451e-8a0d-f584e3d2df8b	6e2a60e2-d0fc-4eac-9f95-e1ba3650556d	f	2025-07-31 13:09:44.279066+00
aa999493-c716-4353-9bc0-cd025d5cc467	9f82f663-4a3c-48e5-83d9-fc2b1a5bd69d	c6740995-5fb4-4f3c-9825-f94535fda107	6e2a60e2-d0fc-4eac-9f95-e1ba3650556d	f	2025-07-31 13:10:11.156576+00
ea517ab2-fa93-4187-adef-2758787883d6	9f82f663-4a3c-48e5-83d9-fc2b1a5bd69d	602cb9fa-e3af-4b8e-872f-1e5226647586	6e2a60e2-d0fc-4eac-9f95-e1ba3650556d	f	2025-07-31 13:10:29.512962+00
1c96bebf-1395-408f-8c31-90820f653a5f	9f82f663-4a3c-48e5-83d9-fc2b1a5bd69d	a738057d-6928-4070-a0a1-205f7b14cde3	6e2a60e2-d0fc-4eac-9f95-e1ba3650556d	f	2025-07-31 13:10:38.669604+00
f147c02f-f4a9-424f-9d3c-d373f22b8ffa	9f82f663-4a3c-48e5-83d9-fc2b1a5bd69d	2b26e80d-dd6b-4b3e-bec0-a840174cba2f	6e2a60e2-d0fc-4eac-9f95-e1ba3650556d	f	2025-07-31 13:12:07.86221+00
7ff40ec1-5077-44b3-bc1d-e7953920e776	9f82f663-4a3c-48e5-83d9-fc2b1a5bd69d	bf579b38-41ac-4f0c-a499-c1f2b13953d9	6e2a60e2-d0fc-4eac-9f95-e1ba3650556d	f	2025-07-31 13:13:43.377652+00
122a1ae6-af10-41e7-aca9-90d103745ba3	9f82f663-4a3c-48e5-83d9-fc2b1a5bd69d	a5bfa352-04a5-4a20-ad0d-29a40cfbc153	6e2a60e2-d0fc-4eac-9f95-e1ba3650556d	f	2025-07-31 13:26:06.028432+00
9d455da3-8ee5-4222-9285-f79aff5833ec	9f82f663-4a3c-48e5-83d9-fc2b1a5bd69d	4123b2ca-714b-43e0-a10f-09592bd7508e	6e2a60e2-d0fc-4eac-9f95-e1ba3650556d	f	2025-07-31 13:26:06.028432+00
04b9778a-8d54-43a8-b305-d8ed210b8ed3	9f82f663-4a3c-48e5-83d9-fc2b1a5bd69d	1daca593-7cfa-46c9-a36a-30f33958744a	6e2a60e2-d0fc-4eac-9f95-e1ba3650556d	f	2025-07-31 13:26:06.028432+00
de0a4fd2-d43b-4f21-a8d1-dc9b8cfbff18	d0355ee2-baba-454d-9b84-ae8ba8088c64	a560f4c3-f1f4-485a-8136-cbbb0601916d	cb9bf81d-7df4-41d5-8a90-9a223151fd4b	t	2025-07-31 14:08:32.769873+00
e4272a08-6e3f-41ca-919d-9ead0cb0e1c7	710df751-8fd6-41db-8245-8b2bf039ed85	3aba7e66-800d-4a91-9dc9-1294d83f6f94	\N	t	2025-08-06 13:04:22.938421+00
09ae7479-7fd0-43a1-9515-0ad625aefce0	710df751-8fd6-41db-8245-8b2bf039ed85	a738057d-6928-4070-a0a1-205f7b14cde3	\N	t	2025-08-06 13:04:55.378935+00
f7c90910-0d70-4fca-8b31-9771bf642833	710df751-8fd6-41db-8245-8b2bf039ed85	310688c7-3853-4ba4-9c6d-cd4677a916b6	\N	t	2025-08-06 13:05:02.689035+00
d8107bc4-7390-47e2-b480-9b9b9a2f82b6	b5780bae-a178-45db-aaea-52670c16f19f	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.001817+00
b67d46ba-20ce-4d6d-bd0d-4835a690c5e9	b5780bae-a178-45db-aaea-52670c16f19f	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.00286+00
123821d4-041b-4ba8-b28d-879da32dcb7c	b5780bae-a178-45db-aaea-52670c16f19f	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.003915+00
1dc13cf3-3b60-4612-8f22-7833d3c519d1	b5780bae-a178-45db-aaea-52670c16f19f	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.004951+00
753bf2cd-4755-4404-8a6a-51ba280d4c40	b5780bae-a178-45db-aaea-52670c16f19f	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.005899+00
c3834db3-b90c-49e9-89e0-b0f1968245b0	b5780bae-a178-45db-aaea-52670c16f19f	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.006809+00
5223210a-133b-4e3e-b804-717beb11eab2	b5780bae-a178-45db-aaea-52670c16f19f	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.007712+00
4d825307-aa5d-4e9c-b16e-e9efeac14387	b5780bae-a178-45db-aaea-52670c16f19f	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.008424+00
fb9a51dc-8b1e-4f28-bd5c-24ba30974d7b	b5780bae-a178-45db-aaea-52670c16f19f	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.009165+00
8f3f6ef5-2f8a-48c4-8cdc-e6d65f8d85db	b5780bae-a178-45db-aaea-52670c16f19f	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.009924+00
0a39e88f-60ce-43c9-bb3f-c71c816318bb	b5780bae-a178-45db-aaea-52670c16f19f	2e52a361-3ac6-472e-b052-74ab8666fb39	import-script	f	2025-08-12 15:07:01.010795+00
4ffa7383-7465-43bc-ac0a-c5e8cc5749cd	25e7dca3-4f08-414f-baae-7b868102a0a2	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.013219+00
cef272fe-8eb1-4a1a-9191-f0963405a4c1	25e7dca3-4f08-414f-baae-7b868102a0a2	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.014152+00
7ad62bf2-95be-496a-a3cb-776d7992edd2	25e7dca3-4f08-414f-baae-7b868102a0a2	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.014929+00
f4c2f519-2e6b-4b10-a205-4cc83629612b	25e7dca3-4f08-414f-baae-7b868102a0a2	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.015813+00
28c17081-4227-4f03-91c3-fb4a865746da	25e7dca3-4f08-414f-baae-7b868102a0a2	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.016508+00
6ae4e1c8-af0c-4866-9a09-60038fb7bbb8	25e7dca3-4f08-414f-baae-7b868102a0a2	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.017157+00
c21cc0e4-2ec5-4141-b641-52513367577c	25e7dca3-4f08-414f-baae-7b868102a0a2	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.017787+00
89047366-bf56-4093-8026-5bb0fc8cabb7	25e7dca3-4f08-414f-baae-7b868102a0a2	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.018563+00
821d232b-b17c-4ed1-b416-8bd2060bf127	25e7dca3-4f08-414f-baae-7b868102a0a2	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.019214+00
2628adcd-ae13-4c13-95e7-c5940690a5a4	25e7dca3-4f08-414f-baae-7b868102a0a2	38486edf-4126-4ce2-8e3a-debd7774255e	import-script	f	2025-08-12 15:07:01.020017+00
bbea644f-c562-43f4-99ae-e423cee8cf5c	d7adcc64-bfee-4ef3-999f-659dd00e2175	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.021896+00
04613a15-779d-4488-8a4f-cb8606cf1519	d7adcc64-bfee-4ef3-999f-659dd00e2175	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.022563+00
f5c3261f-0aba-4083-b6a7-0e81730c4351	d7adcc64-bfee-4ef3-999f-659dd00e2175	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.023318+00
f8bb66da-0039-4854-b8a4-b86ee4408125	d7adcc64-bfee-4ef3-999f-659dd00e2175	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.024022+00
ae97114c-5b39-4df9-9b0e-fba477867ee5	d7adcc64-bfee-4ef3-999f-659dd00e2175	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.024684+00
d6386e76-1383-4720-b4c5-87484b33af77	d7adcc64-bfee-4ef3-999f-659dd00e2175	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.025335+00
f7a8174e-13ad-4f1d-982f-a39b49db2f94	d7adcc64-bfee-4ef3-999f-659dd00e2175	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.026034+00
4a685100-9b4d-438b-bf4d-35bbfa3b8870	d7adcc64-bfee-4ef3-999f-659dd00e2175	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.02711+00
ef741a95-fcbf-4767-a2c5-7fbb25ebf29c	d7adcc64-bfee-4ef3-999f-659dd00e2175	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.02874+00
46d8a77e-715b-400f-81c3-9917f5dd1edb	d7adcc64-bfee-4ef3-999f-659dd00e2175	4ecdd3e6-396a-4291-8728-6565f772e27d	import-script	f	2025-08-12 15:07:01.029747+00
fa42d928-d8c1-4ad4-b341-3266e30ccf39	1fe135c6-0c7e-426e-9e33-b8419c772fdb	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.031936+00
5b849928-13d3-4877-926a-3c43c3e83141	1fe135c6-0c7e-426e-9e33-b8419c772fdb	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.032728+00
796f03e5-b1b9-4c82-8d8d-f62b4dcaddfc	1fe135c6-0c7e-426e-9e33-b8419c772fdb	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.033541+00
e68359b5-2ab3-49c8-9065-9955090e3507	1fe135c6-0c7e-426e-9e33-b8419c772fdb	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.034446+00
e03a3a30-db09-40ba-87ed-35e8a924069d	1fe135c6-0c7e-426e-9e33-b8419c772fdb	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.035282+00
adab3884-7987-4b77-aa41-646ee8ffcf45	1fe135c6-0c7e-426e-9e33-b8419c772fdb	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.036068+00
0d078815-900b-46a0-ba98-44f5aebadd57	1fe135c6-0c7e-426e-9e33-b8419c772fdb	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.036939+00
aa833483-e083-4afc-8d8e-647dcb8ae043	1fe135c6-0c7e-426e-9e33-b8419c772fdb	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.03767+00
ef22f0c8-a8d0-4cea-aa2a-ee8bc1729c50	1fe135c6-0c7e-426e-9e33-b8419c772fdb	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.038349+00
7402a4a5-57ae-48c0-9128-39657170e870	1fe135c6-0c7e-426e-9e33-b8419c772fdb	2b26e80d-dd6b-4b3e-bec0-a840174cba2f	import-script	f	2025-08-12 15:07:01.038988+00
080c033b-d898-458b-9404-f4276004de24	1fe135c6-0c7e-426e-9e33-b8419c772fdb	38486edf-4126-4ce2-8e3a-debd7774255e	import-script	f	2025-08-12 15:07:01.039622+00
475b6f93-364f-463e-b6a6-bd2b5f83063d	5c691a85-1101-4ea0-810f-f7fe2ed0bb90	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.041628+00
b7207155-d755-42ad-85a7-2892c5035989	5c691a85-1101-4ea0-810f-f7fe2ed0bb90	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.042475+00
ecb1aaee-f02b-405e-b787-3dafdf515f36	5c691a85-1101-4ea0-810f-f7fe2ed0bb90	a5bfa352-04a5-4a20-ad0d-29a40cfbc153	import-script	f	2025-08-12 15:07:01.043312+00
7fa60a80-0bff-496e-9d97-0c512bd9f76e	5c691a85-1101-4ea0-810f-f7fe2ed0bb90	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.044512+00
d3234f32-f477-4c1c-8e16-492ec479875e	5c691a85-1101-4ea0-810f-f7fe2ed0bb90	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.045274+00
1449995d-ba6e-4a0f-a125-f5781e82a4f8	5c691a85-1101-4ea0-810f-f7fe2ed0bb90	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.046166+00
bde5e19b-3067-4d74-a564-3bdd23517890	5c691a85-1101-4ea0-810f-f7fe2ed0bb90	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.047017+00
65305a9f-087c-42fb-91df-6557d49f8c9c	5c691a85-1101-4ea0-810f-f7fe2ed0bb90	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.04776+00
dffc4228-ed44-4b18-b423-f1672224b7e5	5c691a85-1101-4ea0-810f-f7fe2ed0bb90	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.048438+00
134cfc32-bddc-435f-b25c-0ff00aa8a842	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.050185+00
e4dd0e0f-e262-4bf5-9914-0ae2298028d6	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.050839+00
e759c075-59c5-45f7-9ec0-c3637aa3cf8d	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.051453+00
6c264abc-f92b-4ce3-8d3e-2cf84245c230	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.052198+00
749cf310-5c63-4869-ad38-52b7e754fa8e	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.052784+00
66f939d4-5086-46ef-bf04-67cd83be5458	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.053372+00
f2bc9868-8e94-4225-8ef2-645528c773d2	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.053966+00
34b95d8b-5d30-4fc0-972f-0f4636fa499a	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.054604+00
51815e74-0644-4e7f-bb09-cbea65e1dd0b	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.055315+00
94d73f54-5808-4071-98ca-c90e5fa2e886	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	38486edf-4126-4ce2-8e3a-debd7774255e	import-script	f	2025-08-12 15:07:01.056029+00
8dab5130-7708-465d-9c0d-f1e00c397708	00138327-c6fe-463b-8fce-30c76bd4c099	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.057848+00
2339daa6-aa5b-46a8-b374-a355cf13b642	00138327-c6fe-463b-8fce-30c76bd4c099	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.058651+00
4d406e2b-503a-45c5-b4a7-cebecfac3ef4	00138327-c6fe-463b-8fce-30c76bd4c099	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.059789+00
fa54ce86-1906-4959-829c-c21693a1a7ec	00138327-c6fe-463b-8fce-30c76bd4c099	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.060876+00
db17da2c-1e7f-4c20-955c-db29393953d8	00138327-c6fe-463b-8fce-30c76bd4c099	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.061933+00
43c8dc7f-cc13-4cc9-83e2-48d6be8a490f	00138327-c6fe-463b-8fce-30c76bd4c099	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.062845+00
84f87ce6-03b6-404d-80b0-09159919f04f	00138327-c6fe-463b-8fce-30c76bd4c099	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.063748+00
b6a992ff-9602-4bf8-baea-59882dce7a7c	00138327-c6fe-463b-8fce-30c76bd4c099	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.064453+00
c19ebbce-9d02-4132-9922-81f6f3e0da29	00138327-c6fe-463b-8fce-30c76bd4c099	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.065093+00
e7990e40-e6a4-44ab-84a3-921bbd8e3128	3da642a8-2604-4b14-b5c2-e54cbea674ba	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.066914+00
7a0f36cf-7d91-4e6a-9bd5-0db6868c8238	3da642a8-2604-4b14-b5c2-e54cbea674ba	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.067592+00
db37f71c-b476-4c71-b458-338fbfdaacb6	3da642a8-2604-4b14-b5c2-e54cbea674ba	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.068205+00
4dfabb8e-6136-429d-bbbb-7d65271aac53	3da642a8-2604-4b14-b5c2-e54cbea674ba	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.068777+00
57bd1059-c92b-4cd7-b481-bb583123e3bf	3da642a8-2604-4b14-b5c2-e54cbea674ba	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.06935+00
1775b268-7a80-494e-9bcb-e31d28f0f057	3da642a8-2604-4b14-b5c2-e54cbea674ba	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.069934+00
c0976137-b60c-4bfe-8565-434e40f08b29	3da642a8-2604-4b14-b5c2-e54cbea674ba	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.070519+00
2e2f81be-b760-44c6-a1a3-0eb9ef838c39	3da642a8-2604-4b14-b5c2-e54cbea674ba	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.071161+00
764552d6-c42c-45bc-9447-08ad83447bca	3da642a8-2604-4b14-b5c2-e54cbea674ba	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.071919+00
90cc6a97-4684-445d-9079-9cc17567fe6f	3da642a8-2604-4b14-b5c2-e54cbea674ba	2b26e80d-dd6b-4b3e-bec0-a840174cba2f	import-script	f	2025-08-12 15:07:01.072811+00
07f573c6-b0fe-41f7-b85a-6587fd28d032	94b7a6f8-805d-4f54-8c25-fa7fe1b0c238	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.074579+00
2d1c4a2d-480f-4d46-aeaa-6f2a92a35615	94b7a6f8-805d-4f54-8c25-fa7fe1b0c238	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.075387+00
0deb8c45-239e-496d-b225-903234fd0ed8	94b7a6f8-805d-4f54-8c25-fa7fe1b0c238	a5bfa352-04a5-4a20-ad0d-29a40cfbc153	import-script	f	2025-08-12 15:07:01.076064+00
da83943b-d682-4d0a-bd2b-b1bd07c854ca	94b7a6f8-805d-4f54-8c25-fa7fe1b0c238	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.077165+00
ef64fd04-d848-435f-bb84-d9a056a2c4ec	94b7a6f8-805d-4f54-8c25-fa7fe1b0c238	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.077896+00
c17400f4-f42a-4b15-b1f2-48bcc3dc091e	94b7a6f8-805d-4f54-8c25-fa7fe1b0c238	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.07873+00
4961c12a-753d-442f-a125-f1a59c754e7d	94b7a6f8-805d-4f54-8c25-fa7fe1b0c238	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.079481+00
dccf62b6-e7a9-4a73-95d8-61dcef01cacd	94b7a6f8-805d-4f54-8c25-fa7fe1b0c238	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.080261+00
7648aaa7-0708-4662-b139-765f5edde9ce	94b7a6f8-805d-4f54-8c25-fa7fe1b0c238	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.081026+00
6f1384b1-0fd1-4042-b880-e6c26e1865dd	94b7a6f8-805d-4f54-8c25-fa7fe1b0c238	4ecdd3e6-396a-4291-8728-6565f772e27d	import-script	f	2025-08-12 15:07:01.082012+00
5a59d4c2-f8b3-4140-9be5-9601f0f7658f	06cc6caf-acc5-497b-a68a-6a643d7cc66c	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.084318+00
921c877a-2aad-4f6a-810f-8a6b82cac3a2	06cc6caf-acc5-497b-a68a-6a643d7cc66c	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.08517+00
be5d9f69-033f-42fb-905f-71fcc8277440	06cc6caf-acc5-497b-a68a-6a643d7cc66c	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.085876+00
00077389-bd11-4373-94ea-364e600d64f1	06cc6caf-acc5-497b-a68a-6a643d7cc66c	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.08656+00
d2d44be0-2dc3-4dfe-9389-d400cb70354f	06cc6caf-acc5-497b-a68a-6a643d7cc66c	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.087317+00
3016a741-59e4-4523-809b-f38e2a3786e6	06cc6caf-acc5-497b-a68a-6a643d7cc66c	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.08816+00
0aed2158-59c4-43e2-ab3a-183e4c1fa3a2	06cc6caf-acc5-497b-a68a-6a643d7cc66c	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.088989+00
00a45e51-0e01-4b01-b4f3-ff93a67d0aa1	06cc6caf-acc5-497b-a68a-6a643d7cc66c	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.089823+00
0d55b9b3-4c73-44f7-a7c6-b979fc62299b	06cc6caf-acc5-497b-a68a-6a643d7cc66c	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.090537+00
07d97d56-d2c0-4914-8c66-0b70d1fb244d	3ce8e31c-010b-4edf-83c3-a1adc682509e	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.092256+00
38a12d4c-5b41-4746-aba6-b2b6b224833c	3ce8e31c-010b-4edf-83c3-a1adc682509e	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.092971+00
73984a57-695e-4800-8144-d2123530a9dd	3ce8e31c-010b-4edf-83c3-a1adc682509e	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.09368+00
ac0d6757-5850-4cf9-8f87-2d478e948683	3ce8e31c-010b-4edf-83c3-a1adc682509e	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.094484+00
b1163d5e-371d-4ae3-9ad8-42629a42da8d	3ce8e31c-010b-4edf-83c3-a1adc682509e	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.095282+00
25df538a-0e3b-4edf-ae5d-1b5eb7697237	3ce8e31c-010b-4edf-83c3-a1adc682509e	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.095984+00
061662b3-1e54-4b0b-97a4-d5aa5856b470	3ce8e31c-010b-4edf-83c3-a1adc682509e	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.096674+00
b3b5df77-a066-4411-b1ff-c26420b34106	3ce8e31c-010b-4edf-83c3-a1adc682509e	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.097432+00
4dbcb991-2a99-4d4a-a58b-540d9efc70ab	********-badb-4198-8762-266d4a3b216a	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.099441+00
bbb9a604-3962-47c7-914c-8c269548be4d	********-badb-4198-8762-266d4a3b216a	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.100368+00
62618cd6-2f38-41b7-9074-1d5179f292ee	********-badb-4198-8762-266d4a3b216a	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.101251+00
8314e95f-a5ce-4fbc-9ca8-15e078ca74cc	********-badb-4198-8762-266d4a3b216a	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.102089+00
87192ccf-e00d-47db-abf3-9649a49c0726	********-badb-4198-8762-266d4a3b216a	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.102794+00
12e05ae3-2837-4460-825a-2e5a583f574e	********-badb-4198-8762-266d4a3b216a	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.103488+00
019a01da-2135-453b-a043-f605e65feb60	********-badb-4198-8762-266d4a3b216a	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.104222+00
ab2a1131-a7ca-4fcb-b205-ab96b4348510	********-badb-4198-8762-266d4a3b216a	ebba64a0-0c05-4f0e-a1c2-6fd9abf0a737	import-script	f	2025-08-12 15:07:01.104947+00
da88524e-9b84-4d14-9a0e-df14143a0186	ac9d5b5e-dd53-438f-8582-1b2d36b8705f	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.10687+00
406808f2-3a37-4017-8e52-bbaaf07fe7df	ac9d5b5e-dd53-438f-8582-1b2d36b8705f	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.107646+00
290ee247-1116-44e8-a967-bb84e74b3442	ac9d5b5e-dd53-438f-8582-1b2d36b8705f	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.108267+00
c472aee6-fa46-4460-ad08-23b9e924f62a	ac9d5b5e-dd53-438f-8582-1b2d36b8705f	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.108891+00
045c5d56-a277-4ca4-a920-a878d60e5cfc	ac9d5b5e-dd53-438f-8582-1b2d36b8705f	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.109701+00
8635011d-5cd7-485b-963a-8289c88f33cc	ac9d5b5e-dd53-438f-8582-1b2d36b8705f	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.110759+00
bef16196-3ac7-4241-83f0-416ca3921f6b	ac9d5b5e-dd53-438f-8582-1b2d36b8705f	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.111796+00
36b62d8a-5762-4dec-856c-6b9c27f0e380	ac9d5b5e-dd53-438f-8582-1b2d36b8705f	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.112716+00
56e71339-d04e-4b74-b05b-25b5011ea052	b16b8f39-dc19-411c-ab31-bfff59534448	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.114373+00
b85c528e-f332-40db-97bf-188472c8111a	b16b8f39-dc19-411c-ab31-bfff59534448	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.115009+00
9fa686cd-05c6-4510-b480-7b7cb1f39e2a	b16b8f39-dc19-411c-ab31-bfff59534448	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.115871+00
e15a1ed2-b869-4e50-bc9e-27c185a1a66d	b16b8f39-dc19-411c-ab31-bfff59534448	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.116505+00
58aa9538-6e80-41ba-871b-8dd37a5bf5ab	b16b8f39-dc19-411c-ab31-bfff59534448	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.117151+00
36443236-1374-45eb-8e61-cecfc2d02cdf	b16b8f39-dc19-411c-ab31-bfff59534448	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.117924+00
b33052ad-ea57-4d62-82f0-a13c6fbc1f99	b16b8f39-dc19-411c-ab31-bfff59534448	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.118658+00
1623633f-2d06-49d1-a87c-bd9f4781db1c	b16b8f39-dc19-411c-ab31-bfff59534448	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.119349+00
6a135a28-e807-4793-bd0c-07d5592d1610	9a3e818d-bcf7-4681-83d5-e89b225832a8	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.121048+00
0cec1725-9b11-4540-b588-77c105025f89	9a3e818d-bcf7-4681-83d5-e89b225832a8	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.12167+00
67a4241b-4ee1-406e-8c29-065548b19320	9a3e818d-bcf7-4681-83d5-e89b225832a8	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.122294+00
e099fd7f-a518-4e99-bc0b-6ee5ea2671ff	9a3e818d-bcf7-4681-83d5-e89b225832a8	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.122896+00
b682f187-c90c-4590-a05d-12fb65dc98cd	9a3e818d-bcf7-4681-83d5-e89b225832a8	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.123483+00
b03f3d91-b36e-48c3-8f90-dd6d108ca447	9a3e818d-bcf7-4681-83d5-e89b225832a8	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.124085+00
0fca7df3-0b1f-4d53-91b0-efee77949b9f	9a3e818d-bcf7-4681-83d5-e89b225832a8	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.124778+00
5a1f4c62-1835-494a-961f-3dbf887bba70	9a3e818d-bcf7-4681-83d5-e89b225832a8	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.125394+00
e37b205c-cc02-4934-b7a6-c63ec3ff0b5a	9a3e818d-bcf7-4681-83d5-e89b225832a8	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.126141+00
dcf073cf-e8c4-4775-97f6-ee0a418162c4	9a3e818d-bcf7-4681-83d5-e89b225832a8	2e52a361-3ac6-472e-b052-74ab8666fb39	import-script	f	2025-08-12 15:07:01.127004+00
fd2c4056-85a2-4942-8e33-d3ccc996db0b	a46168a4-1aa0-4f63-98cd-0708ff01c082	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.128802+00
a1a9898d-6c5f-43ba-9064-3b73f8992981	a46168a4-1aa0-4f63-98cd-0708ff01c082	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.129464+00
46b68819-b021-429a-b3eb-48fd7777e555	a46168a4-1aa0-4f63-98cd-0708ff01c082	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.130104+00
696d4482-58c4-4da0-825d-6affe2dcc014	a46168a4-1aa0-4f63-98cd-0708ff01c082	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.130715+00
f3522a97-fa5d-4e2d-bce2-ed636938c750	a46168a4-1aa0-4f63-98cd-0708ff01c082	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.13133+00
5b8f5cf5-15a8-4a27-ac76-d4799455368b	a46168a4-1aa0-4f63-98cd-0708ff01c082	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.132113+00
32783c77-acd5-4cc5-94fe-cea87521a272	a46168a4-1aa0-4f63-98cd-0708ff01c082	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.132979+00
2195123c-a088-4900-a3f9-b73305e0f5ea	a46168a4-1aa0-4f63-98cd-0708ff01c082	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.133814+00
36edf4f5-5b37-4c15-8899-b4152e7aa933	abd65218-3a9c-4b44-9591-6176c5243664	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.135517+00
10929458-cd66-4933-989d-1d9a8aa1637d	abd65218-3a9c-4b44-9591-6176c5243664	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.13622+00
8276f819-2a00-4f45-836d-8226019011e6	abd65218-3a9c-4b44-9591-6176c5243664	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.136847+00
a4f848b5-b88d-41ed-87a9-a3f13fd0580e	abd65218-3a9c-4b44-9591-6176c5243664	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.137619+00
d5c9b662-eeb6-4838-8d0b-6b072273782e	abd65218-3a9c-4b44-9591-6176c5243664	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.13829+00
ed3ac243-0aa4-45cb-a86c-66bb44eb7501	abd65218-3a9c-4b44-9591-6176c5243664	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.139021+00
9190fc46-a1cb-4b31-8407-4add36715dd4	abd65218-3a9c-4b44-9591-6176c5243664	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.139814+00
0aab013b-47e6-4a3f-933d-fc9ada6f39a9	abd65218-3a9c-4b44-9591-6176c5243664	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.140555+00
d7d43463-8584-41e9-a62c-6066306d8165	abd65218-3a9c-4b44-9591-6176c5243664	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.141266+00
c0cce1b2-4b76-419d-bf29-10c6ef5de09e	0e50c593-a32d-4420-93ae-7e76fbfa0674	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.143019+00
e25e7e8c-1413-4cd1-bb1e-642fcb7b7ddb	0e50c593-a32d-4420-93ae-7e76fbfa0674	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.143935+00
0eb10256-eac3-4474-af37-35833ddcb837	0e50c593-a32d-4420-93ae-7e76fbfa0674	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.145073+00
217e6e2a-6723-4301-b756-c208bb6d7806	0e50c593-a32d-4420-93ae-7e76fbfa0674	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.145736+00
4d678f73-c7ec-4504-a274-c3c839befef3	0e50c593-a32d-4420-93ae-7e76fbfa0674	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.146568+00
8f92c4a3-0a4a-4e72-ab95-50e177bcf2d0	0e50c593-a32d-4420-93ae-7e76fbfa0674	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.147252+00
a6fedc61-8820-4664-93c0-52d7df43ca5d	0e50c593-a32d-4420-93ae-7e76fbfa0674	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.147918+00
db473c0f-f6b9-4cdd-bd60-42e805da3893	0e50c593-a32d-4420-93ae-7e76fbfa0674	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.148545+00
35dae3b5-dea0-4bb8-840c-11207ea8524f	0e50c593-a32d-4420-93ae-7e76fbfa0674	38486edf-4126-4ce2-8e3a-debd7774255e	import-script	f	2025-08-12 15:07:01.149166+00
eb84502c-003d-4244-8bc1-04d9a6e50355	417f26fe-f19b-4085-8445-07a7dfb60a48	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.150714+00
a65833bb-5540-4a45-a6a9-f69e233dba13	417f26fe-f19b-4085-8445-07a7dfb60a48	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.151381+00
8e460933-d13f-4aef-8b79-d7106cf9f307	417f26fe-f19b-4085-8445-07a7dfb60a48	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.152056+00
953690f4-ad62-4649-a87d-bf8ea82700f0	417f26fe-f19b-4085-8445-07a7dfb60a48	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.15271+00
86643e6e-1e9a-435f-8a47-0d45baa9cfe3	417f26fe-f19b-4085-8445-07a7dfb60a48	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.153365+00
667d4b0c-15a2-44ef-857f-8ec6c5ccf01b	417f26fe-f19b-4085-8445-07a7dfb60a48	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.154097+00
81a38f69-0f96-4c7e-b30a-e4f4ebfb1fcc	417f26fe-f19b-4085-8445-07a7dfb60a48	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.15498+00
9c8238ae-73f6-4c93-ae15-2234a3fdf180	417f26fe-f19b-4085-8445-07a7dfb60a48	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.15576+00
125e8bd2-9626-4fc8-a474-cc6bfcc5a9e9	417f26fe-f19b-4085-8445-07a7dfb60a48	2b26e80d-dd6b-4b3e-bec0-a840174cba2f	import-script	f	2025-08-12 15:07:01.15648+00
44879985-30f0-433f-954e-1e034949cc60	efd876d9-7e26-45c2-9cb5-f89006ccf201	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.158054+00
cf9fecb2-fcf1-4ef0-b0c3-ff1081f966bd	efd876d9-7e26-45c2-9cb5-f89006ccf201	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.158731+00
da7279c1-210a-4cbf-a080-6e9e72114e5c	efd876d9-7e26-45c2-9cb5-f89006ccf201	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.159381+00
87a78d07-e507-4010-88dc-57d0f143733d	efd876d9-7e26-45c2-9cb5-f89006ccf201	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.160284+00
857ad379-4410-49e8-97ef-4c65b20b8ac6	efd876d9-7e26-45c2-9cb5-f89006ccf201	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.161209+00
e0c34d99-714c-49f7-a896-00e4047d21ac	efd876d9-7e26-45c2-9cb5-f89006ccf201	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.161993+00
ec359ca2-cf4c-40f4-b6f2-efabe98ff495	efd876d9-7e26-45c2-9cb5-f89006ccf201	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.162857+00
9505ffab-512d-4c5b-819f-566641f3b458	efd876d9-7e26-45c2-9cb5-f89006ccf201	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.163504+00
b8c27ef5-c2a4-4e3a-8d1c-77b2bf038f6d	efd876d9-7e26-45c2-9cb5-f89006ccf201	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.164147+00
1e8160a6-3f4e-4777-8b16-915b4414f3a5	83c7307b-b03a-4d10-b0c8-4d3676699dd0	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.165898+00
057339dc-357d-4b8c-8d58-27ffa3473e08	83c7307b-b03a-4d10-b0c8-4d3676699dd0	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.166613+00
e66283bb-8109-4d1f-81cb-462dd8362222	83c7307b-b03a-4d10-b0c8-4d3676699dd0	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.167486+00
03ea3cba-7103-4cf8-b894-af5a1edf4af8	83c7307b-b03a-4d10-b0c8-4d3676699dd0	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.168107+00
99d7af2c-0774-4d9a-8735-d677cc366210	83c7307b-b03a-4d10-b0c8-4d3676699dd0	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.168703+00
889b523e-5e03-4c56-9ade-d9c6c8392b1a	83c7307b-b03a-4d10-b0c8-4d3676699dd0	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.169309+00
c2c0eb80-62b8-45ee-b77d-77f76f6c0ec6	83c7307b-b03a-4d10-b0c8-4d3676699dd0	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.169904+00
25f70d2b-4ea8-4747-a2ba-5f1fefcc13df	83c7307b-b03a-4d10-b0c8-4d3676699dd0	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.170608+00
9421c4aa-5008-4d93-acad-60861bf7a7e5	83c7307b-b03a-4d10-b0c8-4d3676699dd0	4ecdd3e6-396a-4291-8728-6565f772e27d	import-script	f	2025-08-12 15:07:01.171256+00
53794afe-5ffd-43dc-a840-ec96a3c9111a	8abb35a2-ac9f-4716-8f3c-d45071b48958	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.172995+00
cfbde678-8ae1-47e2-b15e-c224bd919849	8abb35a2-ac9f-4716-8f3c-d45071b48958	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.173677+00
624a88a2-f262-4b25-b55d-a9792721bf02	8abb35a2-ac9f-4716-8f3c-d45071b48958	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.174488+00
bc6e804c-bb9d-44ac-8291-0782a4af89f0	8abb35a2-ac9f-4716-8f3c-d45071b48958	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.175069+00
f2531c46-9619-4faa-90b9-2aa1e3e19966	8abb35a2-ac9f-4716-8f3c-d45071b48958	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.175651+00
5236a307-2f5c-494d-9cb0-b5ad99fc3fbb	8abb35a2-ac9f-4716-8f3c-d45071b48958	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.176389+00
e38a7049-ae3a-42c2-9fbb-698d8f3761f4	8abb35a2-ac9f-4716-8f3c-d45071b48958	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.177259+00
f2369366-e77b-4c19-976e-9b3808e53f8f	8abb35a2-ac9f-4716-8f3c-d45071b48958	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.17817+00
a2195e6e-649b-4532-a731-6f4ea66f37b2	59f19980-1594-4133-9195-baa612e3dd79	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.179773+00
9fb9c5c5-2589-4de6-9e96-61f2360bb835	59f19980-1594-4133-9195-baa612e3dd79	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.180423+00
2365c21d-be8e-44c2-8ae8-018317ddae4d	59f19980-1594-4133-9195-baa612e3dd79	a5bfa352-04a5-4a20-ad0d-29a40cfbc153	import-script	f	2025-08-12 15:07:01.181036+00
1f7c157a-6549-42b4-9b01-c0c76ab01447	59f19980-1594-4133-9195-baa612e3dd79	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.181859+00
4fdb8323-97ec-4e39-8f1e-4820392f865b	59f19980-1594-4133-9195-baa612e3dd79	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.182468+00
66efff93-44e0-4c10-898c-eb68a25b7258	59f19980-1594-4133-9195-baa612e3dd79	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.183095+00
cf7b6366-2e02-434e-872f-6880f63e4999	59f19980-1594-4133-9195-baa612e3dd79	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.183773+00
2cbe704b-1aa2-436f-80b7-dab63227ecf5	59f19980-1594-4133-9195-baa612e3dd79	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.184496+00
ac82eccf-46ed-4623-a3e1-64c556050404	dc95baae-b25a-4ef5-a400-8894b83d5158	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.185998+00
331d5bb7-5afc-4bcd-bba2-c5ec0c1c1efe	dc95baae-b25a-4ef5-a400-8894b83d5158	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.186695+00
ddfc7cb0-79f0-4c27-9410-a0e2343202c7	dc95baae-b25a-4ef5-a400-8894b83d5158	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.187397+00
739fcd5f-8cd0-4b37-b849-527d4f508a7c	dc95baae-b25a-4ef5-a400-8894b83d5158	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.188084+00
0fe01ab4-94b7-4afb-82d1-8c28a81c0f35	dc95baae-b25a-4ef5-a400-8894b83d5158	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.188773+00
f2a95b1d-b2a4-40d4-93dc-69ffc3d63f68	dc95baae-b25a-4ef5-a400-8894b83d5158	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.190096+00
5e593fdc-7c11-4802-87ff-3cd3229db0d2	dc95baae-b25a-4ef5-a400-8894b83d5158	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.190844+00
2e997731-9037-4a7a-9f7a-a3067c1a5588	dc95baae-b25a-4ef5-a400-8894b83d5158	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.191558+00
ce55736e-7938-4ae4-abdc-4277c7909605	dc95baae-b25a-4ef5-a400-8894b83d5158	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.192275+00
db0d3c3d-136e-466c-b8db-f2d519616964	dc95baae-b25a-4ef5-a400-8894b83d5158	ebba64a0-0c05-4f0e-a1c2-6fd9abf0a737	import-script	f	2025-08-12 15:07:01.193054+00
e03a085b-58ea-4604-86e6-ee3ed31490a5	bf2c20dc-63ed-4ec4-9985-43ba6e0f5daf	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.195239+00
628ac9e4-87d3-49b6-a351-bca9a6742c31	bf2c20dc-63ed-4ec4-9985-43ba6e0f5daf	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.196145+00
5dc1f544-5886-4483-b04e-a566f4611201	bf2c20dc-63ed-4ec4-9985-43ba6e0f5daf	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.196903+00
2b861e3e-4c88-4ede-bf0c-1800a2c648f6	bf2c20dc-63ed-4ec4-9985-43ba6e0f5daf	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.19761+00
a93fa53a-c94c-451b-a467-61341d4e7806	bf2c20dc-63ed-4ec4-9985-43ba6e0f5daf	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.19826+00
28bf99b4-afea-4a39-abb8-08dec7362eda	bf2c20dc-63ed-4ec4-9985-43ba6e0f5daf	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.198893+00
cd2416df-af4c-4806-9749-2a1c3daff2c9	bf2c20dc-63ed-4ec4-9985-43ba6e0f5daf	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.199657+00
b6a5b580-82fe-4766-a368-dc5c602b9600	bf2c20dc-63ed-4ec4-9985-43ba6e0f5daf	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.200419+00
7b64c79c-5a0c-4d6d-abdc-1d4a74975d5b	bf2c20dc-63ed-4ec4-9985-43ba6e0f5daf	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.201114+00
9e919c4a-9025-472a-85b1-8f25f4579ed0	25498a55-e29f-4a93-aab8-752cd1f2d5fc	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.203024+00
cbc0b02d-f60c-48ae-b39f-0bb8d20c2823	25498a55-e29f-4a93-aab8-752cd1f2d5fc	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.203725+00
81eca842-f9bc-45c9-aeea-3a5bbb0a26e1	25498a55-e29f-4a93-aab8-752cd1f2d5fc	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.204624+00
124b992c-2b6c-4dc2-b10a-8ffa266316e5	25498a55-e29f-4a93-aab8-752cd1f2d5fc	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.205252+00
963f5382-691b-44ff-ab0f-989da92d9b09	25498a55-e29f-4a93-aab8-752cd1f2d5fc	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.205866+00
e27d8cb3-b766-4fe7-a8ce-6b3ff677b8db	25498a55-e29f-4a93-aab8-752cd1f2d5fc	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.206596+00
d23a5f14-1ee4-4ed4-a989-4422479279d1	25498a55-e29f-4a93-aab8-752cd1f2d5fc	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.207315+00
cff7a7c4-e960-4451-a862-3ba063bf9811	25498a55-e29f-4a93-aab8-752cd1f2d5fc	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.208046+00
998ea5fc-cc09-4d1f-be57-83be82478a91	25498a55-e29f-4a93-aab8-752cd1f2d5fc	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.20872+00
086e261d-8e8d-43e5-89cd-89de22441e9a	0fdfd0c6-3f68-4d4d-b7c4-2973998104be	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.210541+00
ea516ecb-16f6-4838-9307-88bcbf9bb959	0fdfd0c6-3f68-4d4d-b7c4-2973998104be	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.211402+00
b86cbdb7-1cbe-49b8-bf9e-1d657ca5ff21	0fdfd0c6-3f68-4d4d-b7c4-2973998104be	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.212162+00
ddada22a-2655-4895-9b3a-8812a8ab6cdc	0fdfd0c6-3f68-4d4d-b7c4-2973998104be	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.212869+00
620a4988-6550-4609-9c1c-b9f84bea8d1e	0fdfd0c6-3f68-4d4d-b7c4-2973998104be	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.213539+00
2fb00350-e7d7-44c7-a5db-23fdff68fa5b	0fdfd0c6-3f68-4d4d-b7c4-2973998104be	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.214221+00
85968e89-9a33-49b0-bd19-15f66501700c	0fdfd0c6-3f68-4d4d-b7c4-2973998104be	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.214816+00
202134b4-987f-4ab9-90c1-074c70e8fd58	0fdfd0c6-3f68-4d4d-b7c4-2973998104be	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.215473+00
a6659b09-bb39-4529-857e-e872d9085d52	0fdfd0c6-3f68-4d4d-b7c4-2973998104be	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.21607+00
e00e303e-b5fc-4cad-acfc-91ad1843cee4	0fdfd0c6-3f68-4d4d-b7c4-2973998104be	2e52a361-3ac6-472e-b052-74ab8666fb39	import-script	f	2025-08-12 15:07:01.216678+00
ccb508d8-24f1-4848-ad7c-86b5e5029033	d3c9a4b2-2eba-49db-9b01-2fa568614b8c	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.218302+00
eed25ded-1c70-4db5-83ba-bcd1d1112e0d	d3c9a4b2-2eba-49db-9b01-2fa568614b8c	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.219095+00
bae186f7-ca85-4508-bdb9-4f89de22fb34	d3c9a4b2-2eba-49db-9b01-2fa568614b8c	a5bfa352-04a5-4a20-ad0d-29a40cfbc153	import-script	f	2025-08-12 15:07:01.219785+00
48a5161a-6e54-45b7-abcd-1a5e958ae9ab	d3c9a4b2-2eba-49db-9b01-2fa568614b8c	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.220723+00
7e8526e5-c9f6-41bb-aac4-9432eee246ba	d3c9a4b2-2eba-49db-9b01-2fa568614b8c	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.221329+00
720e844a-8348-492a-883d-f1acfd2a36fa	d3c9a4b2-2eba-49db-9b01-2fa568614b8c	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.222047+00
bd553068-6ed9-456e-a4b1-93ca28e43903	d3c9a4b2-2eba-49db-9b01-2fa568614b8c	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.222634+00
1a644f23-88e4-4680-88ce-7c9bea5dc901	d3c9a4b2-2eba-49db-9b01-2fa568614b8c	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.223275+00
6243ff25-cf58-48e4-8635-c90198afebed	d3c9a4b2-2eba-49db-9b01-2fa568614b8c	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.22403+00
2d649487-1dae-48db-914b-b6e98ab762fa	4fe21b42-ee3b-4914-9d3f-50458ac26597	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.225833+00
d01b6088-52a6-4c3c-b2ea-ba6bcefde45e	4fe21b42-ee3b-4914-9d3f-50458ac26597	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.226634+00
03da79cc-9710-4eb4-93aa-2def9e92ce89	4fe21b42-ee3b-4914-9d3f-50458ac26597	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.227526+00
06889467-eb4b-414b-8416-d08d2c41d2e7	4fe21b42-ee3b-4914-9d3f-50458ac26597	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.228372+00
0dfae2d3-be77-4d7b-9f30-7d88ce5b9004	4fe21b42-ee3b-4914-9d3f-50458ac26597	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.229141+00
4179bc4c-5043-4bf1-b6ed-c3643ef34f06	4fe21b42-ee3b-4914-9d3f-50458ac26597	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.229841+00
821e1d5a-9578-4404-8dfc-9afe01a2bccf	4fe21b42-ee3b-4914-9d3f-50458ac26597	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.230497+00
9149c794-368a-44c6-a267-f576aedf4b1d	4fe21b42-ee3b-4914-9d3f-50458ac26597	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.231108+00
8f32791f-ca07-496e-862e-0a729f1ba589	4fe21b42-ee3b-4914-9d3f-50458ac26597	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.231715+00
6365f327-9b41-46cc-a0cf-be20854c5e56	6b081fa3-3deb-4064-a681-638103a77d3c	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.233175+00
e9dffb78-e806-4c8f-a81d-2e2636a661e8	6b081fa3-3deb-4064-a681-638103a77d3c	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.233908+00
ade21372-e228-4e51-8e6a-6d167f545ef9	6b081fa3-3deb-4064-a681-638103a77d3c	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.23459+00
c8033176-6219-4ef1-bfa1-0561979c7ce0	6b081fa3-3deb-4064-a681-638103a77d3c	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.235187+00
7122d44a-e9db-411c-83cb-f85488fca724	6b081fa3-3deb-4064-a681-638103a77d3c	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.235766+00
758465b7-d33a-433a-8bf3-298a1c4cefbc	6b081fa3-3deb-4064-a681-638103a77d3c	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.236334+00
202071c9-597c-4462-9686-1238180e1ef5	6b081fa3-3deb-4064-a681-638103a77d3c	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.236942+00
b5196441-f880-4e27-9f97-4057bca571a9	6b081fa3-3deb-4064-a681-638103a77d3c	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.237585+00
2f5d43dc-b52b-4a28-9025-54bd06595bfc	6b081fa3-3deb-4064-a681-638103a77d3c	38486edf-4126-4ce2-8e3a-debd7774255e	import-script	f	2025-08-12 15:07:01.238282+00
c2bc93d7-367c-4eab-b958-e1e7b87c7d7b	8bd8f26a-b36d-4d8a-9e40-57a9711303a5	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.239688+00
94103705-f7a1-4456-bcf1-03e4428862ce	8bd8f26a-b36d-4d8a-9e40-57a9711303a5	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.240272+00
31380951-63a0-4e6b-a739-10d634acad76	8bd8f26a-b36d-4d8a-9e40-57a9711303a5	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.24106+00
90ec8c36-33c6-4ff9-96a7-d1f1c7d8236b	8bd8f26a-b36d-4d8a-9e40-57a9711303a5	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.241627+00
5b386976-5f62-472a-abee-f6ad8f374258	8bd8f26a-b36d-4d8a-9e40-57a9711303a5	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.242207+00
9ae3a052-ec9a-4bab-84f1-bca443b41edf	8bd8f26a-b36d-4d8a-9e40-57a9711303a5	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.242783+00
20ddb319-d037-4efd-9714-45379d98ea2f	8bd8f26a-b36d-4d8a-9e40-57a9711303a5	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.243549+00
a17d1455-677f-40af-aff1-f5c1c402026a	8bd8f26a-b36d-4d8a-9e40-57a9711303a5	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.244501+00
baf28931-cc8d-4a2c-ab7d-330c3dff52bc	8bd8f26a-b36d-4d8a-9e40-57a9711303a5	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.245397+00
0f9f70e2-2236-46e3-9b28-a0ea29ec6d65	8baa1ddd-faef-4602-9f1a-c25c73dff5f8	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.24726+00
3ec093fc-268e-4d11-b19e-03d19e411dad	8baa1ddd-faef-4602-9f1a-c25c73dff5f8	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.248014+00
daef82c5-5bea-4047-ba5c-d900cf4150c8	8baa1ddd-faef-4602-9f1a-c25c73dff5f8	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.248659+00
d8a93c93-25b8-4ec2-82f0-12c75ae31bfa	8baa1ddd-faef-4602-9f1a-c25c73dff5f8	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.249272+00
c85fe00e-9e82-4ced-b13c-b063a5229e08	8baa1ddd-faef-4602-9f1a-c25c73dff5f8	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.249939+00
9a16e9f4-38f6-4755-bd66-29fd81fca011	8baa1ddd-faef-4602-9f1a-c25c73dff5f8	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.25066+00
be41f68d-89d8-4207-b2f4-398460063eed	8baa1ddd-faef-4602-9f1a-c25c73dff5f8	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.251345+00
8c325449-c227-4af0-8bac-94cd38fca5a6	8baa1ddd-faef-4602-9f1a-c25c73dff5f8	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.252026+00
9879f016-2ee7-4708-8098-47c74409f5bb	8baa1ddd-faef-4602-9f1a-c25c73dff5f8	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.252712+00
40d2027c-1613-436f-9d6d-870e16edc700	c2b0593e-73f3-4970-8bb3-4a510827181a	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.254213+00
3409bdff-bff1-4118-bcd2-56d2a92986ef	c2b0593e-73f3-4970-8bb3-4a510827181a	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.254891+00
d555f98d-1174-48e9-a37a-b88553c78750	c2b0593e-73f3-4970-8bb3-4a510827181a	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.255572+00
8741b8af-bd1b-4083-8db2-1fba65e88d15	c2b0593e-73f3-4970-8bb3-4a510827181a	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.256191+00
d0f15a9c-ded3-468c-af3d-cb00fa192b43	c2b0593e-73f3-4970-8bb3-4a510827181a	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.256761+00
e1f4e70e-8420-4397-bbfd-f89c8b424eae	c2b0593e-73f3-4970-8bb3-4a510827181a	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.257369+00
a73bbd15-f4b3-46f0-952c-c5af44b88fbd	c2b0593e-73f3-4970-8bb3-4a510827181a	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.258012+00
d32b9129-a678-4a95-a55f-69ef0fa6fbe0	c2b0593e-73f3-4970-8bb3-4a510827181a	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.258632+00
b4f8ca45-c48d-4704-b0e9-f75d085d66a0	c2b0593e-73f3-4970-8bb3-4a510827181a	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.259387+00
c1aa6035-f5e1-4357-8579-dae81ae6458c	c2b0593e-73f3-4970-8bb3-4a510827181a	4ecdd3e6-396a-4291-8728-6565f772e27d	import-script	f	2025-08-12 15:07:01.260149+00
f8aedf4f-65ef-43eb-b8d3-b4f6f9c176f2	647b7f13-9573-4673-9d03-922935a9fc9f	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.26214+00
50835b6e-0b45-41a2-8baa-457e3115902e	647b7f13-9573-4673-9d03-922935a9fc9f	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.262925+00
94427d80-5cb2-471d-8d60-e5a929c56622	647b7f13-9573-4673-9d03-922935a9fc9f	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.264141+00
d958dc0b-8497-4118-b7ea-3b91e8bc51c3	647b7f13-9573-4673-9d03-922935a9fc9f	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.264867+00
64a2994b-f1bc-4a41-834d-b4fcad395090	647b7f13-9573-4673-9d03-922935a9fc9f	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.265652+00
d62f9ad2-a46e-4e9f-9b7f-a5961cdaf517	647b7f13-9573-4673-9d03-922935a9fc9f	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.266351+00
8a997d12-39ce-447b-9ed4-45b0c35fd8dc	647b7f13-9573-4673-9d03-922935a9fc9f	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.266986+00
d043c40b-f10f-41db-91e5-2436ddfec72e	647b7f13-9573-4673-9d03-922935a9fc9f	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.267635+00
88361fe9-3d62-4c37-963c-de9f01824857	b8bd7b27-b49d-4f71-baea-d3d7b43a3550	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.269402+00
9e574cc5-b579-4594-92c0-2d3792253b26	b8bd7b27-b49d-4f71-baea-d3d7b43a3550	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.270036+00
88f1ed75-6be4-4648-a34d-5b2156f633dd	b8bd7b27-b49d-4f71-baea-d3d7b43a3550	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.270869+00
58a890db-bec1-44e1-ba24-7f48d45f8e8c	b8bd7b27-b49d-4f71-baea-d3d7b43a3550	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.271674+00
18e2b6d6-7c96-4cf8-8007-27d2ec795254	b8bd7b27-b49d-4f71-baea-d3d7b43a3550	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.27251+00
eb5e58c5-5357-4c65-8cc7-a829035c09d1	b8bd7b27-b49d-4f71-baea-d3d7b43a3550	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.273247+00
de84817f-850d-4d07-820d-1c0fc8bb81e9	b8bd7b27-b49d-4f71-baea-d3d7b43a3550	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.274037+00
e809b80d-a0f8-42e4-9d17-bf175ea14f7d	b8bd7b27-b49d-4f71-baea-d3d7b43a3550	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.274733+00
8f945ae1-1b15-4304-a027-479f3f0e08dd	59c64665-a162-48c4-bcb6-927a78ed5e69	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.276627+00
39886ff8-7530-4cbe-9114-9dfc3cb2e342	59c64665-a162-48c4-bcb6-927a78ed5e69	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.27744+00
7f1fdd90-0dd4-40ef-8c16-c842cf89c8d1	59c64665-a162-48c4-bcb6-927a78ed5e69	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.27862+00
5a04d6fc-2c95-4118-a13d-dce015d34ef4	59c64665-a162-48c4-bcb6-927a78ed5e69	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.279419+00
eb8cd5b0-2790-4aef-8c8e-c5d95859a01b	59c64665-a162-48c4-bcb6-927a78ed5e69	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.280189+00
90643504-0951-4749-9d11-d4f933732e1b	59c64665-a162-48c4-bcb6-927a78ed5e69	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.28098+00
61ac10cb-1672-45f2-a7e4-e294d1974140	59c64665-a162-48c4-bcb6-927a78ed5e69	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.281773+00
e8abe3f9-d199-4a93-8950-86a19c0403ff	59c64665-a162-48c4-bcb6-927a78ed5e69	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.282497+00
3fba4c8a-789a-41a9-96a5-264a31a44bc2	59c64665-a162-48c4-bcb6-927a78ed5e69	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.283306+00
28973a86-4687-4687-9cfa-bb847086dbd9	59c64665-a162-48c4-bcb6-927a78ed5e69	ebba64a0-0c05-4f0e-a1c2-6fd9abf0a737	import-script	f	2025-08-12 15:07:01.284286+00
0c4d5734-1955-4ef3-8e69-0894071d0bff	8b73480a-face-4af2-98f3-79264cfca659	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.286146+00
a9569bfa-fb84-489f-be34-f10429c61533	8b73480a-face-4af2-98f3-79264cfca659	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.286851+00
5be32776-81dc-436f-994c-5ff635a8295d	8b73480a-face-4af2-98f3-79264cfca659	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.287616+00
1cce8c34-6d58-4dfe-9e27-8fbbafef59d6	8b73480a-face-4af2-98f3-79264cfca659	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.288255+00
0399c7f9-765b-4cc6-81d0-32fecfe23377	8b73480a-face-4af2-98f3-79264cfca659	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.288889+00
c2407792-bcc2-49b6-a720-5eed7061de07	8b73480a-face-4af2-98f3-79264cfca659	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.289533+00
8f8ee4c3-4a07-4b3e-99bc-6d5a07252739	8b73480a-face-4af2-98f3-79264cfca659	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.29021+00
fa3c73a6-bbe8-4886-bb73-9a4527096057	8b73480a-face-4af2-98f3-79264cfca659	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.290958+00
7aadead4-f769-4b02-92bf-dcbfe85ecda8	1f8509ef-6130-4303-a173-6506ec830047	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.292499+00
d3cd378d-99c9-4e51-93bf-618c4a673bde	1f8509ef-6130-4303-a173-6506ec830047	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.293223+00
a2602c1f-a56c-4697-a4b7-f36b08014887	1f8509ef-6130-4303-a173-6506ec830047	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.294016+00
5a36185b-a408-45c7-82b8-13435e4406f4	1f8509ef-6130-4303-a173-6506ec830047	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.294812+00
02838482-f7e0-47b3-afb3-b64fe203b640	1f8509ef-6130-4303-a173-6506ec830047	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.295499+00
519a0abe-3e99-48bf-a8dc-1edade2f51aa	1f8509ef-6130-4303-a173-6506ec830047	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.296128+00
fcf0d240-b8f2-4df0-b7b1-82d5e73e395b	1f8509ef-6130-4303-a173-6506ec830047	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.296727+00
53636e8f-ea85-4b9c-aa01-28211929bb0e	1f8509ef-6130-4303-a173-6506ec830047	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.297322+00
71e1add9-907f-4ddf-a2c9-4a74b8116293	5c519372-e81d-4c55-aaac-b77d113d48ca	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.298939+00
78e5793e-61d7-42ad-bd03-7836c67b8b63	5c519372-e81d-4c55-aaac-b77d113d48ca	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.2996+00
3279e2a7-f2e6-4500-bb49-3abe3cf81ce4	5c519372-e81d-4c55-aaac-b77d113d48ca	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.300458+00
8d58bab9-5e55-44f8-9a41-6773e46f2261	5c519372-e81d-4c55-aaac-b77d113d48ca	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.301035+00
ed8ff185-b96c-44fe-907f-90d6da28ac74	5c519372-e81d-4c55-aaac-b77d113d48ca	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.301633+00
806c6f56-171b-451c-a732-e84f90c66b79	5c519372-e81d-4c55-aaac-b77d113d48ca	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.302228+00
48a989fc-0ca7-4006-b119-67711c78f7a1	5c519372-e81d-4c55-aaac-b77d113d48ca	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.302917+00
58d6b5c4-ef17-4e11-a1c1-14cb98b70ed3	5c519372-e81d-4c55-aaac-b77d113d48ca	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.303545+00
faa38e0c-f7fb-435f-9e13-39d1aa5f7af6	5c519372-e81d-4c55-aaac-b77d113d48ca	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.30416+00
4fad6f4a-4e29-4f49-bbbc-3ffdf304d04e	5c519372-e81d-4c55-aaac-b77d113d48ca	2e52a361-3ac6-472e-b052-74ab8666fb39	import-script	f	2025-08-12 15:07:01.304756+00
4243551a-cfc2-4451-b070-31289c0b4d11	8aeefd4a-cc05-402c-89af-12b8783431e5	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.306144+00
6415b09d-7b7f-4d1f-a8e7-87d09735aed0	8aeefd4a-cc05-402c-89af-12b8783431e5	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.306859+00
7ec4093d-2538-4c18-bcea-3848f8ebbc1b	8aeefd4a-cc05-402c-89af-12b8783431e5	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.30787+00
2088570b-d246-4296-8692-bcc7d4ed8cbe	8aeefd4a-cc05-402c-89af-12b8783431e5	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.308509+00
25a0fffb-f2ca-418a-b25b-cfa719beccf9	8aeefd4a-cc05-402c-89af-12b8783431e5	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.309127+00
08c46d1c-ecc7-49c6-ba39-878c1892646c	8aeefd4a-cc05-402c-89af-12b8783431e5	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.309976+00
f35dcf0c-ec77-4c29-8c4a-8403520df7de	8aeefd4a-cc05-402c-89af-12b8783431e5	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.310903+00
ab34a850-7b9b-482b-81f7-e9f4e9c7f35f	8aeefd4a-cc05-402c-89af-12b8783431e5	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.311766+00
6de2237f-13e9-4488-9b7a-585d28fe622a	a2313897-00a7-4bf7-a0fd-fcaa26eff747	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.313425+00
3f93a004-6118-40c1-9d18-92a4a6c57990	a2313897-00a7-4bf7-a0fd-fcaa26eff747	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.31409+00
6a0e8b5e-8730-4e7f-8d68-3902f2f94225	a2313897-00a7-4bf7-a0fd-fcaa26eff747	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.314719+00
d2fe46f2-6ac9-4ee6-b9c0-18f4cb5691ed	a2313897-00a7-4bf7-a0fd-fcaa26eff747	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.315326+00
5b23cc44-a90f-4dde-9545-1d166f78903b	a2313897-00a7-4bf7-a0fd-fcaa26eff747	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.315918+00
20f8bdaf-5049-42cc-b06e-58a4258862f6	a2313897-00a7-4bf7-a0fd-fcaa26eff747	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.317445+00
4243e078-2142-40bd-bdb6-48cf4ba3e6e0	a2313897-00a7-4bf7-a0fd-fcaa26eff747	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.318322+00
e37679b3-57da-4d96-b573-17c20f7c96db	2e551ef6-f8cb-4601-9032-99c4289712b9	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.320115+00
239fd192-e35f-449a-9814-53df040adc48	2e551ef6-f8cb-4601-9032-99c4289712b9	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.320819+00
ac6a6c81-fa41-4635-9985-7d6e32c26940	2e551ef6-f8cb-4601-9032-99c4289712b9	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.32144+00
53aef210-72a5-4b27-807c-78127f4cec1b	2e551ef6-f8cb-4601-9032-99c4289712b9	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.322058+00
5040a239-7b15-4bcb-9fdb-2252dc9f5539	2e551ef6-f8cb-4601-9032-99c4289712b9	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.322673+00
cd8aca6a-e500-46ae-87a6-f5a373c186c9	2e551ef6-f8cb-4601-9032-99c4289712b9	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.32325+00
623b1958-ecb9-4538-9c72-2ca937a375eb	2e551ef6-f8cb-4601-9032-99c4289712b9	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.32386+00
7409ddc3-681b-45fa-8df9-36543a8a3878	2e551ef6-f8cb-4601-9032-99c4289712b9	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.324711+00
879eb3fc-5c49-46ed-a43d-a5e6c2d39914	2e551ef6-f8cb-4601-9032-99c4289712b9	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.325445+00
66400c33-4828-4451-b562-3f7c97881e5d	2e551ef6-f8cb-4601-9032-99c4289712b9	38486edf-4126-4ce2-8e3a-debd7774255e	import-script	f	2025-08-12 15:07:01.326204+00
0559f95b-93d6-4338-a1a6-a93b5c3c3c38	02836b5c-32a3-446a-b7e2-82a1ec01b8cb	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.327908+00
295bc037-5d07-40fd-8e6c-46b59ac55b42	02836b5c-32a3-446a-b7e2-82a1ec01b8cb	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.328634+00
6877f9e9-2895-4d00-9e88-4be5eef8c4dc	02836b5c-32a3-446a-b7e2-82a1ec01b8cb	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.329676+00
5c13056a-93ac-4006-ab92-a1e2afa3c682	02836b5c-32a3-446a-b7e2-82a1ec01b8cb	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.330393+00
50d946e9-8b0b-4b0c-8d68-4846869049a1	02836b5c-32a3-446a-b7e2-82a1ec01b8cb	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.331069+00
cab2fe96-ed30-454c-9027-458a7902f441	02836b5c-32a3-446a-b7e2-82a1ec01b8cb	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.331735+00
b7604816-cb05-40b8-a6fd-2b9f7c129699	02836b5c-32a3-446a-b7e2-82a1ec01b8cb	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.332445+00
3be89dc1-e4a8-45fe-8e52-fa7a9bae1bf0	02836b5c-32a3-446a-b7e2-82a1ec01b8cb	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.333082+00
1e492fda-3e15-4a56-83c8-4efad369db73	37855bda-7840-4253-a111-a337cabdcd25	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.334609+00
f80b551f-178e-4cb8-b35e-9b6b9ae9c479	37855bda-7840-4253-a111-a337cabdcd25	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.33525+00
b0472e1d-3caa-4e98-85d3-2266b42c4dac	37855bda-7840-4253-a111-a337cabdcd25	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.335854+00
17895315-65e0-4f32-aa3c-67fd7964c1ba	37855bda-7840-4253-a111-a337cabdcd25	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.336481+00
8b3e2993-42d3-44b5-b6d8-3367b69b5057	37855bda-7840-4253-a111-a337cabdcd25	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.337081+00
811560aa-8378-4706-bd06-bb8b5a81a9f9	37855bda-7840-4253-a111-a337cabdcd25	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.337731+00
2e918980-6a71-4343-b1ab-ef258fa4b04c	37855bda-7840-4253-a111-a337cabdcd25	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.33839+00
fb243b6a-a32a-49af-b23e-d3181ffff336	c6e8582e-0915-41c3-a633-ae0826192a24	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.339851+00
da2c9835-cfb4-44b8-9f94-b5248f94cacf	c6e8582e-0915-41c3-a633-ae0826192a24	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.340472+00
62ae5718-4f01-4d52-9dc6-b5fe91e81cb8	c6e8582e-0915-41c3-a633-ae0826192a24	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.34109+00
02c051c6-8c2e-472a-8736-9cce7d99bcf6	c6e8582e-0915-41c3-a633-ae0826192a24	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.341666+00
a8b8c6c8-ea7c-4ab3-bae5-ffef63c12a7e	c6e8582e-0915-41c3-a633-ae0826192a24	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.342242+00
1beaac4c-00c5-4ee9-b3a5-c752e89ec826	c6e8582e-0915-41c3-a633-ae0826192a24	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.342911+00
678758c2-1dcf-4c3d-9250-7e68993aff78	c6e8582e-0915-41c3-a633-ae0826192a24	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.343658+00
0ecd2216-ef94-4acf-ac48-1b4fef7c4f14	c6e8582e-0915-41c3-a633-ae0826192a24	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.344443+00
44da17c1-c76d-4b55-9baa-45051f7d5aa4	c6e8582e-0915-41c3-a633-ae0826192a24	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.345315+00
d501f7da-a73b-4795-b80d-30071002789b	c6e8582e-0915-41c3-a633-ae0826192a24	4ecdd3e6-396a-4291-8728-6565f772e27d	import-script	f	2025-08-12 15:07:01.346232+00
65273d8f-c41e-41c4-930a-cd3ebf58657a	832184e3-ca5f-41c8-9140-0339c333164f	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.34791+00
927c9c15-d5c7-4a70-b70a-c53c54694582	832184e3-ca5f-41c8-9140-0339c333164f	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.348565+00
930d9030-b8c1-402a-8a8f-86912f4c30d9	832184e3-ca5f-41c8-9140-0339c333164f	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.349426+00
9d78b40b-3138-4004-af40-139a004deec0	832184e3-ca5f-41c8-9140-0339c333164f	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.350023+00
3223d84a-1a00-45ab-a6a7-45b511df8c77	832184e3-ca5f-41c8-9140-0339c333164f	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.350601+00
469616b5-0863-45f7-89a7-82d7100fa714	832184e3-ca5f-41c8-9140-0339c333164f	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.351223+00
5aa7dbc1-ee84-496d-989e-58db7160e749	832184e3-ca5f-41c8-9140-0339c333164f	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.351842+00
28c355f1-8b98-4d75-9e2f-d3f8a8d3225d	832184e3-ca5f-41c8-9140-0339c333164f	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.352422+00
4d90aba5-c7a9-423e-8519-0b1c3950ab0c	f70e5abe-a9a1-4a33-95dc-2216279bda8c	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.353797+00
95d391b3-07b6-4555-8c44-6872b397871e	f70e5abe-a9a1-4a33-95dc-2216279bda8c	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.354428+00
9a73af87-2d9f-49d4-819e-00473b118d9f	f70e5abe-a9a1-4a33-95dc-2216279bda8c	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.355101+00
ecccc833-95a4-47d0-8373-1f98b18a11e7	f70e5abe-a9a1-4a33-95dc-2216279bda8c	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.355686+00
37cd8c31-cd46-4ff8-9ee1-00ed37e36b42	f70e5abe-a9a1-4a33-95dc-2216279bda8c	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.356254+00
68f0f93e-77a7-4028-9d1d-9bbd3814be4a	f70e5abe-a9a1-4a33-95dc-2216279bda8c	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.35681+00
d6bf6bfe-6493-4ca6-b1a8-4897c15e664c	f70e5abe-a9a1-4a33-95dc-2216279bda8c	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.357356+00
adf79afa-9371-475c-a0b1-3e54d8ed86f2	f70e5abe-a9a1-4a33-95dc-2216279bda8c	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.357903+00
59ad4313-2e9c-42a7-b436-fe2845c329b8	c91cf384-36cc-412f-bcab-6fda76e641c3	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.360053+00
873018a1-de94-4204-946d-7d69c449b141	c91cf384-36cc-412f-bcab-6fda76e641c3	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.36098+00
6ba2df89-15c5-4010-9d4a-df95d9e33257	c91cf384-36cc-412f-bcab-6fda76e641c3	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.362159+00
308f6279-6ecc-4c4f-847a-a43b80b5f920	c91cf384-36cc-412f-bcab-6fda76e641c3	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.362875+00
e0e9bcd4-530e-43ef-8861-0e7a81f6e028	c91cf384-36cc-412f-bcab-6fda76e641c3	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.363556+00
dbd1ab62-ed74-4aab-b5cd-24905bc7ea8e	c91cf384-36cc-412f-bcab-6fda76e641c3	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.364185+00
9f80e0b1-20f1-4ee7-a79f-b20cf5a6bc1f	c91cf384-36cc-412f-bcab-6fda76e641c3	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.364911+00
0104504a-e76b-4427-8b4c-6df278bb4901	c91cf384-36cc-412f-bcab-6fda76e641c3	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.365628+00
71bf30b9-6b6f-4159-8c0d-a7b3b79472e1	c91cf384-36cc-412f-bcab-6fda76e641c3	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.366258+00
6d675f3b-b23b-4571-93ae-41c5e40d3483	c91cf384-36cc-412f-bcab-6fda76e641c3	ebba64a0-0c05-4f0e-a1c2-6fd9abf0a737	import-script	f	2025-08-12 15:07:01.366853+00
b422671a-7507-4de5-9e70-cc244ebf6e3f	49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.368266+00
0be96035-5332-481d-b3d8-78633ad3323c	49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.36885+00
1b5d768a-b37b-4bbc-84ef-ae146a5fe3d8	49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.36942+00
471d7fdf-5357-43a8-9ad1-32da1c4d22b5	49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.36998+00
335c468a-383d-4d3e-b0f6-58d6094f1763	49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.370589+00
f12dc9ad-2038-4348-a330-ca48bf72633b	49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.371243+00
ac1c2e0b-de8b-4aaa-898c-319c89b65628	49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.37186+00
329c2e37-2ef9-486e-ba29-ee4c0f962ef7	49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.372463+00
c8a6b4a2-8cdb-463b-8066-b02e233c618a	49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.373129+00
e0b69350-db2a-4b3d-b7e5-bbf41250b006	49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	2b26e80d-dd6b-4b3e-bec0-a840174cba2f	import-script	f	2025-08-12 15:07:01.373756+00
68493989-86be-4c50-bd8e-a6f82b8516d3	456f9891-6e24-4136-8368-a6e576d2d53a	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.375096+00
420f826d-c9f2-4d7e-b6ac-818252106c46	456f9891-6e24-4136-8368-a6e576d2d53a	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.375697+00
f065b4c2-5820-4dc8-bdec-70b040591718	456f9891-6e24-4136-8368-a6e576d2d53a	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.376297+00
cd36fd3e-9f7a-4af4-bccc-b200e663315c	456f9891-6e24-4136-8368-a6e576d2d53a	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.37704+00
488f898f-d78e-4375-891d-1b3ed4af0eff	456f9891-6e24-4136-8368-a6e576d2d53a	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.37791+00
aab1ea23-d29a-4a63-9ea9-465986d6d7d5	456f9891-6e24-4136-8368-a6e576d2d53a	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.378688+00
e52640af-6f65-44ae-b233-67a42afdf133	456f9891-6e24-4136-8368-a6e576d2d53a	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.379368+00
68268cd7-3c38-4242-890e-7a1ca60ca79f	4d92ae8f-991a-4d22-90f9-aab1f6c2512f	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.381061+00
9d76855b-2f49-495a-9cf8-3e2fad4c5541	4d92ae8f-991a-4d22-90f9-aab1f6c2512f	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.381729+00
23ac8da7-7e8c-43c7-aa3b-8761c010db43	4d92ae8f-991a-4d22-90f9-aab1f6c2512f	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.38233+00
43f1374d-4959-4fb2-8f7a-21399e33887e	4d92ae8f-991a-4d22-90f9-aab1f6c2512f	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.382952+00
bcf1f87b-7f9e-4248-8e46-e172cf3ff9f4	4d92ae8f-991a-4d22-90f9-aab1f6c2512f	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.383867+00
ed0fcd47-6178-46af-bc44-06492e904d06	4d92ae8f-991a-4d22-90f9-aab1f6c2512f	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.384931+00
ff06b05a-0e2d-4388-9c86-43e5af944b58	4d92ae8f-991a-4d22-90f9-aab1f6c2512f	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.385681+00
24639f4d-79ce-43ec-bc63-9df7f87cb8b0	4d92ae8f-991a-4d22-90f9-aab1f6c2512f	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.386338+00
7f307a5d-664b-4c5b-9280-75c0d9485d81	4d92ae8f-991a-4d22-90f9-aab1f6c2512f	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.386973+00
cba5136b-f050-4fa9-91d0-878d78105c84	4d92ae8f-991a-4d22-90f9-aab1f6c2512f	2e52a361-3ac6-472e-b052-74ab8666fb39	import-script	f	2025-08-12 15:07:01.38759+00
45437eed-6127-425d-9a2c-2e3585ca6aba	b28367d9-2016-4a4b-a162-5b865cb44118	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.388998+00
771fb5ec-e50d-4e30-b7fc-fa1182762fc8	b28367d9-2016-4a4b-a162-5b865cb44118	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.389615+00
bf1a7a0d-7abf-42f1-ace1-29d92739d8d2	b28367d9-2016-4a4b-a162-5b865cb44118	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.390425+00
8e55e6f1-e763-46e6-8f60-712e204b674d	b28367d9-2016-4a4b-a162-5b865cb44118	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.390987+00
e2379f61-750b-4e20-9d82-7c5d1311081e	b28367d9-2016-4a4b-a162-5b865cb44118	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.391564+00
6b5ff0de-9eab-410c-b7e8-148df187f77d	b28367d9-2016-4a4b-a162-5b865cb44118	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.392133+00
eddbc7a3-1f63-4f4b-b567-a6bde6f2d92c	b28367d9-2016-4a4b-a162-5b865cb44118	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.392723+00
8a72dba9-4475-4483-a9f4-cbf16b984cff	b28367d9-2016-4a4b-a162-5b865cb44118	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.393456+00
b7539343-0a03-4b03-83ed-4ee2721842b3	6b09eed5-2cc0-4f8d-8f93-4d22d09ef433	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.395284+00
a7bdb6a7-f748-446f-a7ba-439ca0aa1e62	6b09eed5-2cc0-4f8d-8f93-4d22d09ef433	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.396102+00
6f8ec7b0-f9f2-4aea-b66e-7a61ff3c05be	6b09eed5-2cc0-4f8d-8f93-4d22d09ef433	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.396835+00
9eb4c381-cbea-4ae8-99cf-875a3fee6e4e	6b09eed5-2cc0-4f8d-8f93-4d22d09ef433	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.397517+00
5a0e104b-5fe7-43ca-ad4a-9766d78a22d2	6b09eed5-2cc0-4f8d-8f93-4d22d09ef433	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.398159+00
4e449f3a-9559-4bf5-8cb8-fe3ac9877218	6b09eed5-2cc0-4f8d-8f93-4d22d09ef433	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.398747+00
68b80b07-2ad4-4ed0-9d35-764a3161ff93	6b09eed5-2cc0-4f8d-8f93-4d22d09ef433	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.399339+00
734f829e-0795-4705-8ca0-526cf0cadb63	6b09eed5-2cc0-4f8d-8f93-4d22d09ef433	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.399966+00
00391afc-b45b-4604-96cb-9c3cb9d59212	6b09eed5-2cc0-4f8d-8f93-4d22d09ef433	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.400582+00
825cb012-d9f4-48da-bebe-a20e1bb3acb4	997a91da-3e54-4ae5-8b8b-7130d4de2728	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.401953+00
71e2d6f4-38f5-48e0-a549-853e1b6ef177	997a91da-3e54-4ae5-8b8b-7130d4de2728	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.402569+00
43a81ca1-8366-41d9-a5b3-bc55c6f7a1b0	997a91da-3e54-4ae5-8b8b-7130d4de2728	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.403152+00
5d1f4cad-decb-4dfa-8291-340a2f831bba	997a91da-3e54-4ae5-8b8b-7130d4de2728	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.403727+00
299a7f74-20e5-44fe-9c4e-15f20935c48f	997a91da-3e54-4ae5-8b8b-7130d4de2728	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.404312+00
b94de53f-974f-4bd7-a731-d099e09460bd	997a91da-3e54-4ae5-8b8b-7130d4de2728	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.405022+00
feda1386-464a-4237-acef-121e38c504d2	997a91da-3e54-4ae5-8b8b-7130d4de2728	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.405754+00
0e081526-5ba5-402b-8973-4960e1ab6a30	997a91da-3e54-4ae5-8b8b-7130d4de2728	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.406501+00
920daacc-4a73-425d-aa7a-263960895353	997a91da-3e54-4ae5-8b8b-7130d4de2728	38486edf-4126-4ce2-8e3a-debd7774255e	import-script	f	2025-08-12 15:07:01.407227+00
8ce88470-2faa-4b46-a96f-2ad5592eecde	601b5439-4f73-43cf-920d-f760941427d5	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.408749+00
fab7b17b-e916-4e72-b27e-f236f51ec73e	601b5439-4f73-43cf-920d-f760941427d5	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.409436+00
8e249a8f-b682-4b7d-a9b4-762dd7dc2e57	601b5439-4f73-43cf-920d-f760941427d5	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.410263+00
159e62fe-f809-4199-85c9-6c462ea28a4a	601b5439-4f73-43cf-920d-f760941427d5	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.411011+00
0f0de50d-11f5-45ca-bee9-708330c500e9	601b5439-4f73-43cf-920d-f760941427d5	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.411653+00
52b5f136-d0e8-4ce0-bb32-7af139a1a59c	601b5439-4f73-43cf-920d-f760941427d5	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.412253+00
e004b852-0dab-4c1a-9bfb-b88b6aad349f	601b5439-4f73-43cf-920d-f760941427d5	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.412834+00
c8e47904-05eb-4daa-a69b-d376acd1b0c5	601b5439-4f73-43cf-920d-f760941427d5	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.413406+00
51c9e597-a51d-494f-9688-39c4c307eae9	37441a48-1255-417f-a513-cac9b1853e23	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.414769+00
b90ce9c4-0a08-4783-a277-7cbcc5211539	37441a48-1255-417f-a513-cac9b1853e23	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.415429+00
a8e50548-36f2-41b4-9c5f-d8b421885481	37441a48-1255-417f-a513-cac9b1853e23	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.416125+00
5916cd12-b2e1-4d5a-b3dd-d5b9227bcd8b	37441a48-1255-417f-a513-cac9b1853e23	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.416806+00
84964fba-c80d-4fcb-9ff1-246d98cdc8b6	37441a48-1255-417f-a513-cac9b1853e23	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.417436+00
8e7252ac-275e-48ad-9467-e5b5a8aa108e	37441a48-1255-417f-a513-cac9b1853e23	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.41807+00
2316f884-d049-4555-a4bd-564e60df29d3	37441a48-1255-417f-a513-cac9b1853e23	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.418732+00
b92e3e8a-db65-4c77-ba73-25d55fbb788d	37441a48-1255-417f-a513-cac9b1853e23	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.419495+00
14b2664f-3b7a-4eac-b2a4-aaf3c087f00f	37441a48-1255-417f-a513-cac9b1853e23	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.420648+00
2276e071-95b5-41ce-93d6-fcbc0ffce460	b12de1f1-114b-4e30-b6aa-432e5398c97b	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.424422+00
d49a31f6-fac4-464e-918e-090d1c612612	b12de1f1-114b-4e30-b6aa-432e5398c97b	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.425445+00
66c505c1-6b44-45cd-8c09-0e9fc572a5e8	b12de1f1-114b-4e30-b6aa-432e5398c97b	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.426248+00
35c3a439-0249-421b-8059-f2acee385868	b12de1f1-114b-4e30-b6aa-432e5398c97b	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.427427+00
9d4efc17-2da6-4e78-b8ec-eee92f770bca	b12de1f1-114b-4e30-b6aa-432e5398c97b	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.428498+00
1476bae1-c253-4e69-a2e2-78774ae2507f	b12de1f1-114b-4e30-b6aa-432e5398c97b	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.429379+00
210ae4ec-d428-49a2-866f-6d9aaa77658c	b12de1f1-114b-4e30-b6aa-432e5398c97b	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.430259+00
a97afd9b-c202-4e83-8896-55872c529e9b	b12de1f1-114b-4e30-b6aa-432e5398c97b	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.431159+00
27dfea2c-cd0c-4d22-8cf5-62d57aaaaaf5	b12de1f1-114b-4e30-b6aa-432e5398c97b	4ecdd3e6-396a-4291-8728-6565f772e27d	import-script	f	2025-08-12 15:07:01.4321+00
07e163b6-5157-408d-b5a8-5eea35ed1585	674b216a-eb1b-4a8f-9543-495009109b70	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.434818+00
d786ba0d-a751-4817-992c-e6f7cc584897	674b216a-eb1b-4a8f-9543-495009109b70	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.435886+00
117bcb7f-f0f1-4873-bce6-19cbb90824c2	674b216a-eb1b-4a8f-9543-495009109b70	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.436929+00
6c9920c6-b422-4021-b8cc-4810385e5855	674b216a-eb1b-4a8f-9543-495009109b70	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.437944+00
243ff3d2-4712-4636-a215-219f870303b1	674b216a-eb1b-4a8f-9543-495009109b70	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.43882+00
05ffa0d9-563c-49b4-a597-5b0e8caf7b33	674b216a-eb1b-4a8f-9543-495009109b70	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.439634+00
35c069a0-c740-4afb-8c0c-d02dc6fe37be	674b216a-eb1b-4a8f-9543-495009109b70	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.440542+00
e83bc15c-8deb-45ec-81ca-e40f3b612c96	674b216a-eb1b-4a8f-9543-495009109b70	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.441657+00
8b0930df-ebc6-4ea2-a2f2-839b45496844	674b216a-eb1b-4a8f-9543-495009109b70	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.442546+00
4b86b656-882d-4013-8eae-1849f9bcc9ed	000eb8b4-345d-4e1f-ae98-69318a0e97aa	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.444755+00
b3b71dd3-1b7f-4f6e-831a-14d385e0d165	000eb8b4-345d-4e1f-ae98-69318a0e97aa	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.445832+00
70fc5c84-1860-477c-a294-899cfbc91e31	000eb8b4-345d-4e1f-ae98-69318a0e97aa	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.447169+00
de04862a-5f35-4962-a3fb-6793043d885a	000eb8b4-345d-4e1f-ae98-69318a0e97aa	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.449176+00
1d94b9d9-b603-4b67-a306-e3ca4a3a8fb1	000eb8b4-345d-4e1f-ae98-69318a0e97aa	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.451245+00
fd38c472-09be-491a-b556-d28af6020a46	000eb8b4-345d-4e1f-ae98-69318a0e97aa	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.453085+00
dd26caa6-b521-45fa-9214-824861e667c0	000eb8b4-345d-4e1f-ae98-69318a0e97aa	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.45464+00
996e40a1-e979-4ef7-a99a-1f7ed61d0576	000eb8b4-345d-4e1f-ae98-69318a0e97aa	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.455854+00
ddc160f8-44f3-436b-a5b0-7fb31557e88f	000eb8b4-345d-4e1f-ae98-69318a0e97aa	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.456864+00
0d8dfbf0-52a3-43a7-87de-e5282ae3fe20	0bf1feef-964a-4f18-9566-3fffb1de71e6	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.459317+00
027dc256-6a91-4f58-8478-a92ecd30a988	0bf1feef-964a-4f18-9566-3fffb1de71e6	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.461037+00
bc11b855-601e-440f-b00e-7f4c9fe77bb0	0bf1feef-964a-4f18-9566-3fffb1de71e6	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.464226+00
42f9230c-f6fb-4bff-a44e-30d4bbfde4f1	0bf1feef-964a-4f18-9566-3fffb1de71e6	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.465376+00
83e01bf1-6f47-42b8-a741-383191158ed8	0bf1feef-964a-4f18-9566-3fffb1de71e6	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.466384+00
b32690fc-b59b-4e37-bdcb-e153e7b9cb41	0bf1feef-964a-4f18-9566-3fffb1de71e6	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.467352+00
76741cbe-4f78-419e-9181-c79284c0024f	0bf1feef-964a-4f18-9566-3fffb1de71e6	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.468287+00
3a90ed6c-07a2-45fb-a762-a14dd7ebed47	0bf1feef-964a-4f18-9566-3fffb1de71e6	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.469186+00
1b080f06-334f-4611-afcf-d33fb7d62e92	0bf1feef-964a-4f18-9566-3fffb1de71e6	ebba64a0-0c05-4f0e-a1c2-6fd9abf0a737	import-script	f	2025-08-12 15:07:01.469929+00
616a17a6-7cbc-4506-843a-ed4e082de846	f3f2bb21-94cc-42de-abcf-123f837bcd2f	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.471758+00
9a33c0f2-dbf5-4124-85e7-54d834e73f67	f3f2bb21-94cc-42de-abcf-123f837bcd2f	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.472562+00
d47e7d20-aaf3-4a96-9d51-baedd349c5aa	f3f2bb21-94cc-42de-abcf-123f837bcd2f	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.473407+00
65260237-05ca-4fb6-bf17-cbf507bc6827	f3f2bb21-94cc-42de-abcf-123f837bcd2f	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.474299+00
4e9cc865-a7ab-4dc6-8abb-a966039d4564	f3f2bb21-94cc-42de-abcf-123f837bcd2f	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.475274+00
acf9e9ec-4876-4d61-a731-adf1eb50fa08	f3f2bb21-94cc-42de-abcf-123f837bcd2f	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.476285+00
3325c89e-f645-4838-a5b1-fc3058ffa829	f3f2bb21-94cc-42de-abcf-123f837bcd2f	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.477507+00
3ef37662-a148-4c55-8e34-533c0759ef65	f3f2bb21-94cc-42de-abcf-123f837bcd2f	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.47844+00
48cb9eb3-56cf-43e8-af10-406dda23af15	f3f2bb21-94cc-42de-abcf-123f837bcd2f	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.479523+00
afef45fd-f102-4e13-8678-ca8663f84721	004a3070-db79-47ab-8872-3c383c05f26c	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.481473+00
7c813790-6838-4f32-ad7b-2014fda0b1a5	004a3070-db79-47ab-8872-3c383c05f26c	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.482343+00
ac0ca6b8-a666-47aa-9a5d-7cae007a3016	004a3070-db79-47ab-8872-3c383c05f26c	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.483341+00
76a9c8b9-26c4-435b-8f82-7da416c9f8fb	004a3070-db79-47ab-8872-3c383c05f26c	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.484371+00
9b9ab08a-bdb1-4ca0-8b87-c7acda8932a8	004a3070-db79-47ab-8872-3c383c05f26c	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.485363+00
5f4659d5-a876-48be-9a22-7ff9fd4839b7	004a3070-db79-47ab-8872-3c383c05f26c	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.486466+00
8826b2f2-a93a-4066-9882-b2ee05352f87	004a3070-db79-47ab-8872-3c383c05f26c	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.487358+00
9cc9df57-cafb-4edf-8773-2458842ac8f4	004a3070-db79-47ab-8872-3c383c05f26c	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.488253+00
4e5d06e7-b6d2-404e-a797-1fa66236e443	dc33601c-f4a9-464e-9e78-f76e83290a5e	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.490053+00
acca7251-cde8-4ff9-b39c-a47d8b7ac34e	dc33601c-f4a9-464e-9e78-f76e83290a5e	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.490905+00
4353c484-0f07-4694-ba72-6592df50f9cb	dc33601c-f4a9-464e-9e78-f76e83290a5e	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.492011+00
d091cc50-61dc-492a-aa24-06a7bc5d99ae	dc33601c-f4a9-464e-9e78-f76e83290a5e	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.49269+00
4f77467c-2e6c-40c7-9c0c-4d1ff4743425	dc33601c-f4a9-464e-9e78-f76e83290a5e	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.493349+00
2a39e629-4641-4486-bcc0-e79fe094ba8b	dc33601c-f4a9-464e-9e78-f76e83290a5e	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.493983+00
f2f79eca-a51f-43f7-8b48-59ee3f5c5a4d	dc33601c-f4a9-464e-9e78-f76e83290a5e	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.494772+00
3c8ca4d2-f863-40b8-91a7-5d4c5468a519	dc33601c-f4a9-464e-9e78-f76e83290a5e	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.495623+00
639ff4f1-17a9-4287-81ac-33a53f97d200	dc33601c-f4a9-464e-9e78-f76e83290a5e	2e52a361-3ac6-472e-b052-74ab8666fb39	import-script	f	2025-08-12 15:07:01.496486+00
ad136c1a-fb0e-48d2-842f-62631b34e8f0	e9de360f-c376-465e-8517-dc3c37bcd9dd	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.49834+00
2b674ef3-0f7c-4ded-9bff-73b7954b102a	e9de360f-c376-465e-8517-dc3c37bcd9dd	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.499071+00
d3aed9ef-f9cc-47b4-9431-ae3cdc7a6366	e9de360f-c376-465e-8517-dc3c37bcd9dd	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.499763+00
547accaf-fc44-4f18-a814-398ab2a4f68b	e9de360f-c376-465e-8517-dc3c37bcd9dd	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.500509+00
9afec3b4-243a-4929-b20b-a0946e916a7d	e9de360f-c376-465e-8517-dc3c37bcd9dd	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.501247+00
221be26c-143f-47b2-ba41-aec3d8d5ef29	e9de360f-c376-465e-8517-dc3c37bcd9dd	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.501881+00
fc049fc2-d263-4fdd-8f46-92add7d65f8c	e9de360f-c376-465e-8517-dc3c37bcd9dd	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.502605+00
164043d5-efdf-40f4-a800-4018d4b63da7	e9de360f-c376-465e-8517-dc3c37bcd9dd	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.503324+00
54de56f0-45fd-4687-9680-0f3415aec82c	e9de360f-c376-465e-8517-dc3c37bcd9dd	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.504037+00
5e305c20-50c6-4b8e-9e35-cac1ebcd7554	bb2fd4de-5722-4e72-b9ae-19e265a8633d	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.505641+00
9f057a2d-1338-4722-a41c-4b60130f346f	bb2fd4de-5722-4e72-b9ae-19e265a8633d	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.506302+00
4668de68-5c24-4ee6-a72e-32d79eea2c3d	bb2fd4de-5722-4e72-b9ae-19e265a8633d	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.506913+00
21a4de81-419a-41c2-82c7-65b540721baf	bb2fd4de-5722-4e72-b9ae-19e265a8633d	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.50753+00
fadda94a-89b9-4331-919a-b906c4308726	bb2fd4de-5722-4e72-b9ae-19e265a8633d	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.508136+00
90c1662f-42d1-4ba2-b7e8-d84bfb91f18e	bb2fd4de-5722-4e72-b9ae-19e265a8633d	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.508875+00
79be2ed3-1de9-4005-b20d-0135d524c1ce	bb2fd4de-5722-4e72-b9ae-19e265a8633d	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.509754+00
c19cdb9f-e626-4bdb-965e-0818081200fd	bb2fd4de-5722-4e72-b9ae-19e265a8633d	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.510597+00
9c8f3632-a476-4e81-91af-e74b779a5f75	bb2fd4de-5722-4e72-b9ae-19e265a8633d	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.51136+00
6f5295f4-da9e-49ce-8fba-656cebc3789b	9bc051c4-8ea2-46a7-88bc-c81ae2b04a32	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.513538+00
b6792d5c-f575-4d28-94fe-a5d121f90606	9bc051c4-8ea2-46a7-88bc-c81ae2b04a32	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.514522+00
3df0b146-1881-4d06-b82a-8dcdfc8e2ae2	9bc051c4-8ea2-46a7-88bc-c81ae2b04a32	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.515346+00
25cc1894-31d8-46e5-828c-1f79dd3e8316	9bc051c4-8ea2-46a7-88bc-c81ae2b04a32	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.516127+00
01b6e5a1-6525-4056-b10a-653b7a5276a2	9bc051c4-8ea2-46a7-88bc-c81ae2b04a32	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.516854+00
0ab46489-4dee-4074-a9d5-47a0669e39e3	9bc051c4-8ea2-46a7-88bc-c81ae2b04a32	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.517565+00
9709b244-e33c-4d13-8cd0-59b4130e690f	9bc051c4-8ea2-46a7-88bc-c81ae2b04a32	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.518255+00
e72815d1-1b8f-40bf-9255-557225f894b5	9bc051c4-8ea2-46a7-88bc-c81ae2b04a32	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.518887+00
6fa4856f-1514-4614-bf61-65affad98cf2	9bc051c4-8ea2-46a7-88bc-c81ae2b04a32	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.519529+00
94cd013f-e05e-4b46-9461-b6e515b0aee8	9bc051c4-8ea2-46a7-88bc-c81ae2b04a32	38486edf-4126-4ce2-8e3a-debd7774255e	import-script	f	2025-08-12 15:07:01.520145+00
2bf7c864-2555-421d-a120-95f7ef74b6e6	43667920-619d-482d-8a4f-6ad4cba0558b	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.521842+00
bf434eff-e587-4e98-a884-b963eed6852f	43667920-619d-482d-8a4f-6ad4cba0558b	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.522526+00
01968018-a976-4d10-bf58-01b362882355	43667920-619d-482d-8a4f-6ad4cba0558b	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.523345+00
88a68bbd-3966-4dcd-a75f-2e5b0fe51d84	43667920-619d-482d-8a4f-6ad4cba0558b	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.524238+00
511de7b1-e21b-430b-bdeb-42024286904e	43667920-619d-482d-8a4f-6ad4cba0558b	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.525094+00
46886b31-e994-4496-86d5-fe341b87fac5	43667920-619d-482d-8a4f-6ad4cba0558b	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.525787+00
07bdd15f-d455-419c-aa97-6e3fa2b45872	43667920-619d-482d-8a4f-6ad4cba0558b	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.52655+00
1cf5d40e-ef5a-42b7-980d-ccc7ae9e16ba	43667920-619d-482d-8a4f-6ad4cba0558b	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.527348+00
95bbc5c5-892e-4df7-a5e8-b5a471d15061	43667920-619d-482d-8a4f-6ad4cba0558b	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.528145+00
58f0001e-c7ad-4a91-a269-a3d575b1337a	04e50ca5-a627-45f8-b4b7-5d3dc84a4506	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.530177+00
2fa9d932-e70e-4470-857c-33888d0db2b7	04e50ca5-a627-45f8-b4b7-5d3dc84a4506	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.530901+00
b6cae33b-2b16-44a4-a9a2-a3282c221b5f	04e50ca5-a627-45f8-b4b7-5d3dc84a4506	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.531594+00
36108d0f-9b23-49ea-9880-3b860701b472	04e50ca5-a627-45f8-b4b7-5d3dc84a4506	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.532236+00
5e558351-c0e8-4fca-8f33-d3a35d419bf2	04e50ca5-a627-45f8-b4b7-5d3dc84a4506	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.532865+00
198eec2a-f0dc-45ff-87c5-85a98db9530e	04e50ca5-a627-45f8-b4b7-5d3dc84a4506	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.53348+00
7df9add8-648f-440b-8089-41cf2df74d6f	04e50ca5-a627-45f8-b4b7-5d3dc84a4506	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.534085+00
c36e7d16-57f1-40b4-ac70-90866b27bf7e	04e50ca5-a627-45f8-b4b7-5d3dc84a4506	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.534884+00
aada14ae-310b-4ca5-a72b-953a221418aa	04e50ca5-a627-45f8-b4b7-5d3dc84a4506	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.53553+00
708a4361-a568-4383-81ee-9d65a53c33d8	e7c38b02-bbf3-4b1a-9de6-2866de2c11e5	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.536994+00
39c32985-8900-4965-9a96-a0ffebb3215a	e7c38b02-bbf3-4b1a-9de6-2866de2c11e5	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.53778+00
68d32e74-6cd2-4003-9d5f-9137c6a01133	e7c38b02-bbf3-4b1a-9de6-2866de2c11e5	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.538582+00
facae4e7-2341-4e5f-baf7-4d70ae278f44	e7c38b02-bbf3-4b1a-9de6-2866de2c11e5	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.539344+00
9f925cf6-bd26-4bce-84be-62576fc34f60	e7c38b02-bbf3-4b1a-9de6-2866de2c11e5	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.540024+00
76594bc5-b163-41da-9d2b-26953d258809	e7c38b02-bbf3-4b1a-9de6-2866de2c11e5	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.540772+00
6d243558-bb0f-4564-9302-793e382b1b56	e7c38b02-bbf3-4b1a-9de6-2866de2c11e5	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.54153+00
bbf44dd6-0fe5-49f1-afb3-54d4d038588a	e7c38b02-bbf3-4b1a-9de6-2866de2c11e5	4ecdd3e6-396a-4291-8728-6565f772e27d	import-script	f	2025-08-12 15:07:01.542286+00
39172c40-cca3-45de-a541-d0d266824cdc	74e1fc43-8fc7-4894-9ac6-319eb6ecda91	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.544103+00
e94b3681-a917-406e-9f57-479cf44209c8	74e1fc43-8fc7-4894-9ac6-319eb6ecda91	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.544973+00
b1d265e4-e2e7-4d41-9ffa-b277315b0873	74e1fc43-8fc7-4894-9ac6-319eb6ecda91	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.546256+00
7dbe14a9-fb30-4c04-9c13-1e447d81c13d	74e1fc43-8fc7-4894-9ac6-319eb6ecda91	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.54705+00
07c95588-bdb7-498f-a8bb-af569345f4ea	74e1fc43-8fc7-4894-9ac6-319eb6ecda91	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.547769+00
4ef93c7b-e72e-4bae-bf38-f797e0d7a3b6	74e1fc43-8fc7-4894-9ac6-319eb6ecda91	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.548487+00
030ed156-8b84-4b10-a9ec-cd1a34056172	74e1fc43-8fc7-4894-9ac6-319eb6ecda91	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.549171+00
4cf76646-2617-436f-9e9b-bf5ae49cee0a	74e1fc43-8fc7-4894-9ac6-319eb6ecda91	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.549891+00
a02b965d-0d7f-40d1-a9b2-4926cc2289aa	8f466bb4-6793-4ebe-a712-dd7689e1a4f8	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.551538+00
8f906782-e6ad-4368-8e36-6ff0c18d1a56	8f466bb4-6793-4ebe-a712-dd7689e1a4f8	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.552194+00
53337ba6-baf0-4f67-aa96-7dcd477e0c67	8f466bb4-6793-4ebe-a712-dd7689e1a4f8	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.552869+00
6b4dfe52-9a21-4a79-b932-e116d4e7d1c3	8f466bb4-6793-4ebe-a712-dd7689e1a4f8	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.553486+00
78793b49-a245-4876-b55e-096048ccecfb	8f466bb4-6793-4ebe-a712-dd7689e1a4f8	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.55413+00
e9aec66c-447a-4cd2-b58d-015833489497	8f466bb4-6793-4ebe-a712-dd7689e1a4f8	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.554855+00
6b67f4bd-8ab6-48be-9264-ccb765978c74	8f466bb4-6793-4ebe-a712-dd7689e1a4f8	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.555662+00
18d4cc72-1190-4af6-9826-2e288c97ee35	8f466bb4-6793-4ebe-a712-dd7689e1a4f8	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.55636+00
a553420b-53ee-476b-8edc-69aecca373db	7ab34c9b-1578-4f2e-99e3-878b5149f0c4	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.557871+00
14936410-1f0c-4e9b-8ee1-7e59fde311a9	7ab34c9b-1578-4f2e-99e3-878b5149f0c4	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.558532+00
fc831dca-e606-4b69-9c72-1550122e9f60	7ab34c9b-1578-4f2e-99e3-878b5149f0c4	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.559227+00
a1e54ef5-af0b-4901-8dfa-8d04788f1711	7ab34c9b-1578-4f2e-99e3-878b5149f0c4	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.560071+00
bdb8eb41-9726-4ba8-bda9-6da108f600bf	7ab34c9b-1578-4f2e-99e3-878b5149f0c4	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.560894+00
01676990-382d-468c-b333-eb1275d8b28a	7ab34c9b-1578-4f2e-99e3-878b5149f0c4	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.561748+00
39110934-1052-4cbe-bffb-4c08af527e8b	7ab34c9b-1578-4f2e-99e3-878b5149f0c4	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.562606+00
0115f8bd-c6cd-4e9a-a306-32b1dc67497a	7ab34c9b-1578-4f2e-99e3-878b5149f0c4	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.563451+00
35843586-5b4d-4c2c-8104-5836b1a0b4e0	7ab34c9b-1578-4f2e-99e3-878b5149f0c4	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.564252+00
7a4425a9-3551-420a-a3ae-beeec610dfbf	7ab34c9b-1578-4f2e-99e3-878b5149f0c4	ebba64a0-0c05-4f0e-a1c2-6fd9abf0a737	import-script	f	2025-08-12 15:07:01.565011+00
fbe0a0d3-4e89-4a14-b078-3d9925f820d2	0a9e280f-8e34-4ab5-ae80-88f60ecd0d77	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.56677+00
84141334-df0a-424d-ada6-0713a5666b0c	0a9e280f-8e34-4ab5-ae80-88f60ecd0d77	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.567503+00
44fa93a6-aad0-4431-94d9-02f054d07496	0a9e280f-8e34-4ab5-ae80-88f60ecd0d77	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.568799+00
59e37c5c-0207-48e0-8266-e3a0b853ed38	0a9e280f-8e34-4ab5-ae80-88f60ecd0d77	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.569678+00
7af4e79a-32d2-4153-85e2-7b43b44ad597	0a9e280f-8e34-4ab5-ae80-88f60ecd0d77	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.571243+00
2da560c9-e898-4c87-a367-604471814314	0a9e280f-8e34-4ab5-ae80-88f60ecd0d77	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.571963+00
ce3c9b39-2844-4c9d-8746-88ef59546b40	531dd3fb-eea8-4376-9c7f-f6c188810fc9	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.573539+00
70e34596-ee6d-412a-8827-7f48de490abc	531dd3fb-eea8-4376-9c7f-f6c188810fc9	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.57424+00
bf97327e-c31a-46b9-912a-9dbc08b30112	531dd3fb-eea8-4376-9c7f-f6c188810fc9	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.575436+00
70a158c3-4b1e-40fa-865e-83437bd9e5d6	531dd3fb-eea8-4376-9c7f-f6c188810fc9	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.576279+00
4bc33553-0d80-4b11-99bd-94fdebe0867c	531dd3fb-eea8-4376-9c7f-f6c188810fc9	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.57713+00
5cb98c7c-dd4d-4bc8-a855-6d7a3bd95591	531dd3fb-eea8-4376-9c7f-f6c188810fc9	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.578612+00
49c4e108-693e-40e4-9b2b-22420b89a0b3	531dd3fb-eea8-4376-9c7f-f6c188810fc9	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.57959+00
9d65a468-7d38-4198-b81e-15f45ad5a3ec	531dd3fb-eea8-4376-9c7f-f6c188810fc9	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.580429+00
ca1d1470-3625-47e5-843c-bd50b7625c90	e0c95800-8906-4535-9140-c25ee08dedd9	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.582948+00
77512d51-317d-4f3d-b70e-e3697dbf3acf	e0c95800-8906-4535-9140-c25ee08dedd9	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.583931+00
eabbdc01-af83-4412-8ad6-b988c35e4813	e0c95800-8906-4535-9140-c25ee08dedd9	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.585115+00
ef0d120d-e68a-4253-be20-7d6b3403730e	e0c95800-8906-4535-9140-c25ee08dedd9	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.585817+00
f1e72750-6853-4d8d-b1c5-0ebe0f8b2b31	e0c95800-8906-4535-9140-c25ee08dedd9	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.586598+00
2fd31e7c-a707-4201-9678-193cc95a3724	e0c95800-8906-4535-9140-c25ee08dedd9	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.587265+00
ed08c782-64c7-4fd7-8bd9-f4f1e6aeb5ec	e0c95800-8906-4535-9140-c25ee08dedd9	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.588115+00
edcf6ae1-14c0-442b-af53-b3ce51510fd6	e0c95800-8906-4535-9140-c25ee08dedd9	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.588811+00
b1e1d8f6-5f52-4003-b97b-ddb1f1e49374	e0c95800-8906-4535-9140-c25ee08dedd9	2e52a361-3ac6-472e-b052-74ab8666fb39	import-script	f	2025-08-12 15:07:01.589626+00
bff37e4f-fc4e-4b13-b476-a2433f3090c8	6b8523ca-0c34-4495-ae7c-58b7c187f8dd	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.591107+00
132d5300-e76c-41f6-b805-e24433a71fe2	6b8523ca-0c34-4495-ae7c-58b7c187f8dd	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.591739+00
2122a9f4-6eab-411a-bfb7-9ec1f34979ef	6b8523ca-0c34-4495-ae7c-58b7c187f8dd	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.592593+00
f4822e4a-1057-4cdd-8d5a-72390323638a	6b8523ca-0c34-4495-ae7c-58b7c187f8dd	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.593321+00
8a1d5870-6e2e-4d2c-bf45-ee53dc6d8ecf	6b8523ca-0c34-4495-ae7c-58b7c187f8dd	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.594429+00
684de291-354c-45b9-9e9e-dd70b1357372	6b8523ca-0c34-4495-ae7c-58b7c187f8dd	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.595394+00
25518fcd-18ed-425c-83a0-89385ecd0b68	6b8523ca-0c34-4495-ae7c-58b7c187f8dd	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.596225+00
f4117897-9ebf-425a-a9a0-5031a51fed84	6b8523ca-0c34-4495-ae7c-58b7c187f8dd	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.596935+00
6a45ffc8-7f5c-493f-ad7a-7630d2898600	dbeb0b10-a459-416d-bc6e-5bd84421b248	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.598471+00
5e14017e-b917-4f08-91af-aa7336366a92	dbeb0b10-a459-416d-bc6e-5bd84421b248	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.599109+00
c2670e51-4f90-40e6-b536-87f0702bfbc2	dbeb0b10-a459-416d-bc6e-5bd84421b248	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.599727+00
92f4dec0-5b0d-4751-a59e-ea85aac83257	dbeb0b10-a459-416d-bc6e-5bd84421b248	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.600593+00
9319e6fc-dfcb-496f-9356-2479128360ad	dbeb0b10-a459-416d-bc6e-5bd84421b248	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.601291+00
cc1b3d21-a4f8-421f-a940-401f649dd04f	dbeb0b10-a459-416d-bc6e-5bd84421b248	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.601948+00
58641b1d-b206-4b31-ab5c-d488856ac7dd	dbeb0b10-a459-416d-bc6e-5bd84421b248	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.6026+00
58c916ad-e214-4acc-867e-e8d980e15cf7	dbeb0b10-a459-416d-bc6e-5bd84421b248	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.603382+00
9c69d5ad-81b2-434d-9d4b-06545f17f4de	00ce236d-d595-4b94-885f-902083b3b055	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.605418+00
542aeaed-9090-4e04-b981-48d49acc5125	00ce236d-d595-4b94-885f-902083b3b055	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.606214+00
332ac44e-a9cb-4ba3-9565-15e44c8bb7c5	00ce236d-d595-4b94-885f-902083b3b055	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.606894+00
47463ee3-9ced-49df-8cc2-43df37803fd3	00ce236d-d595-4b94-885f-902083b3b055	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.607661+00
17ca9300-640b-4b8d-8999-a8e06c949b05	00ce236d-d595-4b94-885f-902083b3b055	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.60836+00
34a352d5-4a20-46ad-a71a-522a23e44121	00ce236d-d595-4b94-885f-902083b3b055	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.609157+00
f26e88bf-77a6-4d2a-a714-cb93d532c34f	00ce236d-d595-4b94-885f-902083b3b055	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.609869+00
2d1edcd4-fc76-4698-9661-958679873422	00ce236d-d595-4b94-885f-902083b3b055	38486edf-4126-4ce2-8e3a-debd7774255e	import-script	f	2025-08-12 15:07:01.610771+00
15cdf7a0-b163-4e27-bf86-eb38e1784ab1	efb1372f-7edf-4f62-8c3c-d8538dfeeb9b	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.612569+00
30ed2b89-afc1-4495-8965-f3a65364f3e8	efb1372f-7edf-4f62-8c3c-d8538dfeeb9b	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.613383+00
88f33016-2af4-4def-a2f8-1d97f256fc0b	efb1372f-7edf-4f62-8c3c-d8538dfeeb9b	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.614497+00
e226fef5-e3e4-4bb4-9ff9-2c24a58b9744	efb1372f-7edf-4f62-8c3c-d8538dfeeb9b	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.615146+00
236fa557-79ea-4fa6-8bfb-7484a474cedf	efb1372f-7edf-4f62-8c3c-d8538dfeeb9b	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.615834+00
71554bac-1d8f-41f4-93aa-38877689d55b	efb1372f-7edf-4f62-8c3c-d8538dfeeb9b	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.617073+00
c4c228e2-9edf-4fb0-a1ac-69dd4c4ab15a	efb1372f-7edf-4f62-8c3c-d8538dfeeb9b	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.617896+00
901447c6-8246-4744-8626-a3b7963a300f	efb1372f-7edf-4f62-8c3c-d8538dfeeb9b	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.618793+00
dbe9dfbe-18b7-4f08-8896-500eeb5477b6	9e2a3af7-68a6-42ea-916d-5a484b9bda9a	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.620437+00
5a0aa4aa-dada-4394-bcc3-2904834bf819	9e2a3af7-68a6-42ea-916d-5a484b9bda9a	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.621147+00
a3377b06-97b5-4443-beb2-89418b76473b	9e2a3af7-68a6-42ea-916d-5a484b9bda9a	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.621784+00
4634b779-bd68-44f1-86b8-0433150a8e26	9e2a3af7-68a6-42ea-916d-5a484b9bda9a	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.62249+00
568d714a-81fc-4bef-a0a4-10b1479c6ff3	9e2a3af7-68a6-42ea-916d-5a484b9bda9a	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.623291+00
e19434ef-f076-41c6-94c0-722020fc3036	9e2a3af7-68a6-42ea-916d-5a484b9bda9a	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.624195+00
72b984c1-c067-4dbf-b58b-b4503ba0c576	9e2a3af7-68a6-42ea-916d-5a484b9bda9a	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.624975+00
aa7e3007-311e-4b27-bc42-5cb6ed40689c	9e2a3af7-68a6-42ea-916d-5a484b9bda9a	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.625676+00
bd4545c1-096a-4ddd-beef-a0e7a88b64bb	a4359e21-ce42-43cd-95b5-a72df59597bb	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.627243+00
f1328f89-62bf-42f5-8bf0-bf7baac3fbee	a4359e21-ce42-43cd-95b5-a72df59597bb	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.628012+00
1461086c-e9e9-43a7-87e7-ce1ba9ab50c6	a4359e21-ce42-43cd-95b5-a72df59597bb	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.628777+00
89c433df-9c9c-45b2-be25-869a4ad64dec	a4359e21-ce42-43cd-95b5-a72df59597bb	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.629589+00
001e1968-d989-4748-8e65-b1c760ab3529	a4359e21-ce42-43cd-95b5-a72df59597bb	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.63028+00
4df96a2f-be12-41e9-a813-fa98594d1f22	a4359e21-ce42-43cd-95b5-a72df59597bb	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.631129+00
0f355681-1f32-41d7-922c-aec59ae50d79	a4359e21-ce42-43cd-95b5-a72df59597bb	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.632087+00
8566c452-d74b-41b0-b972-628e1ddf2818	a4359e21-ce42-43cd-95b5-a72df59597bb	4ecdd3e6-396a-4291-8728-6565f772e27d	import-script	f	2025-08-12 15:07:01.632879+00
79f0f3d4-82c0-4c31-b73d-62f167de1738	81319234-71ef-4f21-9d02-8bf7fbd2ecd4	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.634681+00
addc4807-2419-48d7-8e68-5365d3ae6add	81319234-71ef-4f21-9d02-8bf7fbd2ecd4	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.635411+00
7a712c37-0f7e-49cd-875b-ff2c02bc7d89	81319234-71ef-4f21-9d02-8bf7fbd2ecd4	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.636354+00
8836aa79-7de2-463f-bd35-5bcb0cd79d61	81319234-71ef-4f21-9d02-8bf7fbd2ecd4	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.636966+00
5c48d6b1-8a3d-4dad-a9ed-5ff083cd1c08	81319234-71ef-4f21-9d02-8bf7fbd2ecd4	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.637598+00
5c96ab41-b70e-4c7b-93d3-1942ae55b4e3	81319234-71ef-4f21-9d02-8bf7fbd2ecd4	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.638255+00
93ae225b-ac77-4b09-b5fa-f6b1609ec813	81319234-71ef-4f21-9d02-8bf7fbd2ecd4	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.63905+00
f8e56c7c-5aca-46f4-a8e1-aab3b5ba640e	81319234-71ef-4f21-9d02-8bf7fbd2ecd4	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.639848+00
f753a9b8-6611-4aac-9620-0876737683cd	9413d6f2-9c16-4489-881a-f5a073632d6a	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.641491+00
cb42a7d8-72a5-4e12-9da5-b63a33f987d3	9413d6f2-9c16-4489-881a-f5a073632d6a	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.642205+00
ff032d1d-8d48-44f1-b2d9-44fe8c088aba	9413d6f2-9c16-4489-881a-f5a073632d6a	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.643326+00
41aa3845-adca-477e-9731-871829ba3f6c	9413d6f2-9c16-4489-881a-f5a073632d6a	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.644053+00
c8b2f038-a64e-497f-ae6d-c39c4023d7a1	9413d6f2-9c16-4489-881a-f5a073632d6a	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.644814+00
dfd77680-4f4a-4e40-8a61-c3eab3695c22	9413d6f2-9c16-4489-881a-f5a073632d6a	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.645601+00
9c494977-29bc-4ca1-91a9-73e109b59347	9413d6f2-9c16-4489-881a-f5a073632d6a	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.64642+00
6e8a3533-5ba8-44af-be0e-05343653071a	164a02bf-58de-4d70-90ed-54a17641c1a1	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.648045+00
f96922e4-cc4a-40f8-a62c-73dff3a9e07c	164a02bf-58de-4d70-90ed-54a17641c1a1	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.648842+00
de4cde37-b4e9-4e40-b1ed-4de32cf649a7	164a02bf-58de-4d70-90ed-54a17641c1a1	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.649993+00
6d71ee1b-6e7f-4d16-a529-fb479bfbc5c8	164a02bf-58de-4d70-90ed-54a17641c1a1	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.650707+00
1d6d422a-21c3-44d1-98fd-f89d1488a493	164a02bf-58de-4d70-90ed-54a17641c1a1	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.651348+00
de43a784-156f-463a-b055-59c00aade540	164a02bf-58de-4d70-90ed-54a17641c1a1	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.652268+00
5d762c6d-6226-4648-babb-758cc84ce90c	164a02bf-58de-4d70-90ed-54a17641c1a1	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.652971+00
261aca00-04a5-4b8d-8762-cd1743723b82	164a02bf-58de-4d70-90ed-54a17641c1a1	ebba64a0-0c05-4f0e-a1c2-6fd9abf0a737	import-script	f	2025-08-12 15:07:01.653667+00
2461afab-251a-4581-97b9-7b5346c4496d	ef1015c5-0570-4c1c-a60e-fc9006b0b513	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.655185+00
31d83389-036b-4a8d-b5ea-705d7f5af663	ef1015c5-0570-4c1c-a60e-fc9006b0b513	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.655817+00
d29d4209-c241-4d59-9e06-a322eedbfa79	ef1015c5-0570-4c1c-a60e-fc9006b0b513	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.65665+00
6e90c934-3fc1-42c8-90c4-767720a02fc7	ef1015c5-0570-4c1c-a60e-fc9006b0b513	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.657234+00
8f1b87bd-4707-4cc6-8bdf-ed4ca6a184f0	ef1015c5-0570-4c1c-a60e-fc9006b0b513	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.657828+00
22301d83-22b2-416e-8706-8d60259ade25	ef1015c5-0570-4c1c-a60e-fc9006b0b513	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.658395+00
ef839566-63ca-4f27-a37c-ecde42ea9e41	ef1015c5-0570-4c1c-a60e-fc9006b0b513	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.659028+00
e6f16277-2f5b-41fe-91a7-c963d49cf5ac	b6b1eb84-5f0e-4457-a6c8-0a3743233791	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.660711+00
579f1041-95bf-4e55-8312-62a935a52b4c	b6b1eb84-5f0e-4457-a6c8-0a3743233791	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.661509+00
a6548630-08d4-40c4-ab28-d4602d5c864f	b6b1eb84-5f0e-4457-a6c8-0a3743233791	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.662305+00
afe88a68-7ff8-4c76-9ba3-4b604f2e690a	b6b1eb84-5f0e-4457-a6c8-0a3743233791	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.663037+00
bd30bc67-af9c-41c0-a2f1-c5bc2d9c5e45	b6b1eb84-5f0e-4457-a6c8-0a3743233791	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.663719+00
c2f813a7-cb9c-4b92-bf14-c201073d8be8	b6b1eb84-5f0e-4457-a6c8-0a3743233791	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.664351+00
e84f55c4-9064-4b30-a85b-6019719a6539	b6b1eb84-5f0e-4457-a6c8-0a3743233791	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.664965+00
2989faee-e90f-42d9-9479-b6ffce9554c1	4bd5e470-ca13-4562-9f0f-ed7b830576c0	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.666435+00
0e7bb894-e0ce-4aef-a779-bd364bc8b922	4bd5e470-ca13-4562-9f0f-ed7b830576c0	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.66708+00
13be280f-860e-453c-a011-e0dba5e210a7	4bd5e470-ca13-4562-9f0f-ed7b830576c0	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.66792+00
cd124055-c8bc-4ac4-940a-ff963dd35a3b	4bd5e470-ca13-4562-9f0f-ed7b830576c0	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.668528+00
1ee79491-1d78-476b-93a2-132f22850e10	4bd5e470-ca13-4562-9f0f-ed7b830576c0	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.669142+00
d26855b7-5991-4429-913e-2c485a2b089d	4bd5e470-ca13-4562-9f0f-ed7b830576c0	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.669847+00
7f5eca45-2ac8-469d-97fe-cf2750a99e58	4bd5e470-ca13-4562-9f0f-ed7b830576c0	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.670443+00
15decc4f-83fd-4b53-b30a-1ada684e8d62	4bd5e470-ca13-4562-9f0f-ed7b830576c0	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.671115+00
53e95cad-4a99-4185-878a-987e34fbff30	4bd5e470-ca13-4562-9f0f-ed7b830576c0	2e52a361-3ac6-472e-b052-74ab8666fb39	import-script	f	2025-08-12 15:07:01.671746+00
1c00e051-b527-447c-a340-e1980b5095c1	0fdc87d9-5f64-48ee-9410-8a25cf12e550	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.673104+00
ff90e954-92d7-4ad6-a6f8-d8686e110ab4	0fdc87d9-5f64-48ee-9410-8a25cf12e550	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.67372+00
ec88beaa-1f9e-49a9-ae7f-595ae20618f0	0fdc87d9-5f64-48ee-9410-8a25cf12e550	0da39480-235b-4362-b9af-0490c9c1160f	import-script	f	2025-08-12 15:07:01.674328+00
a5d02bef-4110-4a56-8f96-1a9f0c44d178	0fdc87d9-5f64-48ee-9410-8a25cf12e550	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.674916+00
b23ad8d8-3232-4894-964d-6bdc612e3f4e	0fdc87d9-5f64-48ee-9410-8a25cf12e550	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.675516+00
c94f797b-d46f-4b43-8242-ec44529adef1	0fdc87d9-5f64-48ee-9410-8a25cf12e550	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.676092+00
feb902b1-f185-417c-bae7-9122d65e753f	0fdc87d9-5f64-48ee-9410-8a25cf12e550	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.676717+00
cf4ddcea-5bfa-4e83-a83e-182937c2c799	0fdc87d9-5f64-48ee-9410-8a25cf12e550	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.677396+00
40092de2-6d00-4784-8099-6b3499a2b81e	0fdc87d9-5f64-48ee-9410-8a25cf12e550	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.678247+00
3bcd35b8-f2c9-419e-bd79-f40cf5dcc3e8	8d626f73-5fc4-46e1-92b7-91e21dd5b70c	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.680113+00
674c1845-d4a0-400c-a447-7f879b53eb93	8d626f73-5fc4-46e1-92b7-91e21dd5b70c	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.680822+00
79765027-b9ed-48f6-a873-ec1b8a9617c9	8d626f73-5fc4-46e1-92b7-91e21dd5b70c	0b83e1ea-f42c-4320-b3ad-38bc40ffd5b2	import-script	f	2025-08-12 15:07:01.681721+00
32be0325-351e-4602-b79b-3566a9d0b5fc	8d626f73-5fc4-46e1-92b7-91e21dd5b70c	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.682344+00
1398594d-ffa8-49cb-a799-2cb3129b9e51	8d626f73-5fc4-46e1-92b7-91e21dd5b70c	18ce8268-56c4-4c32-8948-7623b046d789	import-script	f	2025-08-12 15:07:01.682964+00
4a22c581-7fd1-4888-bf53-f1a343fc5e32	8d626f73-5fc4-46e1-92b7-91e21dd5b70c	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.683798+00
cc4764bd-8f9e-4763-8dbd-1d38d2fab157	8d626f73-5fc4-46e1-92b7-91e21dd5b70c	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.684575+00
e8bb042f-0c0b-43c2-8c80-e8e764d46d0e	555cf212-1e5d-4a4a-8f9a-8880b3f0e9f2	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.685998+00
0aab6506-ce76-4443-acaf-e175c0b4122f	555cf212-1e5d-4a4a-8f9a-8880b3f0e9f2	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.686843+00
83401022-88b6-4ef3-b5b8-e8bd52e11544	555cf212-1e5d-4a4a-8f9a-8880b3f0e9f2	310688c7-3853-4ba4-9c6d-cd4677a916b6	import-script	f	2025-08-12 15:07:01.687749+00
d76a3774-0909-4b0f-b63c-964d1dad769a	555cf212-1e5d-4a4a-8f9a-8880b3f0e9f2	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.688379+00
0401acd1-f78b-4309-8380-952a799b90fb	555cf212-1e5d-4a4a-8f9a-8880b3f0e9f2	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.689005+00
3891b82e-f98c-4c3a-aa3e-9a616ec50905	555cf212-1e5d-4a4a-8f9a-8880b3f0e9f2	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.689595+00
5ff2a2b2-62fb-40f5-9901-d232d26ce824	555cf212-1e5d-4a4a-8f9a-8880b3f0e9f2	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.690308+00
912c1e8e-3e0e-4896-a048-8305324383e0	555cf212-1e5d-4a4a-8f9a-8880b3f0e9f2	38486edf-4126-4ce2-8e3a-debd7774255e	import-script	f	2025-08-12 15:07:01.690994+00
627f3111-9be2-41d8-9203-3ceb1db1f4dc	5624c6d5-ca41-4386-84bb-334cb64e5f69	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.692515+00
978cb6f6-64a7-4356-b8e3-2bd295d143d0	5624c6d5-ca41-4386-84bb-334cb64e5f69	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.693204+00
ae8d8576-da5a-4b0d-a0a5-adfe564bef49	5624c6d5-ca41-4386-84bb-334cb64e5f69	268b111d-44b5-4d74-8865-230c40c1e677	import-script	f	2025-08-12 15:07:01.694145+00
cc2b9551-27d1-4f0f-a6a6-eea09505802d	5624c6d5-ca41-4386-84bb-334cb64e5f69	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.69488+00
a0aaa942-c0a9-4086-bf09-d9e352719c76	5624c6d5-ca41-4386-84bb-334cb64e5f69	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.695629+00
f558097f-5278-4c90-b0ff-bb07baa3658c	5624c6d5-ca41-4386-84bb-334cb64e5f69	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.696349+00
93b81093-510c-4c3a-b527-2a80f6dc2816	5624c6d5-ca41-4386-84bb-334cb64e5f69	bf579b38-41ac-4f0c-a499-c1f2b13953d9	import-script	f	2025-08-12 15:07:01.697127+00
169e8973-cd81-490a-9d5c-dea3a64e9e58	5624c6d5-ca41-4386-84bb-334cb64e5f69	1ae5d945-4638-4201-9590-53bb1ff76c43	import-script	f	2025-08-12 15:07:01.697829+00
224191c4-e6a0-41de-b76e-8fb08a790539	3c629f04-0cb3-44f1-ab2b-8f2481fd2a7f	4123b2ca-714b-43e0-a10f-09592bd7508e	import-script	f	2025-08-12 15:07:01.699265+00
a8f6b743-fc8b-44eb-9dc4-9dd03f3f4ee0	3c629f04-0cb3-44f1-ab2b-8f2481fd2a7f	1daca593-7cfa-46c9-a36a-30f33958744a	import-script	f	2025-08-12 15:07:01.699909+00
3ae290b2-b972-4a32-9741-121d210caaf4	3c629f04-0cb3-44f1-ab2b-8f2481fd2a7f	a5bfa352-04a5-4a20-ad0d-29a40cfbc153	import-script	f	2025-08-12 15:07:01.700569+00
40681031-d03e-4a7b-8650-457419099db5	3c629f04-0cb3-44f1-ab2b-8f2481fd2a7f	0489acf5-5801-4226-95a9-f117265a2840	import-script	f	2025-08-12 15:07:01.701161+00
733aefd6-a1b9-40c0-b581-6474f7624dbc	3c629f04-0cb3-44f1-ab2b-8f2481fd2a7f	647c2c6b-89c0-46ca-845b-9b2d43020d10	import-script	f	2025-08-12 15:07:01.701745+00
dc08b89a-2ff3-4aff-8406-c3ebc7a74530	3c629f04-0cb3-44f1-ab2b-8f2481fd2a7f	7265cf9b-3342-4072-952e-f3dca64021b0	import-script	f	2025-08-12 15:07:01.70234+00
7ce96856-f5ee-4859-b478-bfc275f6135c	3c629f04-0cb3-44f1-ab2b-8f2481fd2a7f	1ded3bb3-aaf9-41b0-8c99-b4fef7abf56b	import-script	f	2025-08-12 15:07:01.703084+00
f7a2e1c0-728f-4c7b-9482-e96aa8e15a53	3c629f04-0cb3-44f1-ab2b-8f2481fd2a7f	ca47af64-8cc1-44c4-bde6-3335c8e831ae	import-script	f	2025-08-12 15:07:01.703793+00
6f05e710-0232-4b24-9349-c2059b241e95	3c629f04-0cb3-44f1-ab2b-8f2481fd2a7f	c62d7cb4-6445-4e3e-82d7-3dbe178715c1	import-script	f	2025-08-12 15:07:01.704429+00
c7bec5b8-b83b-476e-98ba-f83bdd41972b	d0355ee2-baba-454d-9b84-ae8ba8088c64	602cb9fa-e3af-4b8e-872f-1e5226647586	cb9bf81d-7df4-41d5-8a90-9a223151fd4b	f	2025-08-24 09:56:12.227639+00
\.


--
-- Data for Name: company_locations; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.company_locations (id, company_id, location_raw, location_normalized, city, country, country_code, latitude, longitude, is_primary, is_headquarters, location_type, created_at, updated_at) FROM stdin;
7f9a432c-bad0-4d55-bd97-013940f22150	06cc6caf-acc5-497b-a68a-6a643d7cc66c	Herzogenaurach, Germany	Herzogenaurach, Germany	Herzogenaurach	Germany	DE	49.56798000	10.88565000	t	t	office	2025-08-12 14:48:14.600585+00	2025-08-12 14:48:14.600585+00
77d110f3-e35a-4fa8-8fd9-48f24143757d	3ce8e31c-010b-4edf-83c3-a1adc682509e	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	t	t	office	2025-08-12 14:48:14.697028+00	2025-08-12 14:48:14.697028+00
be0404d7-51b6-4a22-9f19-dc44b1647349	5c691a85-1101-4ea0-810f-f7fe2ed0bb90	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	office	2025-08-12 14:48:14.708632+00	2025-08-12 14:48:14.708632+00
72a9daf1-ad22-4d36-829a-170d7848f072	5c691a85-1101-4ea0-810f-f7fe2ed0bb90	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	f	f	office	2025-08-12 14:48:14.708632+00	2025-08-12 14:48:14.708632+00
66b9079e-d53d-4030-a7bd-a692f5dd8136	5c691a85-1101-4ea0-810f-f7fe2ed0bb90	Frankfurt am Main, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	f	f	office	2025-08-12 14:48:14.708632+00	2025-08-12 14:48:14.708632+00
8a7f9e48-d920-41fa-897e-117f3cc5e088	5c691a85-1101-4ea0-810f-f7fe2ed0bb90	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	f	f	office	2025-08-12 14:48:14.708632+00	2025-08-12 14:48:14.708632+00
fe4507c0-7475-4dce-a426-23f44593a93f	5c691a85-1101-4ea0-810f-f7fe2ed0bb90	Stuttgart, Germany	Stuttgart, Germany	Stuttgart	Germany	DE	48.78232000	9.17702000	f	f	office	2025-08-12 14:48:14.708632+00	2025-08-12 14:48:14.708632+00
d51c5f71-8f9b-43ea-9a58-4002a3521fef	5c691a85-1101-4ea0-810f-f7fe2ed0bb90	Düsseldorf, Germany	Düsseldorf, Germany	Düsseldorf	Germany	DE	51.22172000	6.77616000	f	f	office	2025-08-12 14:48:14.708632+00	2025-08-12 14:48:14.708632+00
0cadee06-7c1a-4757-a643-b0e34abd5c1e	5c691a85-1101-4ea0-810f-f7fe2ed0bb90	Hannover, Germany	Hannover, Germany	Hannover	Germany	DE	52.37052000	9.73322000	f	f	office	2025-08-12 14:48:14.708632+00	2025-08-12 14:48:14.708632+00
7d837eff-2d0e-4d83-84d0-4af4e8daffaf	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	Ludwigshafen, Germany	Bodman-Ludwigshafen, Germany	Bodman-Ludwigshafen	Germany	DE	47.81817000	9.05540000	t	t	office	2025-08-12 14:48:14.774137+00	2025-08-12 14:48:14.774137+00
65cfd1cd-a7f2-411f-9120-43c5bb2090ba	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	Mannheim, Germany	Mannheim, Germany	Mannheim	Germany	DE	49.48910000	8.46694000	f	f	office	2025-08-12 14:48:14.774137+00	2025-08-12 14:48:14.774137+00
fdfd62c9-6a40-42ff-a225-dfd4bf94620e	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	Lampertheim, Germany	Lampertheim, Germany	Lampertheim	Germany	DE	49.59786000	8.47250000	f	f	office	2025-08-12 14:48:14.774137+00	2025-08-12 14:48:14.774137+00
4d1eab00-d0d9-4e69-991d-a28d1a974020	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	Schwarzheide, Germany	Schwarzheide, Germany	Schwarzheide	Germany	DE	51.47671000	13.85559000	f	f	office	2025-08-12 14:48:14.774137+00	2025-08-12 14:48:14.774137+00
6145d10f-35b7-43d5-942e-d8f26a87654b	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	Lemförde, Germany	Lemförde, Germany	Lemförde	Germany	DE	52.46557000	8.37621000	f	f	office	2025-08-12 14:48:14.774137+00	2025-08-12 14:48:14.774137+00
01298566-0f00-4b49-9d25-ca83ce344d15	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	f	f	office	2025-08-12 14:48:14.774137+00	2025-08-12 14:48:14.774137+00
0edb5f71-91f3-4e4b-8222-9e6e4c70093b	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	f	f	office	2025-08-12 14:48:14.774137+00	2025-08-12 14:48:14.774137+00
c1738d29-6d51-4156-a7b0-2cb25be9c574	b6d9d93c-a9bb-41c4-be42-b1cfb89738f7	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	f	f	office	2025-08-12 14:48:14.774137+00	2025-08-12 14:48:14.774137+00
8a6837dc-7dec-4222-bd41-6d5b2427a37d	00138327-c6fe-463b-8fce-30c76bd4c099	Leverkusen, Germany	Leverkusen, Germany	Leverkusen	Germany	DE	51.03030000	6.98432000	t	t	office	2025-08-12 14:48:14.835565+00	2025-08-12 14:48:14.835565+00
acf12541-e40f-454c-a2a0-c67aabcbaae2	00138327-c6fe-463b-8fce-30c76bd4c099	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	f	f	office	2025-08-12 14:48:14.835565+00	2025-08-12 14:48:14.835565+00
22f8c932-9cc8-4db8-8eec-9db52a7c5c7a	00138327-c6fe-463b-8fce-30c76bd4c099	Wuppertal, Germany	Wuppertal, Germany	Wuppertal	Germany	DE	51.25627000	7.14816000	f	f	office	2025-08-12 14:48:14.835565+00	2025-08-12 14:48:14.835565+00
aeb7d533-5b07-43da-a00f-34daac17eef3	00138327-c6fe-463b-8fce-30c76bd4c099	Dormagen, Germany	Dormagen, Germany	Dormagen	Germany	DE	51.09683000	6.83167000	f	f	office	2025-08-12 14:48:14.835565+00	2025-08-12 14:48:14.835565+00
39d3b081-e407-452e-bd74-1ae9c4bcdf76	00138327-c6fe-463b-8fce-30c76bd4c099	Bergkamen, Germany	Bergkamen, Germany	Bergkamen	Germany	DE	51.61633000	7.64451000	f	f	office	2025-08-12 14:48:14.835565+00	2025-08-12 14:48:14.835565+00
e930c478-5fb5-4b94-a449-00c7132e8a6f	00138327-c6fe-463b-8fce-30c76bd4c099	Frankfurt am Main, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	f	f	office	2025-08-12 14:48:14.835565+00	2025-08-12 14:48:14.835565+00
c0b2261f-b63f-4680-ba4a-319113a07232	00138327-c6fe-463b-8fce-30c76bd4c099	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	f	f	office	2025-08-12 14:48:14.835565+00	2025-08-12 14:48:14.835565+00
67ba008f-c5c7-44b7-9628-7122ea3e11e0	********-badb-4198-8762-266d4a3b216a	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	t	t	office	2025-08-12 14:48:14.890756+00	2025-08-12 14:48:14.890756+00
1b7322e0-51a6-4236-a937-a61c04ebe06c	25e7dca3-4f08-414f-baae-7b868102a0a2	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	office	2025-08-12 14:48:14.898909+00	2025-08-12 14:48:14.898909+00
11080ea2-aa74-4292-9a1c-98edbbc7f6c9	25e7dca3-4f08-414f-baae-7b868102a0a2	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	f	f	office	2025-08-12 14:48:14.898909+00	2025-08-12 14:48:14.898909+00
9a4bb026-eaab-4773-88f7-b0397fb1c999	25e7dca3-4f08-414f-baae-7b868102a0a2	Dingolfing, Germany	Dingolfing, Germany	Dingolfing	Germany	DE	48.64244000	12.49283000	f	f	office	2025-08-12 14:48:14.898909+00	2025-08-12 14:48:14.898909+00
5f12f09b-f414-4215-961a-d1a39c295f3e	25e7dca3-4f08-414f-baae-7b868102a0a2	Eisenach, Germany	Eisenach, Germany	Eisenach	Germany	DE	50.98070000	10.31522000	f	f	office	2025-08-12 14:48:14.898909+00	2025-08-12 14:48:14.898909+00
403cb665-5c7a-49f8-a374-7a1ba4d0d557	25e7dca3-4f08-414f-baae-7b868102a0a2	Landshut, Germany	Landshut, Germany	Landshut	Germany	DE	48.52961000	12.16179000	f	f	office	2025-08-12 14:48:14.898909+00	2025-08-12 14:48:14.898909+00
f68fb3d9-2659-4d89-820b-f3baae215b3d	25e7dca3-4f08-414f-baae-7b868102a0a2	Leipzig, Germany	Leipzig, Germany	Leipzig	Germany	DE	51.33962000	12.37129000	f	f	office	2025-08-12 14:48:14.898909+00	2025-08-12 14:48:14.898909+00
3d69f9d5-e034-475d-8246-3e2f962811f3	25e7dca3-4f08-414f-baae-7b868102a0a2	Regensburg, Germany	Regensburg, Germany	Regensburg	Germany	DE	49.01513000	12.10161000	f	f	office	2025-08-12 14:48:14.898909+00	2025-08-12 14:48:14.898909+00
de687529-b525-4843-bde5-fda37c50138c	25e7dca3-4f08-414f-baae-7b868102a0a2	Wackersdorf, Germany	Wackersdorf, Germany	Wackersdorf	Germany	DE	49.31667000	12.18333000	f	f	office	2025-08-12 14:48:14.898909+00	2025-08-12 14:48:14.898909+00
8e6b8537-c4f5-469c-becc-d80fc76afea5	25e7dca3-4f08-414f-baae-7b868102a0a2	Ulm, Germany	Ulm, Germany	Ulm	Germany	DE	48.39841000	9.99155000	f	f	office	2025-08-12 14:48:14.898909+00	2025-08-12 14:48:14.898909+00
7fbdfdeb-783e-40e8-b525-697ef3fef908	ac9d5b5e-dd53-438f-8582-1b2d36b8705f	Essen, Germany	Essen, Germany	Essen	Germany	DE	52.72258000	7.93710000	t	t	office	2025-08-12 14:48:14.969586+00	2025-08-12 14:48:14.969586+00
eb585b78-9403-4ad6-8def-151ab7d03064	b16b8f39-dc19-411c-ab31-bfff59534448	Frankfurt am Main, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	t	t	office	2025-08-12 14:48:14.981539+00	2025-08-12 14:48:14.981539+00
7078cbb0-a382-4a96-8839-8e55863f19ff	9a3e818d-bcf7-4681-83d5-e89b225832a8	Hannover, Germany	Hannover, Germany	Hannover	Germany	DE	52.37052000	9.73322000	t	t	office	2025-08-12 14:48:14.995882+00	2025-08-12 14:48:14.995882+00
4fb69e51-1e4b-4049-85b4-6c8ec32367c4	a46168a4-1aa0-4f63-98cd-0708ff01c082	Leverkusen, Germany	Leverkusen, Germany	Leverkusen	Germany	DE	51.03030000	6.98432000	t	t	office	2025-08-12 14:48:15.008029+00	2025-08-12 14:48:15.008029+00
20fd627a-c549-454b-811a-a0f24eb07dac	abd65218-3a9c-4b44-9591-6176c5243664	Stuttgart, Germany	Stuttgart, Germany	Stuttgart	Germany	DE	48.78232000	9.17702000	t	t	office	2025-08-12 14:48:15.020027+00	2025-08-12 14:48:15.020027+00
a15a3fd6-d169-4894-8793-4cbcbc39e644	94b7a6f8-805d-4f54-8c25-fa7fe1b0c238	Frankfurt am Main, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	t	t	office	2025-08-12 14:48:15.031659+00	2025-08-12 14:48:15.031659+00
628b0430-9723-44af-9b92-88ddaef26e54	0e50c593-a32d-4420-93ae-7e76fbfa0674	Frankfurt am Main, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	t	t	office	2025-08-12 14:48:15.04739+00	2025-08-12 14:48:15.04739+00
a83d976d-0e1c-44fe-b28f-5b17b9e419e3	417f26fe-f19b-4085-8445-07a7dfb60a48	Bonn, Germany	Bonn, Germany	Bonn	Germany	DE	50.73438000	7.09549000	t	t	office	2025-08-12 14:48:15.063901+00	2025-08-12 14:48:15.063901+00
cb43582d-1d7a-4d4d-81e1-8092b6d6916f	3da642a8-2604-4b14-b5c2-e54cbea674ba	Bonn, Germany	Bonn, Germany	Bonn	Germany	DE	50.73438000	7.09549000	t	t	office	2025-08-12 14:48:15.076051+00	2025-08-12 14:48:15.076051+00
88f4eb8c-8ba5-45ed-ae1c-3c5291753dbd	3da642a8-2604-4b14-b5c2-e54cbea674ba	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	f	f	office	2025-08-12 14:48:15.076051+00	2025-08-12 14:48:15.076051+00
168f53e5-209b-493a-8147-6a51469080d5	3da642a8-2604-4b14-b5c2-e54cbea674ba	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	f	f	office	2025-08-12 14:48:15.076051+00	2025-08-12 14:48:15.076051+00
53aecfb9-5ae1-4746-8354-5ca8dbfe9b4d	3da642a8-2604-4b14-b5c2-e54cbea674ba	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	f	f	office	2025-08-12 14:48:15.076051+00	2025-08-12 14:48:15.076051+00
16ea84fa-1cf3-4759-be54-a3515b80affc	3da642a8-2604-4b14-b5c2-e54cbea674ba	Frankfurt am Main, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	f	f	office	2025-08-12 14:48:15.076051+00	2025-08-12 14:48:15.076051+00
228bd5e2-4969-4763-a71e-fc6b821bc0de	3da642a8-2604-4b14-b5c2-e54cbea674ba	Stuttgart, Germany	Stuttgart, Germany	Stuttgart	Germany	DE	48.78232000	9.17702000	f	f	office	2025-08-12 14:48:15.076051+00	2025-08-12 14:48:15.076051+00
656be126-a616-4b83-a11d-b93da161826b	3da642a8-2604-4b14-b5c2-e54cbea674ba	Düsseldorf, Germany	Düsseldorf, Germany	Düsseldorf	Germany	DE	51.22172000	6.77616000	f	f	office	2025-08-12 14:48:15.076051+00	2025-08-12 14:48:15.076051+00
69f20d5f-2930-479a-8f6e-7120e2ada056	3da642a8-2604-4b14-b5c2-e54cbea674ba	Darmstadt, Germany	Darmstadt, Germany	Darmstadt	Germany	DE	49.87167000	8.65027000	f	f	office	2025-08-12 14:48:15.076051+00	2025-08-12 14:48:15.076051+00
699cf690-cfdf-4140-a0c0-de351a234240	efd876d9-7e26-45c2-9cb5-f89006ccf201	Essen, Germany	Essen, Germany	Essen	Germany	DE	52.72258000	7.93710000	t	t	office	2025-08-12 14:48:15.174891+00	2025-08-12 14:48:15.174891+00
b2832924-a8f0-4206-a42c-feffbfd67c8f	83c7307b-b03a-4d10-b0c8-4d3676699dd0	Bad Homburg, Germany	Bad Homburg vor der Höhe, Germany	Bad Homburg vor der Höhe	Germany	DE	50.22683000	8.61816000	t	t	office	2025-08-12 14:48:15.191885+00	2025-08-12 14:48:15.191885+00
79c9ba0c-63f7-4555-8891-7264350ca43a	8abb35a2-ac9f-4716-8f3c-d45071b48958	Bad Homburg, Germany	Bad Homburg vor der Höhe, Germany	Bad Homburg vor der Höhe	Germany	DE	50.22683000	8.61816000	t	t	office	2025-08-12 14:48:15.210098+00	2025-08-12 14:48:15.210098+00
b13b2dca-01f0-4f45-bfee-ab8d1bcb1198	59f19980-1594-4133-9195-baa612e3dd79	Hannover, Germany	Hannover, Germany	Hannover	Germany	DE	52.37052000	9.73322000	t	t	office	2025-08-12 14:48:15.229817+00	2025-08-12 14:48:15.229817+00
ded35515-d70e-4525-b412-1d526d9aea57	dc95baae-b25a-4ef5-a400-8894b83d5158	Heidelberg, Germany	Heidelberg, Germany	Heidelberg	Germany	DE	49.40768000	8.69079000	t	t	office	2025-08-12 14:48:15.243974+00	2025-08-12 14:48:15.243974+00
870474e1-7af5-44a9-90e2-25cdc9d69d5d	bf2c20dc-63ed-4ec4-9985-43ba6e0f5daf	Düsseldorf, Germany	Düsseldorf, Germany	Düsseldorf	Germany	DE	51.22172000	6.77616000	t	t	office	2025-08-12 14:48:15.25656+00	2025-08-12 14:48:15.25656+00
bc130ee3-43bc-4011-abb1-66ff8bb3ac98	25498a55-e29f-4a93-aab8-752cd1f2d5fc	Neubiberg, Germany	Neubiberg, Germany	Neubiberg	Germany	DE	48.07710000	11.65812000	t	t	office	2025-08-12 14:48:15.268893+00	2025-08-12 14:48:15.268893+00
071aba47-84f2-4d2f-8152-9ab638d3bb6f	d7adcc64-bfee-4ef3-999f-659dd00e2175	Stuttgart, Germany	Stuttgart, Germany	Stuttgart	Germany	DE	48.78232000	9.17702000	t	t	office	2025-08-12 14:48:15.281139+00	2025-08-12 14:48:15.281139+00
a1ffd867-24cd-4df7-9533-f29055f36b74	d7adcc64-bfee-4ef3-999f-659dd00e2175	Sindelfingen, Germany	Sindelfingen, Germany	Sindelfingen	Germany	DE	48.70000000	9.01667000	f	f	office	2025-08-12 14:48:15.281139+00	2025-08-12 14:48:15.281139+00
28122dee-b7b8-4524-85ba-3f497aa0796a	d7adcc64-bfee-4ef3-999f-659dd00e2175	Bremen, Germany	Bremen, Germany	Bremen	Germany	DE	53.07516000	8.80777000	f	f	office	2025-08-12 14:48:15.281139+00	2025-08-12 14:48:15.281139+00
b16e874e-c174-449a-aa51-27d00a9ad7de	d7adcc64-bfee-4ef3-999f-659dd00e2175	Rastatt, Germany	Rastatt, Germany	Rastatt	Germany	DE	48.85851000	8.20965000	f	f	office	2025-08-12 14:48:15.281139+00	2025-08-12 14:48:15.281139+00
87441bda-e58f-4133-a4e2-0a2178ea9fa6	d7adcc64-bfee-4ef3-999f-659dd00e2175	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	f	f	office	2025-08-12 14:48:15.281139+00	2025-08-12 14:48:15.281139+00
bdc4cd34-e7fb-41e5-9874-e978b5aa43e6	d7adcc64-bfee-4ef3-999f-659dd00e2175	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	f	f	office	2025-08-12 14:48:15.281139+00	2025-08-12 14:48:15.281139+00
6288003f-f9d7-4acb-ad47-0f7fe213316d	d7adcc64-bfee-4ef3-999f-659dd00e2175	Düsseldorf, Germany	Düsseldorf, Germany	Düsseldorf	Germany	DE	51.22172000	6.77616000	f	f	office	2025-08-12 14:48:15.281139+00	2025-08-12 14:48:15.281139+00
cae3461c-75c8-4bfa-8cce-b7eb5ddb19ae	d7adcc64-bfee-4ef3-999f-659dd00e2175	Frankfurt am Main, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	f	f	office	2025-08-12 14:48:15.281139+00	2025-08-12 14:48:15.281139+00
4058adf7-697f-4d5c-af9e-ce9d90650e9a	a0dfffa1-90f9-496b-9a19-9d1db99d197b	Darmstadt, Germany	Darmstadt, Germany	Darmstadt	Germany	DE	49.87167000	8.65027000	t	t	office	2025-08-12 14:48:15.36187+00	2025-08-12 14:48:15.36187+00
828e4352-3069-4410-94bf-42914638c45d	0fdfd0c6-3f68-4d4d-b7c4-2973998104be	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	office	2025-08-12 14:48:15.374876+00	2025-08-12 14:48:15.374876+00
f9309be1-0399-499b-922e-11000f44c778	d3c9a4b2-2eba-49db-9b01-2fa568614b8c	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	office	2025-08-12 14:48:15.391586+00	2025-08-12 14:48:15.391586+00
6e3583f6-871b-43ba-a59b-4ba41f54d137	4fe21b42-ee3b-4914-9d3f-50458ac26597	Stuttgart, Germany	Stuttgart, Germany	Stuttgart	Germany	DE	48.78232000	9.17702000	t	t	office	2025-08-12 14:48:15.409811+00	2025-08-12 14:48:15.409811+00
bbad368f-0d73-4864-9d1a-b9ada2e8bb44	6b081fa3-3deb-4064-a681-638103a77d3c	Stuttgart, Germany	Stuttgart, Germany	Stuttgart	Germany	DE	48.78232000	9.17702000	t	t	office	2025-08-12 14:48:15.424855+00	2025-08-12 14:48:15.424855+00
e1c7f9d4-87e8-4747-b1f6-773e94841ca3	8bd8f26a-b36d-4d8a-9e40-57a9711303a5	Hilden, Germany	Hilden, Germany	Hilden	Germany	DE	51.16818000	6.93093000	t	t	office	2025-08-12 14:48:15.437767+00	2025-08-12 14:48:15.437767+00
6a638c2f-8d70-4cd6-9047-575f0eb84303	8baa1ddd-faef-4602-9f1a-c25c73dff5f8	Düsseldorf, Germany	Düsseldorf, Germany	Düsseldorf	Germany	DE	51.22172000	6.77616000	t	t	office	2025-08-12 14:48:15.446259+00	2025-08-12 14:48:15.446259+00
262dd9c3-ae63-42eb-9757-5849a4058d37	c2b0593e-73f3-4970-8bb3-4a510827181a	Essen, Germany	Essen, Germany	Essen	Germany	DE	52.72258000	7.93710000	t	t	office	2025-08-12 14:48:15.459075+00	2025-08-12 14:48:15.459075+00
31f6d8f8-97c5-4956-aad8-e39f115342a4	329ca019-134c-40ab-b40b-0d6a79b65460	Walldorf, Germany	Walldorf, Germany	Walldorf	Germany	DE	49.30637000	8.64236000	t	t	office	2025-08-12 14:48:15.472327+00	2025-08-12 14:48:15.472327+00
14382cbc-ca01-495a-afaf-fa6a37722adc	329ca019-134c-40ab-b40b-0d6a79b65460	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	f	f	office	2025-08-12 14:48:15.472327+00	2025-08-12 14:48:15.472327+00
7b97124e-e6e5-4974-91ee-9a16f4450d81	329ca019-134c-40ab-b40b-0d6a79b65460	Ratingen, Germany	Ratingen, Germany	Ratingen	Germany	DE	51.29724000	6.84929000	f	f	office	2025-08-12 14:48:15.472327+00	2025-08-12 14:48:15.472327+00
2152d2c0-8c1f-47aa-8c30-623f1ca45257	329ca019-134c-40ab-b40b-0d6a79b65460	Dresden, Germany	Dresden, Germany	Dresden	Germany	DE	51.05089000	13.73832000	f	f	office	2025-08-12 14:48:15.472327+00	2025-08-12 14:48:15.472327+00
2edd5a5c-0efb-4538-a71b-acb1de3f91af	329ca019-134c-40ab-b40b-0d6a79b65460	Eschborn, Germany	Eschborn, Germany	Eschborn	Germany	DE	50.14328000	8.57111000	f	f	office	2025-08-12 14:48:15.472327+00	2025-08-12 14:48:15.472327+00
fb017e8f-159c-4902-8ca6-e6b9a8e4209d	329ca019-134c-40ab-b40b-0d6a79b65460	Gerlingen, Germany	Gerlingen, Germany	Gerlingen	Germany	DE	48.79954000	9.06316000	f	f	office	2025-08-12 14:48:15.472327+00	2025-08-12 14:48:15.472327+00
b555942f-4184-4fcd-9b6a-d9b8b734951e	329ca019-134c-40ab-b40b-0d6a79b65460	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	f	f	office	2025-08-12 14:48:15.472327+00	2025-08-12 14:48:15.472327+00
d38a0d97-d89e-402b-a8dd-a740e9167504	329ca019-134c-40ab-b40b-0d6a79b65460	Hannover, Germany	Hannover, Germany	Hannover	Germany	DE	52.37052000	9.73322000	f	f	office	2025-08-12 14:48:15.472327+00	2025-08-12 14:48:15.472327+00
816315f2-1f1c-40c5-bebc-c98cd09c7527	329ca019-134c-40ab-b40b-0d6a79b65460	Markdorf, Germany	Markdorf, Germany	Markdorf	Germany	DE	47.71916000	9.39028000	f	f	office	2025-08-12 14:48:15.472327+00	2025-08-12 14:48:15.472327+00
9631cf51-b908-4a74-8fd9-eab31f8f1af4	329ca019-134c-40ab-b40b-0d6a79b65460	Garching bei München, Germany	Garching bei München, Germany	Garching bei München	Germany	DE	48.24896000	11.65101000	f	f	office	2025-08-12 14:48:15.472327+00	2025-08-12 14:48:15.472327+00
32b7d2fe-7204-4114-89d7-4a83d9d0ab6b	329ca019-134c-40ab-b40b-0d6a79b65460	Potsdam, Germany	Potsdam, Germany	Potsdam	Germany	DE	52.39886000	13.06566000	f	f	office	2025-08-12 14:48:15.472327+00	2025-08-12 14:48:15.472327+00
85d25c75-46a9-407f-96f5-6a41cd918faf	329ca019-134c-40ab-b40b-0d6a79b65460	Schönefeld, Germany	Schönefeld, Germany	Schönefeld	Germany	DE	52.38897000	13.50374000	f	f	office	2025-08-12 14:48:15.472327+00	2025-08-12 14:48:15.472327+00
3475c55c-c74d-4498-b7ec-90a9479cdc01	647b7f13-9573-4673-9d03-922935a9fc9f	Göttingen, Germany	Göttingen, Germany	Göttingen	Germany	DE	51.53443000	9.93228000	t	t	office	2025-08-12 14:48:15.603719+00	2025-08-12 14:48:15.603719+00
02a9a657-fa19-465e-8cfb-e03f7e4ce78c	b5780bae-a178-45db-aaea-52670c16f19f	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	office	2025-08-12 14:48:15.615934+00	2025-08-12 14:48:15.615934+00
ca9e035b-a232-4f83-b1a5-c6df00ae903e	b5780bae-a178-45db-aaea-52670c16f19f	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	f	f	office	2025-08-12 14:48:15.615934+00	2025-08-12 14:48:15.615934+00
3fdd8068-1869-430c-b402-b2293937ece1	b5780bae-a178-45db-aaea-52670c16f19f	Erlangen, Germany	Erlangen, Germany	Erlangen	Germany	DE	49.59099000	11.00783000	f	f	office	2025-08-12 14:48:15.615934+00	2025-08-12 14:48:15.615934+00
82fe4eb2-1a28-4657-9fd4-89a191d28ca0	b5780bae-a178-45db-aaea-52670c16f19f	Frankfurt am Main, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	f	f	office	2025-08-12 14:48:15.615934+00	2025-08-12 14:48:15.615934+00
9d56eb53-b91c-45e3-860d-206a7abea29c	b5780bae-a178-45db-aaea-52670c16f19f	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	f	f	office	2025-08-12 14:48:15.615934+00	2025-08-12 14:48:15.615934+00
adde938a-a5dc-48f5-a557-6564224c789a	b5780bae-a178-45db-aaea-52670c16f19f	Hannover, Germany	Hannover, Germany	Hannover	Germany	DE	52.37052000	9.73322000	f	f	office	2025-08-12 14:48:15.615934+00	2025-08-12 14:48:15.615934+00
e5da5f95-ca23-41f8-b307-60e04fbdcb0a	b5780bae-a178-45db-aaea-52670c16f19f	Stuttgart, Germany	Stuttgart, Germany	Stuttgart	Germany	DE	48.78232000	9.17702000	f	f	office	2025-08-12 14:48:15.615934+00	2025-08-12 14:48:15.615934+00
3d12d573-9e2e-42bd-8cdf-784a8d5eb6f3	b5780bae-a178-45db-aaea-52670c16f19f	Düsseldorf, Germany	Düsseldorf, Germany	Düsseldorf	Germany	DE	51.22172000	6.77616000	f	f	office	2025-08-12 14:48:15.615934+00	2025-08-12 14:48:15.615934+00
80864bf8-bce0-4134-9c1b-9e76c3d53d24	b8bd7b27-b49d-4f71-baea-d3d7b43a3550	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	office	2025-08-12 14:48:15.694697+00	2025-08-12 14:48:15.694697+00
d455e54d-97b8-4d0d-b27e-ffcbb10f742a	59c64665-a162-48c4-bcb6-927a78ed5e69	Erlangen, Germany	Erlangen, Germany	Erlangen	Germany	DE	49.59099000	11.00783000	t	t	office	2025-08-12 14:48:15.706486+00	2025-08-12 14:48:15.706486+00
54b7679f-a365-47b5-979a-f11d0cdb4381	8b73480a-face-4af2-98f3-79264cfca659	Holzminden, Germany	Holzminden, Germany	Holzminden	Germany	DE	51.82798000	9.44550000	t	t	office	2025-08-12 14:48:15.717739+00	2025-08-12 14:48:15.717739+00
5f7a75ed-5e66-4342-8432-91cbc315d202	1fe135c6-0c7e-426e-9e33-b8419c772fdb	Wolfsburg, Germany	Wolfsburg, Germany	Wolfsburg	Germany	DE	52.42452000	10.78150000	t	t	office	2025-08-12 14:48:15.726267+00	2025-08-12 14:48:15.726267+00
e88ad73e-1a32-4a38-8058-386ddbf95209	d0355ee2-baba-454d-9b84-ae8ba8088c64	Leipzig, Germany	Leipzig, Germany	Leipzig	Germany	DE	51.33962000	12.37129000	f	f	office	2025-08-14 09:43:44.405298+00	2025-08-14 13:09:39.449633+00
af94d333-9a6f-42cb-b34f-bdb69b19e98a	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	Gerlingen, Germany	Gerlingen, Germany	Gerlingen	Germany	DE	48.79954000	9.06316000	f	f	office	2025-08-12 11:29:06.176353+00	2025-08-12 11:29:06.176353+00
ee9a3ba0-7355-479b-a41f-1f68d7df7476	d0355ee2-baba-454d-9b84-ae8ba8088c64	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	f	f	office	2025-08-14 13:09:39.491907+00	2025-08-14 13:09:39.491907+00
33a435b7-d4e4-4ff2-afd7-7ae0dc4896ea	1fe135c6-0c7e-426e-9e33-b8419c772fdb	Hannover, Germany	Hannover, Germany	Hannover	Germany	DE	52.37052000	9.73322000	f	f	office	2025-08-12 14:48:15.726267+00	2025-08-12 14:48:15.726267+00
4310b91e-1d90-47a6-9fa8-6d4bcf5df22c	1fe135c6-0c7e-426e-9e33-b8419c772fdb	Braunschweig, Germany	Braunschweig, Germany	Braunschweig	Germany	DE	52.26594000	10.52673000	f	f	office	2025-08-12 14:48:15.726267+00	2025-08-12 14:48:15.726267+00
c81b3514-03f5-471d-812b-323454492037	1fe135c6-0c7e-426e-9e33-b8419c772fdb	Kassel, Germany	Kassel, Germany	Kassel	Germany	DE	51.31667000	9.50000000	f	f	office	2025-08-12 14:48:15.726267+00	2025-08-12 14:48:15.726267+00
0504dc61-d78f-45cd-b140-067f423a99ea	1fe135c6-0c7e-426e-9e33-b8419c772fdb	Salzgitter, Germany	Salzgitter, Germany	Salzgitter	Germany	DE	52.15705000	10.41540000	f	f	office	2025-08-12 14:48:15.726267+00	2025-08-12 14:48:15.726267+00
16737298-fc06-4a1d-8432-97fdd4a2422e	1fe135c6-0c7e-426e-9e33-b8419c772fdb	Emden, Germany	Emden, Germany	Emden	Germany	DE	53.36745000	7.20778000	f	f	office	2025-08-12 14:48:15.726267+00	2025-08-12 14:48:15.726267+00
9038f310-ee1b-477a-9450-24ce926635aa	1fe135c6-0c7e-426e-9e33-b8419c772fdb	Dresden, Germany	Dresden, Germany	Dresden	Germany	DE	51.05089000	13.73832000	f	f	office	2025-08-12 14:48:15.726267+00	2025-08-12 14:48:15.726267+00
6b97045a-bba2-42a8-b299-e770e01cd4b5	1fe135c6-0c7e-426e-9e33-b8419c772fdb	Zwickau, Germany	Zwickau, Germany	Zwickau	Germany	DE	50.72724000	12.48839000	f	f	office	2025-08-12 14:48:15.726267+00	2025-08-12 14:48:15.726267+00
26b561bc-8ac7-4d22-bf52-46e0ab5143b8	1f8509ef-6130-4303-a173-6506ec830047	Bochum, Germany	Bochum, Germany	Bochum	Germany	DE	51.48165000	7.21648000	t	t	office	2025-08-12 14:48:15.780798+00	2025-08-12 14:48:15.780798+00
64786280-b1c0-49bc-bfc2-ba4dd2368088	5c519372-e81d-4c55-aaac-b77d113d48ca	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	t	t	office	2025-08-12 14:48:15.791021+00	2025-08-12 14:48:15.791021+00
b6d365fe-de93-49d3-b76f-a0fe1bb1b843	8aeefd4a-cc05-402c-89af-12b8783431e5	Herzogenrath, Germany	Herzogenrath, Germany	Herzogenrath	Germany	DE	50.86874000	6.09317000	t	t	office	2025-08-12 14:48:15.801627+00	2025-08-12 14:48:15.801627+00
c0f44b31-c786-486c-a38c-b12744ab6eca	a2313897-00a7-4bf7-a0fd-fcaa26eff747	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	t	t	office	2025-08-12 14:48:15.814368+00	2025-08-12 14:48:15.814368+00
ed3e0172-2310-4011-a4c0-7a7292ba34ca	2e551ef6-f8cb-4601-9032-99c4289712b9	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	t	t	office	2025-08-12 14:48:15.826034+00	2025-08-12 14:48:15.826034+00
876465d4-d2bb-46e5-8380-4120752d8524	02836b5c-32a3-446a-b7e2-82a1ec01b8cb	Neckarsulm, Germany	Neckarsulm, Germany	Neckarsulm	Germany	DE	49.18912000	9.22527000	t	t	office	2025-08-12 14:48:15.838391+00	2025-08-12 14:48:15.838391+00
60bd8650-c1af-44be-a7f7-d1ed2d6340a4	37855bda-7840-4253-a111-a337cabdcd25	Ratingen, Germany	Ratingen, Germany	Ratingen	Germany	DE	51.29724000	6.84929000	t	t	office	2025-08-12 14:48:15.850875+00	2025-08-12 14:48:15.850875+00
d5dd836c-09a3-4750-9488-bd2a8a12324e	c6e8582e-0915-41c3-a633-ae0826192a24	Mannheim, Germany	Mannheim, Germany	Mannheim	Germany	DE	49.48910000	8.46694000	t	t	office	2025-08-12 14:48:15.862646+00	2025-08-12 14:48:15.862646+00
a3b7bd55-c01f-4734-9777-4f45545d8bee	832184e3-ca5f-41c8-9140-0339c333164f	Jena, Germany	Jena, Germany	Jena	Germany	DE	50.92878000	11.58990000	t	t	office	2025-08-12 14:48:15.871066+00	2025-08-12 14:48:15.871066+00
2da6dfc9-6e20-4562-9816-61df0cc2f6fe	f70e5abe-a9a1-4a33-95dc-2216279bda8c	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	office	2025-08-12 14:48:15.879629+00	2025-08-12 14:48:15.879629+00
4ea9fbd8-6388-43a1-af0d-c3d10b60226f	c91cf384-36cc-412f-bcab-6fda76e641c3	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	t	t	office	2025-08-12 14:48:15.889927+00	2025-08-12 14:48:15.889927+00
aea2ea2e-8abf-4fc8-9911-f9d6fe25c71b	49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	Frankfurt am Main, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	t	t	office	2025-08-12 14:48:15.900276+00	2025-08-12 14:48:15.900276+00
55aefc90-c9b7-418f-bf77-fab6bd735ef5	49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	f	f	office	2025-08-12 14:48:15.900276+00	2025-08-12 14:48:15.900276+00
14f2ff70-aedf-4a13-9b1b-4a393d0abcb8	49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	f	f	office	2025-08-12 14:48:15.900276+00	2025-08-12 14:48:15.900276+00
47dea424-5529-48bb-903b-b658add112be	49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	f	f	office	2025-08-12 14:48:15.900276+00	2025-08-12 14:48:15.900276+00
0fca0a2d-bd27-4a08-a250-49be0b522eae	49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	Düsseldorf, Germany	Düsseldorf, Germany	Düsseldorf	Germany	DE	51.22172000	6.77616000	f	f	office	2025-08-12 14:48:15.900276+00	2025-08-12 14:48:15.900276+00
ee2e0824-05cc-4171-a4f1-cbbf9b706e5d	49ba5831-32d0-4dcd-8d29-1c5d5b4676d5	Stuttgart, Germany	Stuttgart, Germany	Stuttgart	Germany	DE	48.78232000	9.17702000	f	f	office	2025-08-12 14:48:15.900276+00	2025-08-12 14:48:15.900276+00
59b8cae2-2192-4536-9bbf-004b50c827b3	456f9891-6e24-4136-8368-a6e576d2d53a	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	t	t	office	2025-08-12 14:48:15.947834+00	2025-08-12 14:48:15.947834+00
a1bf622b-06fe-4c6a-970d-1292f6d60b9c	4d92ae8f-991a-4d22-90f9-aab1f6c2512f	Essen, Germany	Essen, Germany	Essen	Germany	DE	52.72258000	7.93710000	t	t	office	2025-08-12 14:48:15.958057+00	2025-08-12 14:48:15.958057+00
96868471-03ab-408d-a180-9bfcb9340814	b28367d9-2016-4a4b-a162-5b865cb44118	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	t	t	office	2025-08-12 14:48:15.968314+00	2025-08-12 14:48:15.968314+00
6bd5dd69-ce79-4daa-8932-1f3fdc29b4d5	6b09eed5-2cc0-4f8d-8f93-4d22d09ef433	Frankfurt am Main, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	t	t	office	2025-08-12 14:48:15.977265+00	2025-08-12 14:48:15.977265+00
5feb8d78-926f-4563-8b9c-8376459217e6	997a91da-3e54-4ae5-8b8b-7130d4de2728	Büdelsdorf, Germany	Büdelsdorf, Germany	Büdelsdorf	Germany	DE	54.31844000	9.67295000	t	t	office	2025-08-12 14:48:15.989081+00	2025-08-12 14:48:15.989081+00
cc6f85b3-3b4c-40d2-a01a-98c6cdd00861	601b5439-4f73-43cf-920d-f760941427d5	Mannheim, Germany	Mannheim, Germany	Mannheim	Germany	DE	49.48910000	8.46694000	t	t	office	2025-08-12 14:48:16.000607+00	2025-08-12 14:48:16.000607+00
23bdc40c-f1e0-4f77-bdb6-9252055ddee9	37441a48-1255-417f-a513-cac9b1853e23	Düsseldorf, Germany	Düsseldorf, Germany	Düsseldorf	Germany	DE	51.22172000	6.77616000	t	t	office	2025-08-12 14:48:16.008681+00	2025-08-12 14:48:16.008681+00
e75664e8-7059-4819-b2ed-5908c620c557	b12de1f1-114b-4e30-b6aa-432e5398c97b	Düsseldorf, Germany	Düsseldorf, Germany	Düsseldorf	Germany	DE	51.22172000	6.77616000	t	t	office	2025-08-12 14:48:16.020159+00	2025-08-12 14:48:16.020159+00
1b7b579c-72ed-44c5-9fc0-8c63cd9ad774	674b216a-eb1b-4a8f-9543-495009109b70	Lippstadt, Germany	Lippstadt, Germany	Lippstadt	Germany	DE	51.67369000	8.34482000	t	t	office	2025-08-12 14:48:16.032575+00	2025-08-12 14:48:16.032575+00
f220f2be-9c1f-450d-b203-fccd58ee7790	000eb8b4-345d-4e1f-ae98-69318a0e97aa	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	t	t	office	2025-08-12 14:48:16.043055+00	2025-08-12 14:48:16.043055+00
aebd978c-8046-4d62-b6af-dbddd1a46a62	0bf1feef-964a-4f18-9566-3fffb1de71e6	Taufkirchen, Germany	Taufkirchen, Germany	Taufkirchen	Germany	DE	48.04860000	11.61701000	t	t	office	2025-08-12 14:48:16.054553+00	2025-08-12 14:48:16.054553+00
137f2a11-8bb4-46bf-928b-d2f8e4b4a956	f3f2bb21-94cc-42de-abcf-123f837bcd2f	Essen, Germany	Essen, Germany	Essen	Germany	DE	52.72258000	7.93710000	t	t	office	2025-08-12 14:48:16.067481+00	2025-08-12 14:48:16.067481+00
a128efe8-276b-48cf-80f3-f0838495eb5b	004a3070-db79-47ab-8872-3c383c05f26c	Metzingen, Germany	Metzingen, Germany	Metzingen	Germany	DE	48.53695000	9.28330000	t	t	office	2025-08-12 14:48:16.075268+00	2025-08-12 14:48:16.075268+00
9a0a0f05-5aa3-4c19-9cb8-99cb956e52dd	dc33601c-f4a9-464e-9e78-f76e83290a5e	Jena, Germany	Jena, Germany	Jena	Germany	DE	50.92878000	11.58990000	t	t	office	2025-08-12 14:48:16.084193+00	2025-08-12 14:48:16.084193+00
1954c651-e23c-440b-8133-d643f9f14bcf	e9de360f-c376-465e-8517-dc3c37bcd9dd	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	t	t	office	2025-08-12 14:48:16.091165+00	2025-08-12 14:48:16.091165+00
67b45222-7bc2-4610-8dfe-ee5c1a72602a	bb2fd4de-5722-4e72-b9ae-19e265a8633d	Kassel, Germany	Kassel, Germany	Kassel	Germany	DE	51.31667000	9.50000000	t	t	office	2025-08-12 14:48:16.101346+00	2025-08-12 14:48:16.101346+00
bba97af1-dc03-42bc-bcb4-cb5e04780e18	9bc051c4-8ea2-46a7-88bc-c81ae2b04a32	Frankfurt am Main, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	t	t	office	2025-08-12 14:48:16.108637+00	2025-08-12 14:48:16.108637+00
45432992-f033-4387-b3f2-6c0070978263	43667920-619d-482d-8a4f-6ad4cba0558b	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	office	2025-08-12 14:48:16.121936+00	2025-08-12 14:48:16.121936+00
54d53009-301b-4772-8611-e55d462a7712	04e50ca5-a627-45f8-b4b7-5d3dc84a4506	Neutraubling, Germany	Neutraubling, Germany	Neutraubling	Germany	DE	48.98737000	12.20100000	t	t	office	2025-08-12 14:48:16.133099+00	2025-08-12 14:48:16.133099+00
4250ebf7-3479-42c1-908e-aaddeffc0ea4	e7c38b02-bbf3-4b1a-9de6-2866de2c11e5	Düsseldorf, Germany	Düsseldorf, Germany	Düsseldorf	Germany	DE	51.22172000	6.77616000	t	t	office	2025-08-12 14:48:16.150005+00	2025-08-12 14:48:16.150005+00
63c3202b-81b4-468f-ae66-2b557379b626	74e1fc43-8fc7-4894-9ac6-319eb6ecda91	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	office	2025-08-12 14:48:16.157658+00	2025-08-12 14:48:16.157658+00
9e69284e-d409-4ce2-8520-9f4b9eee20b6	8f466bb4-6793-4ebe-a712-dd7689e1a4f8	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	t	t	office	2025-08-12 14:48:16.167028+00	2025-08-12 14:48:16.167028+00
d94c79bf-faf1-4551-b7fa-e7c648d85b62	7ab34c9b-1578-4f2e-99e3-878b5149f0c4	Herzogenaurach, Germany	Herzogenaurach, Germany	Herzogenaurach	Germany	DE	49.56798000	10.88565000	t	t	office	2025-08-12 14:48:16.175237+00	2025-08-12 14:48:16.175237+00
768eff23-4056-47a3-b909-5ed7252e90e7	38afb290-3557-449b-88af-012c141d1b3c	Landsberg am Lech, Germany	Landsberg am Lech, Germany	Landsberg am Lech	Germany	DE	48.04819000	10.88282000	t	t	office	2025-08-12 14:48:16.188765+00	2025-08-12 14:48:16.188765+00
f746e74c-b36d-41ca-932f-033e055ee1d4	e7ebb734-da29-4f54-92fa-b30a41de94f8	Düsseldorf, Germany	Düsseldorf, Germany	Düsseldorf	Germany	DE	51.22172000	6.77616000	t	t	office	2025-08-12 14:48:16.202958+00	2025-08-12 14:48:16.202958+00
05c8f266-4461-4e61-b525-214488ec94b1	c5d86c5f-f4d7-41de-a885-5ecaa99382f6	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	office	2025-08-12 14:48:16.223094+00	2025-08-12 14:48:16.223094+00
c0170335-8339-4bf4-825c-fc580edb525d	c60b72aa-e461-4455-aea2-54351bc248c3	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	office	2025-08-12 14:48:16.233525+00	2025-08-12 14:48:16.233525+00
62eb0285-80d6-48f4-a1fd-f07f96922a66	4b78798f-5911-4d42-b960-d0dc745d8e1b	Koblenz, Germany	Koblenz, Germany	Koblenz	Germany	DE	50.35357000	7.57883000	t	t	office	2025-08-12 14:48:16.241076+00	2025-08-12 14:48:16.241076+00
8424e02a-ed82-40df-9032-fe9376ca574a	566454f3-e954-4ccf-bf17-5159f56eae8e	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	t	t	office	2025-08-12 14:48:16.257059+00	2025-08-12 14:48:16.257059+00
3a234869-6568-49df-a06a-652cbde0ecf9	da45afe9-76e7-4dfb-be53-857c2c5b763b	Hannover, Germany	Hannover, Germany	Hannover	Germany	DE	52.37052000	9.73322000	t	t	office	2025-08-12 14:48:16.26349+00	2025-08-12 14:48:16.26349+00
cd832dba-e946-44e5-a9c6-5aa9d5b659b6	1c933b7b-406a-4bbd-abd9-87e7667cc7eb	Göppingen, Germany	Göppingen, Germany	Göppingen	Germany	DE	48.70354000	9.65209000	t	t	office	2025-08-12 14:48:16.270523+00	2025-08-12 14:48:16.270523+00
220665d5-06f5-4307-8e50-6ad72b1893df	5bf80e46-5c91-499c-89cf-916709d774dd	Essen, Germany	Essen, Germany	Essen	Germany	DE	52.72258000	7.93710000	t	t	office	2025-08-12 14:48:16.281544+00	2025-08-12 14:48:16.281544+00
d0b794a3-3eff-469b-beb1-ab4ee25ead51	5bf80e46-5c91-499c-89cf-916709d774dd	Duisburg, Germany	Duisburg, Germany	Duisburg	Germany	DE	51.43247000	6.76516000	f	f	office	2025-08-12 14:48:16.281544+00	2025-08-12 14:48:16.281544+00
6947a780-0240-481a-bcab-959817b32d6f	5bf80e46-5c91-499c-89cf-916709d774dd	Düsseldorf, Germany	Düsseldorf, Germany	Düsseldorf	Germany	DE	51.22172000	6.77616000	f	f	office	2025-08-12 14:48:16.281544+00	2025-08-12 14:48:16.281544+00
35716baf-3626-4bea-896b-6bf2fec163fe	5bf80e46-5c91-499c-89cf-916709d774dd	Bochum, Germany	Bochum, Germany	Bochum	Germany	DE	51.48165000	7.21648000	f	f	office	2025-08-12 14:48:16.281544+00	2025-08-12 14:48:16.281544+00
94e4df41-e688-4511-af04-01b9df53274c	5bf80e46-5c91-499c-89cf-916709d774dd	Dortmund, Germany	Dortmund, Germany	Dortmund	Germany	DE	51.51494000	7.46600000	f	f	office	2025-08-12 14:48:16.281544+00	2025-08-12 14:48:16.281544+00
a925f34e-7982-404d-8396-762245a5554f	5bf80e46-5c91-499c-89cf-916709d774dd	Krefeld, Germany	Krefeld, Germany	Krefeld	Germany	DE	51.33645000	6.55381000	f	f	office	2025-08-12 14:48:16.281544+00	2025-08-12 14:48:16.281544+00
177df62b-e0c5-4676-a7bb-1ac20cbcac63	5bf80e46-5c91-499c-89cf-916709d774dd	Neuss, Germany	Neuss, Germany	Neuss	Germany	DE	51.19807000	6.68504000	f	f	office	2025-08-12 14:48:16.281544+00	2025-08-12 14:48:16.281544+00
50ca7e6a-389b-4ff4-98da-7554cdfc5f1f	271cf44a-7115-4cbc-929a-6ac11cd2eee3	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	office	2025-08-12 14:48:16.319078+00	2025-08-12 14:48:16.319078+00
a6415f1b-e7b0-4e0c-936e-5d0b0baf7d27	58087702-006c-4c7c-8abf-48324b64977b	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	t	t	office	2025-08-12 14:48:16.325893+00	2025-08-12 14:48:16.325893+00
9a3fc45c-0a5f-47f0-aa3c-ad32aac6256a	ab9f370d-3dbc-40e0-9366-ec154aa2a6b3	Montabaur, Germany	Montabaur, Germany	Montabaur	Germany	DE	50.43588000	7.82320000	t	t	office	2025-08-12 14:48:16.340569+00	2025-08-12 14:48:16.340569+00
25b8e5ba-9467-4c28-807a-b7fc9c2d6506	37e9e641-643b-43af-a3f0-75bce47526be	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	office	2025-08-12 14:48:16.348787+00	2025-08-12 14:48:16.348787+00
b41360e6-4b70-4965-b7e2-09e363e00f7e	0a9e280f-8e34-4ab5-ae80-88f60ecd0d77	Montabaur, Germany	Montabaur, Germany	Montabaur	Germany	DE	50.43588000	7.82320000	t	t	office	2025-08-12 14:48:16.359609+00	2025-08-12 14:48:16.359609+00
5ce23bf7-8ff1-44e3-baad-5eaa5056e177	531dd3fb-eea8-4376-9c7f-f6c188810fc9	Dortmund, Germany	Dortmund, Germany	Dortmund	Germany	DE	51.51494000	7.46600000	t	t	office	2025-08-12 14:48:16.36776+00	2025-08-12 14:48:16.36776+00
3f816c2a-7fc7-4b9b-a3ae-cb04210d6833	e0c95800-8906-4535-9140-c25ee08dedd9	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	office	2025-08-12 14:48:16.374492+00	2025-08-12 14:48:16.374492+00
e83207eb-4ac4-41be-b8f6-d6ac8dff5ad3	6b8523ca-0c34-4495-ae7c-58b7c187f8dd	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	t	t	office	2025-08-12 14:48:16.381381+00	2025-08-12 14:48:16.381381+00
b60718ff-42eb-467c-86fc-7da6fafc2b80	6b8523ca-0c34-4495-ae7c-58b7c187f8dd	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	f	f	office	2025-08-12 14:48:16.381381+00	2025-08-12 14:48:16.381381+00
bcc86b29-742d-45a1-a339-92b1a170cdd3	6b8523ca-0c34-4495-ae7c-58b7c187f8dd	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	f	f	office	2025-08-12 14:48:16.381381+00	2025-08-12 14:48:16.381381+00
e4c10d9f-daff-4c67-aa8b-7a2b1d2d9d02	0585eae4-daf5-4b72-82f5-2dc2031e8c01	Kronberg, Germany	Kronberg, Germany	Kronberg	Germany	DE	50.18424000	8.52320000	f	f	office	2025-08-12 08:29:11.689+00	2025-08-12 11:07:17.44895+00
f803ad07-01ce-440e-84f7-a527e11ad5a5	6b8523ca-0c34-4495-ae7c-58b7c187f8dd	Düsseldorf, Germany	Düsseldorf, Germany	Düsseldorf	Germany	DE	51.22172000	6.77616000	f	f	office	2025-08-12 14:48:16.381381+00	2025-08-12 14:48:16.381381+00
29bd1155-bce9-4174-a0e5-885f25fc5907	6b8523ca-0c34-4495-ae7c-58b7c187f8dd	Frankfurt am Main, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	f	f	office	2025-08-12 14:48:16.381381+00	2025-08-12 14:48:16.381381+00
60f0dc2e-9b6e-409a-9acf-aae21a05fcef	dbeb0b10-a459-416d-bc6e-5bd84421b248	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	office	2025-08-12 14:48:16.413179+00	2025-08-12 14:48:16.413179+00
eed34ea0-e206-47c1-a07f-3fe4eb042bf5	00ce236d-d595-4b94-885f-902083b3b055	Dortmund, Germany	Dortmund, Germany	Dortmund	Germany	DE	51.51494000	7.46600000	t	t	office	2025-08-12 14:48:16.421068+00	2025-08-12 14:48:16.421068+00
350643e2-950e-4c09-a719-ebb4b0a29374	efb1372f-7edf-4f62-8c3c-d8538dfeeb9b	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	office	2025-08-12 14:48:16.429168+00	2025-08-12 14:48:16.429168+00
994aaa64-bdba-4bad-abf0-a4c0fbea5876	9e2a3af7-68a6-42ea-916d-5a484b9bda9a	Düsseldorf, Germany	Düsseldorf, Germany	Düsseldorf	Germany	DE	51.22172000	6.77616000	t	t	office	2025-08-12 14:48:16.438713+00	2025-08-12 14:48:16.438713+00
0d09dde6-7e9f-4479-b695-9e836ff41682	a4359e21-ce42-43cd-95b5-a72df59597bb	Oldenburg, Germany	Oldenburg, Germany	Oldenburg	Germany	DE	53.14118000	8.21467000	t	t	office	2025-08-12 14:48:16.450175+00	2025-08-12 14:48:16.450175+00
26765b8d-5ca0-482e-8db1-6b39e63ac152	81319234-71ef-4f21-9d02-8bf7fbd2ecd4	Koblenz, Germany	Koblenz, Germany	Koblenz	Germany	DE	50.35357000	7.57883000	t	t	office	2025-08-12 14:48:16.461164+00	2025-08-12 14:48:16.461164+00
629f7277-6306-4481-92ff-66643bf2d796	9413d6f2-9c16-4489-881a-f5a073632d6a	Grünwald, Germany	Grünwald, Germany	Grünwald	Germany	DE	48.03947000	11.52320000	t	t	office	2025-08-12 14:48:16.471663+00	2025-08-12 14:48:16.471663+00
90c4dd32-27e9-484b-ab2d-354c12cf552c	164a02bf-58de-4d70-90ed-54a17641c1a1	Frankfurt am Main, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	t	t	office	2025-08-12 14:48:16.479756+00	2025-08-12 14:48:16.479756+00
dee714e8-5286-4230-b607-0b16ed5f46f6	ef1015c5-0570-4c1c-a60e-fc9006b0b513	Garching bei München, Germany	Garching bei München, Germany	Garching bei München	Germany	DE	48.24896000	11.65101000	t	t	office	2025-08-12 14:48:16.490648+00	2025-08-12 14:48:16.490648+00
e73914d9-e8f1-4854-968f-1820728ade18	b6b1eb84-5f0e-4457-a6c8-0a3743233791	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	t	t	office	2025-08-12 14:48:16.501462+00	2025-08-12 14:48:16.501462+00
82b1bfb9-81f8-490b-8e1f-c3874eb91a4b	4bd5e470-ca13-4562-9f0f-ed7b830576c0	Lübeck, Germany	Lübeck, Germany	Lübeck	Germany	DE	53.86893000	10.68729000	t	t	office	2025-08-12 14:48:16.516159+00	2025-08-12 14:48:16.516159+00
7329e15a-09b4-4e93-991b-d8591408a1e5	0fdc87d9-5f64-48ee-9410-8a25cf12e550	Bietigheim-Bissingen, Germany	Bietigheim-Bissingen, Germany	Bietigheim-Bissingen	Germany	DE	48.94407000	9.11755000	t	t	office	2025-08-12 14:48:16.52427+00	2025-08-12 14:48:16.52427+00
435c48dd-6c6b-437e-8305-d539ea9790e6	8d626f73-5fc4-46e1-92b7-91e21dd5b70c	Frankfurt am Main, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	t	t	office	2025-08-12 14:48:16.536356+00	2025-08-12 14:48:16.536356+00
855e1d13-6ef8-4e46-8122-0b582e1d423e	555cf212-1e5d-4a4a-8f9a-8880b3f0e9f2	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	t	t	office	2025-08-12 14:48:16.544972+00	2025-08-12 14:48:16.544972+00
bdb0e5ce-1c66-43ad-b25f-c457b10fcc3f	5624c6d5-ca41-4386-84bb-334cb64e5f69	Dortmund, Germany	Dortmund, Germany	Dortmund	Germany	DE	51.51494000	7.46600000	t	t	office	2025-08-12 14:48:16.554832+00	2025-08-12 14:48:16.554832+00
41cb08fd-edce-4afc-ab07-3426e567ef1c	3c629f04-0cb3-44f1-ab2b-8f2481fd2a7f	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	t	t	office	2025-08-12 14:48:16.566058+00	2025-08-12 14:48:16.566058+00
8a28a826-56a6-4471-b43f-fd69d38e1f41	a32301a7-a61a-4919-8ea2-5258c5b1887d	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	f	f	office	2025-08-12 11:29:06.096272+00	2025-08-12 11:29:06.096272+00
5df301fa-b96e-4377-9bb1-878eeed0f7b0	a32301a7-a61a-4919-8ea2-5258c5b1887d	Dingolfing, Germany	Dingolfing, Germany	Dingolfing	Germany	DE	48.64244000	12.49283000	f	f	office	2025-08-12 11:29:06.119089+00	2025-08-12 11:29:06.119089+00
4c68c89a-657c-4cf0-96a7-f0e3f0d29494	a32301a7-a61a-4919-8ea2-5258c5b1887d	Regensburg, Germany	Regensburg, Germany	Regensburg	Germany	DE	49.01513000	12.10161000	f	f	office	2025-08-12 11:29:06.137096+00	2025-08-12 11:29:06.137096+00
5038cca7-c7fe-4af2-afc2-d970aba96e2a	a32301a7-a61a-4919-8ea2-5258c5b1887d	Leipzig, Germany	Leipzig, Germany	Leipzig	Germany	DE	51.33962000	12.37129000	f	f	office	2025-08-12 11:29:06.154462+00	2025-08-12 11:29:06.154462+00
ab3bd61b-4aa2-44a8-9e8b-1d6542fc6944	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	Hildesheim, Germany	Hildesheim, Germany	Hildesheim	Germany	DE	52.15077000	9.95112000	f	f	office	2025-08-12 11:29:06.19541+00	2025-08-12 11:29:06.19541+00
3f08ee84-15df-4644-b2c7-a7577e1b917d	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	Reutlingen, Germany	Reutlingen, Germany	Reutlingen	Germany	DE	48.49144000	9.20427000	f	f	office	2025-08-12 11:29:06.215026+00	2025-08-12 11:29:06.215026+00
858f7efa-8f98-467f-aa02-8f715178d8ab	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	Bamberg, Germany	Bamberg, Germany	Bamberg	Germany	DE	49.89873000	10.90067000	f	f	office	2025-08-12 11:29:06.232406+00	2025-08-12 11:29:06.232406+00
a9a40dc1-d2cb-48dd-9129-6556af4c0105	d0355ee2-baba-454d-9b84-ae8ba8088c64	Köln, Germany	Köln, Germany	Köln	Germany	DE	50.93333000	6.95000000	t	t	headquarters	2025-08-12 08:04:04.398439+00	2025-08-14 13:09:39.40343+00
ebed1fc9-84bd-4afa-9314-c6523ec47808	c704a431-a900-4ccf-bd2b-a979f1c40ce0	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	f	f	office	2025-08-12 11:29:07.162804+00	2025-08-12 11:29:07.162804+00
38bf54d2-ab06-42b3-8a37-4239a9c01706	c704a431-a900-4ccf-bd2b-a979f1c40ce0	Hamburg, Germany	Hamburg, Germany	Hamburg	Germany	DE	53.57532000	10.01534000	f	f	office	2025-08-12 11:29:07.185465+00	2025-08-12 11:29:07.185465+00
c436740d-1a73-4950-8b07-6af3bac77125	065ba8b7-61bb-4300-a38e-e8c327cb8971	Frankfurt	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	t	t	headquarters	2025-08-12 08:04:04.398439+00	2025-08-12 11:22:43.283172+00
c5a75e3e-7d08-4f9c-917a-a3b977f0eba7	a32301a7-a61a-4919-8ea2-5258c5b1887d	Munich, Germany	Munich, Germany	Munich	Germany	DE	48.13743000	11.57549000	t	t	headquarters	2025-08-12 08:04:04.398439+00	2025-08-12 11:21:44.549169+00
52ba7ee5-c777-4719-98e9-493fb279154b	77c0d9e5-7d2c-409f-a75d-273b066d5f3e	Stuttgart, Germany	Stuttgart, Germany	Stuttgart	Germany	DE	48.78232000	9.17702000	t	t	headquarters	2025-08-12 08:04:04.398439+00	2025-08-12 11:21:44.564658+00
4ad36e23-e434-468b-bdea-641dfd4592f7	2c09a41b-b047-4b54-86be-d7b09997f9ec	Frankfurt	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	t	t	headquarters	2025-08-12 08:04:04.398439+00	2025-08-12 11:22:43.283172+00
01af5b6e-c948-4452-b52d-92edb771fc21	fa2034b0-c512-4be7-a1bc-700d1559a554	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	t	t	headquarters	2025-08-12 08:04:04.398439+00	2025-08-12 11:21:45.003843+00
84cdb967-e27f-4337-9fba-1776c60518e9	c704a431-a900-4ccf-bd2b-a979f1c40ce0	Berlin, Germany	Berlin, Germany	Berlin	Germany	DE	52.52437000	13.41053000	t	t	headquarters	2025-08-12 08:04:04.398439+00	2025-08-12 11:21:45.115342+00
c8c90e72-1278-4ed4-b450-2eaaf2876394	c837dbee-de4f-4726-b325-175809ad737b	Frankfurt	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	t	t	headquarters	2025-08-12 08:04:04.398439+00	2025-08-12 11:22:43.283172+00
c671f7f7-31de-420a-80f4-62f213c8fd01	0dcad781-92e5-45b8-9b85-7e559303c3af	Frankfurt	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	t	t	headquarters	2025-08-12 08:04:04.398439+00	2025-08-12 11:22:43.283172+00
633e5bfd-4586-4fe0-afe9-33d1b7c5ed94	9f82f663-4a3c-48e5-83d9-fc2b1a5bd69d	Frankfurt, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	t	t	headquarters	2025-08-12 08:04:04.398439+00	2025-08-12 11:22:43.283172+00
b7f6bc8e-9676-4d8f-a8f2-e03a02748b88	710df751-8fd6-41db-8245-8b2bf039ed85	Frankfurt, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	t	t	headquarters	2025-08-12 08:04:04.398439+00	2025-08-12 11:22:43.283172+00
612b05e0-2561-4c0d-b0fd-ad405fe2f00c	f391b88a-6e80-4bf6-acc3-3bf36f7a665c	Frankfurt, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	t	t	headquarters	2025-08-12 08:04:04.398439+00	2025-08-12 11:22:43.283172+00
878777bd-e308-4afd-b468-f3fef4fcfbcb	0585eae4-daf5-4b72-82f5-2dc2031e8c01	Frankfurt am Main, Germany	Frankfurt am Main, Germany	Frankfurt am Main	Germany	DE	50.11552000	8.68417000	t	t	headquarters	2025-08-12 08:04:04.398+00	2025-08-12 11:07:17.268181+00
a44a4c24-19c4-4625-b307-7a0c8f4355d0	942f7063-15f7-4841-87bb-ee567d71d9ae	Darmstadt, Germany	Darmstadt, Germany	Darmstadt	Germany	DE	49.87167000	8.65027000	t	t	headquarters	2025-08-12 08:04:04.398439+00	2025-08-12 11:21:45.296509+00
\.


--
-- Data for Name: company_page_views; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.company_page_views (id, company_id, user_id, session_id, ip_address, user_agent, referrer, created_at) FROM stdin;
\.


--
-- Data for Name: company_users; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.company_users (id, company_id, email, is_verified, created_at) FROM stdin;
\.


--
-- Data for Name: company_verification_tokens; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.company_verification_tokens (id, token, user_id, user_email, company_id, expires_at, used_at, created_at) FROM stdin;
\.


--
-- Data for Name: csrf_tokens; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.csrf_tokens (id, session_id, token, expires_at, created_at) FROM stdin;
\.


--
-- Data for Name: daily_analytics_summary; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.daily_analytics_summary (id, date, total_company_views, total_searches, total_benefit_interactions, unique_visitors, unique_searchers, top_searched_benefits, top_viewed_companies, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: magic_link_rate_limits; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.magic_link_rate_limits (id, email, request_count, window_start, created_at) FROM stdin;
\.


--
-- Data for Name: magic_link_tokens; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.magic_link_tokens (id, token, email, user_data, expires_at, used_at, created_at) FROM stdin;
\.


--
-- Data for Name: migration_log; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.migration_log (id, migration_name, description, applied_at) FROM stdin;
1	004-remove-company-verification	Removed manual company verification workflow in favor of automatic email domain matching	2025-07-30 09:50:39.372717+00
2	005-add-company-id-to-users	Added explicit company_id field to users table for better company association tracking	2025-07-30 12:53:44.276299+00
3	009-add-activity-log	Add activity log table for tracking system events	2025-07-31 14:01:01.754562+00
4	manual-cleanup-unused-elements	Manually removed unused verified column, job_postings table, and password_reset_tokens table to match current application functionality	2025-07-31 18:42:45.275869+00
5	012-add-cancelled-status-to-disputes	Add cancelled status to benefit removal disputes table	2025-08-01 16:08:56.003603+00
6	013-add-cancelled-event-to-activity-log	Add benefit_removal_dispute_cancelled event type to activity log constraint	2025-08-01 16:09:01.499137+00
7	018-add-multi-location-support	Add multi-location support for companies with location normalization	2025-08-12 08:04:04.403656+00
8	002_postgresql_caching_system	Added PostgreSQL-based caching system with materialized views and cache management functions	2025-08-14 12:50:49.993681+00
9	001_optimize_postgresql_sessions	Added PostgreSQL session management optimizations, cleanup functions, and monitoring	2025-08-14 12:50:58.342449+00
10	003_update_schema_for_cache_system	Updated schema to support PostgreSQL cache system - added missing company columns and updated activity log constraints	2025-08-14 12:57:57.123253+00
11	004_recreate_materialized_views	Recreated materialized views with correct schema matching actual database structure	2025-08-14 12:58:56.047543+00
12	005_fix_materialized_views	Fixed materialized views with correct column names matching actual database schema	2025-08-14 12:59:35.700446+00
13	006_cache_json_parsing_fix	Fixed JSON parsing error in cache system - removed double JSON.parse() call for JSONB data	2025-08-14 13:15:53.76454+00
14	020-remove-is-active-from-benefit-categories	Remove is_active field from benefit_categories table - active status now determined by benefit count	2025-08-27 07:06:03.629036+00
17	021-standardize-activity-log-constraint	Standardize activity log event type constraint ordering across all environments	2025-08-27 08:58:36.186861+00
18	022-add-auth-logs-comments	Add missing documentation comments to auth_logs table and columns	2025-08-27 09:00:48.809052+00
\.


--
-- Data for Name: missing_company_reports; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.missing_company_reports (id, user_email, email_domain, first_name, last_name, status, admin_notes, company_id, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: rate_limits; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.rate_limits (id, identifier, window_start, request_count, request_timestamps, expires_at, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: saved_companies; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.saved_companies (id, user_id, company_id, created_at) FROM stdin;
\.


--
-- Data for Name: search_queries; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.search_queries (id, query_text, user_id, session_id, results_count, filters_applied, ip_address, user_agent, created_at) FROM stdin;
\.


--
-- Data for Name: session_activity; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.session_activity (id, session_token, activity_type, ip_address, user_agent, created_at) FROM stdin;
\.


--
-- Data for Name: session_config; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.session_config (id, setting_name, setting_value, description, updated_at) FROM stdin;
1	default_session_duration_hours	24	Default session duration in hours	2025-08-14 12:50:58.335709+00
2	max_sessions_per_user	5	Maximum concurrent sessions per user	2025-08-14 12:50:58.335709+00
3	cleanup_interval_minutes	60	How often to run session cleanup in minutes	2025-08-14 12:50:58.335709+00
4	session_extension_hours	2	Hours to extend session on activity	2025-08-14 12:50:58.335709+00
\.


--
-- Data for Name: user_benefit_rankings; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.user_benefit_rankings (id, user_id, benefit_id, ranking, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: user_sessions; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.user_sessions (id, user_id, session_token, expires_at, created_at) FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: benefitlens_user
--

COPY public.users (id, email, first_name, last_name, email_verified, created_at, updated_at, role, payment_status, company_id) FROM stdin;
\.


--
-- Name: migration_log_id_seq; Type: SEQUENCE SET; Schema: public; Owner: benefitlens_user
--

SELECT pg_catalog.setval('public.migration_log_id_seq', 1, false);


--
-- Name: session_config_id_seq; Type: SEQUENCE SET; Schema: public; Owner: benefitlens_user
--

SELECT pg_catalog.setval('public.session_config_id_seq', 1, false);


--
-- Name: activity_log activity_log_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.activity_log
    ADD CONSTRAINT activity_log_pkey PRIMARY KEY (id);


--
-- Name: auth_logs auth_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.auth_logs
    ADD CONSTRAINT auth_logs_pkey PRIMARY KEY (id);


--
-- Name: benefit_categories benefit_categories_name_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.benefit_categories
    ADD CONSTRAINT benefit_categories_name_key UNIQUE (name);


--
-- Name: benefit_categories benefit_categories_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.benefit_categories
    ADD CONSTRAINT benefit_categories_pkey PRIMARY KEY (id);


--
-- Name: benefit_removal_disputes benefit_removal_disputes_company_benefit_id_user_id_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.benefit_removal_disputes
    ADD CONSTRAINT benefit_removal_disputes_company_benefit_id_user_id_key UNIQUE (company_benefit_id, user_id);


--
-- Name: benefit_removal_disputes benefit_removal_disputes_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.benefit_removal_disputes
    ADD CONSTRAINT benefit_removal_disputes_pkey PRIMARY KEY (id);


--
-- Name: benefit_search_interactions benefit_search_interactions_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.benefit_search_interactions
    ADD CONSTRAINT benefit_search_interactions_pkey PRIMARY KEY (id);


--
-- Name: benefit_verifications benefit_verifications_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.benefit_verifications
    ADD CONSTRAINT benefit_verifications_pkey PRIMARY KEY (id);


--
-- Name: benefits benefits_name_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.benefits
    ADD CONSTRAINT benefits_name_key UNIQUE (name);


--
-- Name: benefits benefits_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.benefits
    ADD CONSTRAINT benefits_pkey PRIMARY KEY (id);


--
-- Name: cache_store cache_store_cache_key_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.cache_store
    ADD CONSTRAINT cache_store_cache_key_key UNIQUE (cache_key);


--
-- Name: cache_store cache_store_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.cache_store
    ADD CONSTRAINT cache_store_pkey PRIMARY KEY (id);


--
-- Name: companies companies_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.companies
    ADD CONSTRAINT companies_pkey PRIMARY KEY (id);


--
-- Name: company_analytics_summary company_analytics_summary_company_id_date_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_analytics_summary
    ADD CONSTRAINT company_analytics_summary_company_id_date_key UNIQUE (company_id, date);


--
-- Name: company_analytics_summary company_analytics_summary_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_analytics_summary
    ADD CONSTRAINT company_analytics_summary_pkey PRIMARY KEY (id);


--
-- Name: company_benefits company_benefits_company_id_benefit_id_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_benefits
    ADD CONSTRAINT company_benefits_company_id_benefit_id_key UNIQUE (company_id, benefit_id);


--
-- Name: company_benefits company_benefits_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_benefits
    ADD CONSTRAINT company_benefits_pkey PRIMARY KEY (id);


--
-- Name: company_locations company_locations_company_id_location_normalized_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_locations
    ADD CONSTRAINT company_locations_company_id_location_normalized_key UNIQUE (company_id, location_normalized);


--
-- Name: company_locations company_locations_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_locations
    ADD CONSTRAINT company_locations_pkey PRIMARY KEY (id);


--
-- Name: company_page_views company_page_views_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_page_views
    ADD CONSTRAINT company_page_views_pkey PRIMARY KEY (id);


--
-- Name: company_users company_users_company_id_email_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_users
    ADD CONSTRAINT company_users_company_id_email_key UNIQUE (company_id, email);


--
-- Name: company_users company_users_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_users
    ADD CONSTRAINT company_users_pkey PRIMARY KEY (id);


--
-- Name: company_verification_tokens company_verification_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_verification_tokens
    ADD CONSTRAINT company_verification_tokens_pkey PRIMARY KEY (id);


--
-- Name: company_verification_tokens company_verification_tokens_token_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_verification_tokens
    ADD CONSTRAINT company_verification_tokens_token_key UNIQUE (token);


--
-- Name: csrf_tokens csrf_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.csrf_tokens
    ADD CONSTRAINT csrf_tokens_pkey PRIMARY KEY (id);


--
-- Name: csrf_tokens csrf_tokens_session_id_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.csrf_tokens
    ADD CONSTRAINT csrf_tokens_session_id_key UNIQUE (session_id);


--
-- Name: daily_analytics_summary daily_analytics_summary_date_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.daily_analytics_summary
    ADD CONSTRAINT daily_analytics_summary_date_key UNIQUE (date);


--
-- Name: daily_analytics_summary daily_analytics_summary_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.daily_analytics_summary
    ADD CONSTRAINT daily_analytics_summary_pkey PRIMARY KEY (id);


--
-- Name: magic_link_rate_limits magic_link_rate_limits_email_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.magic_link_rate_limits
    ADD CONSTRAINT magic_link_rate_limits_email_key UNIQUE (email);


--
-- Name: magic_link_rate_limits magic_link_rate_limits_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.magic_link_rate_limits
    ADD CONSTRAINT magic_link_rate_limits_pkey PRIMARY KEY (id);


--
-- Name: magic_link_tokens magic_link_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.magic_link_tokens
    ADD CONSTRAINT magic_link_tokens_pkey PRIMARY KEY (id);


--
-- Name: magic_link_tokens magic_link_tokens_token_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.magic_link_tokens
    ADD CONSTRAINT magic_link_tokens_token_key UNIQUE (token);


--
-- Name: migration_log migration_log_migration_name_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.migration_log
    ADD CONSTRAINT migration_log_migration_name_key UNIQUE (migration_name);


--
-- Name: migration_log migration_log_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.migration_log
    ADD CONSTRAINT migration_log_pkey PRIMARY KEY (id);


--
-- Name: missing_company_reports missing_company_reports_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.missing_company_reports
    ADD CONSTRAINT missing_company_reports_pkey PRIMARY KEY (id);


--
-- Name: rate_limits rate_limits_identifier_window_start_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.rate_limits
    ADD CONSTRAINT rate_limits_identifier_window_start_key UNIQUE (identifier, window_start);


--
-- Name: rate_limits rate_limits_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.rate_limits
    ADD CONSTRAINT rate_limits_pkey PRIMARY KEY (id);


--
-- Name: saved_companies saved_companies_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.saved_companies
    ADD CONSTRAINT saved_companies_pkey PRIMARY KEY (id);


--
-- Name: saved_companies saved_companies_user_id_company_id_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.saved_companies
    ADD CONSTRAINT saved_companies_user_id_company_id_key UNIQUE (user_id, company_id);


--
-- Name: search_queries search_queries_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.search_queries
    ADD CONSTRAINT search_queries_pkey PRIMARY KEY (id);


--
-- Name: session_activity session_activity_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.session_activity
    ADD CONSTRAINT session_activity_pkey PRIMARY KEY (id);


--
-- Name: session_config session_config_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.session_config
    ADD CONSTRAINT session_config_pkey PRIMARY KEY (id);


--
-- Name: session_config session_config_setting_name_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.session_config
    ADD CONSTRAINT session_config_setting_name_key UNIQUE (setting_name);


--
-- Name: user_benefit_rankings user_benefit_rankings_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.user_benefit_rankings
    ADD CONSTRAINT user_benefit_rankings_pkey PRIMARY KEY (id);


--
-- Name: user_benefit_rankings user_benefit_rankings_user_id_benefit_id_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.user_benefit_rankings
    ADD CONSTRAINT user_benefit_rankings_user_id_benefit_id_key UNIQUE (user_id, benefit_id);


--
-- Name: user_sessions user_sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_pkey PRIMARY KEY (id);


--
-- Name: user_sessions user_sessions_session_token_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_session_token_key UNIQUE (session_token);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: idx_activity_log_benefit_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_activity_log_benefit_id ON public.activity_log USING btree (benefit_id);


--
-- Name: idx_activity_log_company_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_activity_log_company_id ON public.activity_log USING btree (company_id);


--
-- Name: idx_activity_log_created_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_activity_log_created_at ON public.activity_log USING btree (created_at DESC);


--
-- Name: idx_activity_log_event_type; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_activity_log_event_type ON public.activity_log USING btree (event_type);


--
-- Name: idx_activity_log_user_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_activity_log_user_id ON public.activity_log USING btree (user_id);


--
-- Name: idx_auth_logs_created_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_auth_logs_created_at ON public.auth_logs USING btree (created_at);


--
-- Name: idx_auth_logs_email; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_auth_logs_email ON public.auth_logs USING btree (email);


--
-- Name: idx_auth_logs_event_type; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_auth_logs_event_type ON public.auth_logs USING btree (event_type);


--
-- Name: idx_auth_logs_failure_stats; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_auth_logs_failure_stats ON public.auth_logs USING btree (status, event_type, created_at) WHERE ((status)::text = 'failure'::text);


--
-- Name: idx_auth_logs_ip_address; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_auth_logs_ip_address ON public.auth_logs USING btree (ip_address);


--
-- Name: idx_auth_logs_status; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_auth_logs_status ON public.auth_logs USING btree (status);


--
-- Name: idx_auth_logs_status_created_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_auth_logs_status_created_at ON public.auth_logs USING btree (status, created_at);


--
-- Name: idx_benefit_categories_name; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefit_categories_name ON public.benefit_categories USING btree (name);


--
-- Name: idx_benefit_categories_sort_order; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefit_categories_sort_order ON public.benefit_categories USING btree (sort_order);


--
-- Name: idx_benefit_removal_disputes_company_benefit_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefit_removal_disputes_company_benefit_id ON public.benefit_removal_disputes USING btree (company_benefit_id);


--
-- Name: idx_benefit_removal_disputes_created_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefit_removal_disputes_created_at ON public.benefit_removal_disputes USING btree (created_at);


--
-- Name: idx_benefit_removal_disputes_status; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefit_removal_disputes_status ON public.benefit_removal_disputes USING btree (status);


--
-- Name: idx_benefit_removal_disputes_user_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefit_removal_disputes_user_id ON public.benefit_removal_disputes USING btree (user_id);


--
-- Name: idx_benefit_search_interactions_benefit_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefit_search_interactions_benefit_id ON public.benefit_search_interactions USING btree (benefit_id);


--
-- Name: idx_benefit_search_interactions_company_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefit_search_interactions_company_id ON public.benefit_search_interactions USING btree (company_id);


--
-- Name: idx_benefit_search_interactions_created_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefit_search_interactions_created_at ON public.benefit_search_interactions USING btree (created_at);


--
-- Name: idx_benefit_search_interactions_search_query_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefit_search_interactions_search_query_id ON public.benefit_search_interactions USING btree (search_query_id);


--
-- Name: idx_benefit_search_interactions_user_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefit_search_interactions_user_id ON public.benefit_search_interactions USING btree (user_id);


--
-- Name: idx_benefit_verifications_company_benefit_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefit_verifications_company_benefit_id ON public.benefit_verifications USING btree (company_benefit_id);


--
-- Name: idx_benefit_verifications_status; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefit_verifications_status ON public.benefit_verifications USING btree (status);


--
-- Name: idx_benefit_verifications_user_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefit_verifications_user_id ON public.benefit_verifications USING btree (user_id);


--
-- Name: idx_benefits_category_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefits_category_id ON public.benefits USING btree (category_id);


--
-- Name: idx_benefits_name; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefits_name ON public.benefits USING btree (name);


--
-- Name: idx_benefits_with_categories_cache_category; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefits_with_categories_cache_category ON public.benefits_with_categories_cache USING btree (category_id);


--
-- Name: idx_benefits_with_categories_cache_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE UNIQUE INDEX idx_benefits_with_categories_cache_id ON public.benefits_with_categories_cache USING btree (id);


--
-- Name: idx_benefits_with_categories_cache_name; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_benefits_with_categories_cache_name ON public.benefits_with_categories_cache USING gin (to_tsvector('english'::regconfig, (name)::text));


--
-- Name: idx_cache_store_created_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_cache_store_created_at ON public.cache_store USING btree (created_at);


--
-- Name: idx_cache_store_expires_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_cache_store_expires_at ON public.cache_store USING btree (expires_at);


--
-- Name: idx_cache_store_key; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_cache_store_key ON public.cache_store USING btree (cache_key);


--
-- Name: idx_companies_domain; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_companies_domain ON public.companies USING btree (domain);


--
-- Name: idx_companies_founded_year; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_companies_founded_year ON public.companies USING btree (founded_year) WHERE (founded_year IS NOT NULL);


--
-- Name: idx_companies_industry; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_companies_industry ON public.companies USING btree (industry);


--
-- Name: idx_companies_size; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_companies_size ON public.companies USING btree (size);


--
-- Name: idx_companies_website; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_companies_website ON public.companies USING btree (website) WHERE (website IS NOT NULL);


--
-- Name: idx_companies_with_benefits_cache_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE UNIQUE INDEX idx_companies_with_benefits_cache_id ON public.companies_with_benefits_cache USING btree (id);


--
-- Name: idx_companies_with_benefits_cache_industry; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_companies_with_benefits_cache_industry ON public.companies_with_benefits_cache USING btree (industry);


--
-- Name: idx_companies_with_benefits_cache_name; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_companies_with_benefits_cache_name ON public.companies_with_benefits_cache USING gin (to_tsvector('english'::regconfig, (name)::text));


--
-- Name: idx_companies_with_benefits_cache_size; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_companies_with_benefits_cache_size ON public.companies_with_benefits_cache USING btree (size);


--
-- Name: idx_company_analytics_summary_company_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_analytics_summary_company_id ON public.company_analytics_summary USING btree (company_id);


--
-- Name: idx_company_analytics_summary_date; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_analytics_summary_date ON public.company_analytics_summary USING btree (date);


--
-- Name: idx_company_benefits_benefit_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_benefits_benefit_id ON public.company_benefits USING btree (benefit_id);


--
-- Name: idx_company_benefits_company_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_benefits_company_id ON public.company_benefits USING btree (company_id);


--
-- Name: idx_company_benefits_verified; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_benefits_verified ON public.company_benefits USING btree (is_verified);


--
-- Name: idx_company_locations_city; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_locations_city ON public.company_locations USING btree (city);


--
-- Name: idx_company_locations_company_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_locations_company_id ON public.company_locations USING btree (company_id);


--
-- Name: idx_company_locations_coords; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_locations_coords ON public.company_locations USING btree (latitude, longitude) WHERE ((latitude IS NOT NULL) AND (longitude IS NOT NULL));


--
-- Name: idx_company_locations_country; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_locations_country ON public.company_locations USING btree (country);


--
-- Name: idx_company_locations_country_code; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_locations_country_code ON public.company_locations USING btree (country_code);


--
-- Name: idx_company_locations_headquarters; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_locations_headquarters ON public.company_locations USING btree (is_headquarters) WHERE (is_headquarters = true);


--
-- Name: idx_company_locations_normalized; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_locations_normalized ON public.company_locations USING btree (location_normalized);


--
-- Name: idx_company_locations_primary; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_locations_primary ON public.company_locations USING btree (is_primary) WHERE (is_primary = true);


--
-- Name: idx_company_locations_type; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_locations_type ON public.company_locations USING btree (location_type);


--
-- Name: idx_company_page_views_company_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_page_views_company_id ON public.company_page_views USING btree (company_id);


--
-- Name: idx_company_page_views_created_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_page_views_created_at ON public.company_page_views USING btree (created_at);


--
-- Name: idx_company_page_views_session_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_page_views_session_id ON public.company_page_views USING btree (session_id);


--
-- Name: idx_company_page_views_user_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_page_views_user_id ON public.company_page_views USING btree (user_id);


--
-- Name: idx_company_users_company_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_users_company_id ON public.company_users USING btree (company_id);


--
-- Name: idx_company_users_email; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_users_email ON public.company_users USING btree (email);


--
-- Name: idx_company_verification_tokens_company_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_verification_tokens_company_id ON public.company_verification_tokens USING btree (company_id);


--
-- Name: idx_company_verification_tokens_expires_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_verification_tokens_expires_at ON public.company_verification_tokens USING btree (expires_at);


--
-- Name: idx_company_verification_tokens_token; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_verification_tokens_token ON public.company_verification_tokens USING btree (token);


--
-- Name: idx_company_verification_tokens_user_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_company_verification_tokens_user_id ON public.company_verification_tokens USING btree (user_id);


--
-- Name: idx_csrf_tokens_expires_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_csrf_tokens_expires_at ON public.csrf_tokens USING btree (expires_at);


--
-- Name: idx_csrf_tokens_session_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_csrf_tokens_session_id ON public.csrf_tokens USING btree (session_id);


--
-- Name: idx_daily_analytics_summary_date; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_daily_analytics_summary_date ON public.daily_analytics_summary USING btree (date);


--
-- Name: idx_magic_link_rate_limits_email; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_magic_link_rate_limits_email ON public.magic_link_rate_limits USING btree (email);


--
-- Name: idx_magic_link_rate_limits_window_start; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_magic_link_rate_limits_window_start ON public.magic_link_rate_limits USING btree (window_start);


--
-- Name: idx_magic_link_tokens_email; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_magic_link_tokens_email ON public.magic_link_tokens USING btree (email);


--
-- Name: idx_magic_link_tokens_expires_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_magic_link_tokens_expires_at ON public.magic_link_tokens USING btree (expires_at);


--
-- Name: idx_magic_link_tokens_token; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_magic_link_tokens_token ON public.magic_link_tokens USING btree (token);


--
-- Name: idx_missing_company_reports_created_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_missing_company_reports_created_at ON public.missing_company_reports USING btree (created_at);


--
-- Name: idx_missing_company_reports_email_domain; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_missing_company_reports_email_domain ON public.missing_company_reports USING btree (email_domain);


--
-- Name: idx_missing_company_reports_status; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_missing_company_reports_status ON public.missing_company_reports USING btree (status);


--
-- Name: idx_missing_company_reports_user_email; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_missing_company_reports_user_email ON public.missing_company_reports USING btree (user_email);


--
-- Name: idx_rate_limits_expires_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_rate_limits_expires_at ON public.rate_limits USING btree (expires_at);


--
-- Name: idx_rate_limits_identifier; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_rate_limits_identifier ON public.rate_limits USING btree (identifier);


--
-- Name: idx_rate_limits_window_start; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_rate_limits_window_start ON public.rate_limits USING btree (window_start);


--
-- Name: idx_search_queries_created_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_search_queries_created_at ON public.search_queries USING btree (created_at);


--
-- Name: idx_search_queries_query_text; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_search_queries_query_text ON public.search_queries USING gin (to_tsvector('english'::regconfig, query_text));


--
-- Name: idx_search_queries_session_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_search_queries_session_id ON public.search_queries USING btree (session_id);


--
-- Name: idx_search_queries_user_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_search_queries_user_id ON public.search_queries USING btree (user_id);


--
-- Name: idx_session_activity_created_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_session_activity_created_at ON public.session_activity USING btree (created_at);


--
-- Name: idx_session_activity_token; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_session_activity_token ON public.session_activity USING btree (session_token);


--
-- Name: idx_user_benefit_rankings_benefit_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_user_benefit_rankings_benefit_id ON public.user_benefit_rankings USING btree (benefit_id);


--
-- Name: idx_user_benefit_rankings_ranking; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_user_benefit_rankings_ranking ON public.user_benefit_rankings USING btree (ranking);


--
-- Name: idx_user_benefit_rankings_updated_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_user_benefit_rankings_updated_at ON public.user_benefit_rankings USING btree (updated_at);


--
-- Name: idx_user_benefit_rankings_user_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_user_benefit_rankings_user_id ON public.user_benefit_rankings USING btree (user_id);


--
-- Name: idx_user_sessions_created_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_user_sessions_created_at ON public.user_sessions USING btree (created_at);


--
-- Name: idx_user_sessions_expires_at; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_user_sessions_expires_at ON public.user_sessions USING btree (expires_at);


--
-- Name: idx_user_sessions_expires_created; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_user_sessions_expires_created ON public.user_sessions USING btree (expires_at, created_at);


--
-- Name: idx_user_sessions_token; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_user_sessions_token ON public.user_sessions USING btree (session_token);


--
-- Name: idx_user_sessions_user_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_user_sessions_user_id ON public.user_sessions USING btree (user_id);


--
-- Name: idx_users_company_id; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_users_company_id ON public.users USING btree (company_id);


--
-- Name: idx_users_email; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_users_email ON public.users USING btree (email);


--
-- Name: idx_users_payment_status; Type: INDEX; Schema: public; Owner: benefitlens_user
--

CREATE INDEX idx_users_payment_status ON public.users USING btree (payment_status);


--
-- Name: benefit_removal_disputes update_benefit_removal_disputes_updated_at; Type: TRIGGER; Schema: public; Owner: benefitlens_user
--

CREATE TRIGGER update_benefit_removal_disputes_updated_at BEFORE UPDATE ON public.benefit_removal_disputes FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: companies update_companies_updated_at; Type: TRIGGER; Schema: public; Owner: benefitlens_user
--

CREATE TRIGGER update_companies_updated_at BEFORE UPDATE ON public.companies FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: company_analytics_summary update_company_analytics_summary_updated_at; Type: TRIGGER; Schema: public; Owner: benefitlens_user
--

CREATE TRIGGER update_company_analytics_summary_updated_at BEFORE UPDATE ON public.company_analytics_summary FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: daily_analytics_summary update_daily_analytics_summary_updated_at; Type: TRIGGER; Schema: public; Owner: benefitlens_user
--

CREATE TRIGGER update_daily_analytics_summary_updated_at BEFORE UPDATE ON public.daily_analytics_summary FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: missing_company_reports update_missing_company_reports_updated_at; Type: TRIGGER; Schema: public; Owner: benefitlens_user
--

CREATE TRIGGER update_missing_company_reports_updated_at BEFORE UPDATE ON public.missing_company_reports FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: user_benefit_rankings update_user_benefit_rankings_updated_at; Type: TRIGGER; Schema: public; Owner: benefitlens_user
--

CREATE TRIGGER update_user_benefit_rankings_updated_at BEFORE UPDATE ON public.user_benefit_rankings FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: users update_users_updated_at; Type: TRIGGER; Schema: public; Owner: benefitlens_user
--

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: activity_log activity_log_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.activity_log
    ADD CONSTRAINT activity_log_benefit_id_fkey FOREIGN KEY (benefit_id) REFERENCES public.benefits(id) ON DELETE SET NULL;


--
-- Name: activity_log activity_log_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.activity_log
    ADD CONSTRAINT activity_log_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE SET NULL;


--
-- Name: benefit_removal_disputes benefit_removal_disputes_company_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.benefit_removal_disputes
    ADD CONSTRAINT benefit_removal_disputes_company_benefit_id_fkey FOREIGN KEY (company_benefit_id) REFERENCES public.company_benefits(id) ON DELETE CASCADE;


--
-- Name: benefit_search_interactions benefit_search_interactions_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.benefit_search_interactions
    ADD CONSTRAINT benefit_search_interactions_benefit_id_fkey FOREIGN KEY (benefit_id) REFERENCES public.benefits(id) ON DELETE CASCADE;


--
-- Name: benefit_search_interactions benefit_search_interactions_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.benefit_search_interactions
    ADD CONSTRAINT benefit_search_interactions_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: benefit_search_interactions benefit_search_interactions_search_query_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.benefit_search_interactions
    ADD CONSTRAINT benefit_search_interactions_search_query_id_fkey FOREIGN KEY (search_query_id) REFERENCES public.search_queries(id) ON DELETE CASCADE;


--
-- Name: benefit_search_interactions benefit_search_interactions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.benefit_search_interactions
    ADD CONSTRAINT benefit_search_interactions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: benefit_verifications benefit_verifications_company_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.benefit_verifications
    ADD CONSTRAINT benefit_verifications_company_benefit_id_fkey FOREIGN KEY (company_benefit_id) REFERENCES public.company_benefits(id) ON DELETE CASCADE;


--
-- Name: benefits benefits_category_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.benefits
    ADD CONSTRAINT benefits_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.benefit_categories(id);


--
-- Name: company_analytics_summary company_analytics_summary_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_analytics_summary
    ADD CONSTRAINT company_analytics_summary_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_benefits company_benefits_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_benefits
    ADD CONSTRAINT company_benefits_benefit_id_fkey FOREIGN KEY (benefit_id) REFERENCES public.benefits(id) ON DELETE CASCADE;


--
-- Name: company_benefits company_benefits_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_benefits
    ADD CONSTRAINT company_benefits_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_locations company_locations_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_locations
    ADD CONSTRAINT company_locations_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_page_views company_page_views_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_page_views
    ADD CONSTRAINT company_page_views_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_page_views company_page_views_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_page_views
    ADD CONSTRAINT company_page_views_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: company_users company_users_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_users
    ADD CONSTRAINT company_users_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_verification_tokens company_verification_tokens_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_verification_tokens
    ADD CONSTRAINT company_verification_tokens_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: company_verification_tokens company_verification_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.company_verification_tokens
    ADD CONSTRAINT company_verification_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: missing_company_reports missing_company_reports_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.missing_company_reports
    ADD CONSTRAINT missing_company_reports_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE SET NULL;


--
-- Name: saved_companies saved_companies_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.saved_companies
    ADD CONSTRAINT saved_companies_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE CASCADE;


--
-- Name: search_queries search_queries_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.search_queries
    ADD CONSTRAINT search_queries_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: user_benefit_rankings user_benefit_rankings_benefit_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.user_benefit_rankings
    ADD CONSTRAINT user_benefit_rankings_benefit_id_fkey FOREIGN KEY (benefit_id) REFERENCES public.benefits(id) ON DELETE CASCADE;


--
-- Name: user_benefit_rankings user_benefit_rankings_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.user_benefit_rankings
    ADD CONSTRAINT user_benefit_rankings_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_sessions user_sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.user_sessions
    ADD CONSTRAINT user_sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: users users_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: benefitlens_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id) ON DELETE SET NULL;


--
-- Name: daily_analytics_summary Admin can view all analytics summaries; Type: POLICY; Schema: public; Owner: benefitlens_user
--

CREATE POLICY "Admin can view all analytics summaries" ON public.daily_analytics_summary FOR SELECT USING (true);


--
-- Name: benefit_search_interactions Admin can view all benefit interactions; Type: POLICY; Schema: public; Owner: benefitlens_user
--

CREATE POLICY "Admin can view all benefit interactions" ON public.benefit_search_interactions FOR SELECT USING (true);


--
-- Name: company_analytics_summary Admin can view all company analytics; Type: POLICY; Schema: public; Owner: benefitlens_user
--

CREATE POLICY "Admin can view all company analytics" ON public.company_analytics_summary FOR SELECT USING (true);


--
-- Name: company_page_views Admin can view all company page views; Type: POLICY; Schema: public; Owner: benefitlens_user
--

CREATE POLICY "Admin can view all company page views" ON public.company_page_views FOR SELECT USING (true);


--
-- Name: search_queries Admin can view all search queries; Type: POLICY; Schema: public; Owner: benefitlens_user
--

CREATE POLICY "Admin can view all search queries" ON public.search_queries FOR SELECT USING (true);


--
-- Name: benefit_removal_disputes Benefit removal disputes are viewable by admins; Type: POLICY; Schema: public; Owner: benefitlens_user
--

CREATE POLICY "Benefit removal disputes are viewable by admins" ON public.benefit_removal_disputes FOR SELECT USING (true);


--
-- Name: company_locations Public company locations are viewable by everyone; Type: POLICY; Schema: public; Owner: benefitlens_user
--

CREATE POLICY "Public company locations are viewable by everyone" ON public.company_locations FOR SELECT USING (true);


--
-- Name: benefit_removal_disputes; Type: ROW SECURITY; Schema: public; Owner: benefitlens_user
--

ALTER TABLE public.benefit_removal_disputes ENABLE ROW LEVEL SECURITY;

--
-- Name: benefit_search_interactions; Type: ROW SECURITY; Schema: public; Owner: benefitlens_user
--

ALTER TABLE public.benefit_search_interactions ENABLE ROW LEVEL SECURITY;

--
-- Name: company_analytics_summary; Type: ROW SECURITY; Schema: public; Owner: benefitlens_user
--

ALTER TABLE public.company_analytics_summary ENABLE ROW LEVEL SECURITY;

--
-- Name: company_locations; Type: ROW SECURITY; Schema: public; Owner: benefitlens_user
--

ALTER TABLE public.company_locations ENABLE ROW LEVEL SECURITY;

--
-- Name: company_page_views; Type: ROW SECURITY; Schema: public; Owner: benefitlens_user
--

ALTER TABLE public.company_page_views ENABLE ROW LEVEL SECURITY;

--
-- Name: daily_analytics_summary; Type: ROW SECURITY; Schema: public; Owner: benefitlens_user
--

ALTER TABLE public.daily_analytics_summary ENABLE ROW LEVEL SECURITY;

--
-- Name: search_queries; Type: ROW SECURITY; Schema: public; Owner: benefitlens_user
--

ALTER TABLE public.search_queries ENABLE ROW LEVEL SECURITY;

--
-- Name: benefits_with_categories_cache; Type: MATERIALIZED VIEW DATA; Schema: public; Owner: benefitlens_user
--

REFRESH MATERIALIZED VIEW public.benefits_with_categories_cache;


--
-- Name: companies_with_benefits_cache; Type: MATERIALIZED VIEW DATA; Schema: public; Owner: benefitlens_user
--

REFRESH MATERIALIZED VIEW public.companies_with_benefits_cache;


--
-- PostgreSQL database dump complete
--


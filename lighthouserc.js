module.exports = {
  ci: {
    collect: {
      staticDistDir: './.next',
      url: [
        'http://localhost:3000',
        'http://localhost:3000/benefits',
        'http://localhost:3000/companies',
        'http://localhost:3000/sign-in'
      ],
      numberOfRuns: 3
    },
    assert: {
      assertions: {
        'categories:performance': ['warn', { minScore: 0.8 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['warn', { minScore: 0.8 }],
        'categories:seo': ['warn', { minScore: 0.8 }]
      }
    },
    upload: {
      target: 'temporary-public-storage'
    }
  }
}

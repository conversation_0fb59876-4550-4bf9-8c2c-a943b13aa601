# E2E Testing CI Mirror Guide

This guide explains how to run E2E tests locally in a way that mirrors the GitHub CI pipeline exactly, addressing the authentication and timing issues that occur in CI but not locally.

## 🎯 Problem Solved

The E2E tests were failing in CI with authentication timeouts and "Complete Sign In" button not appearing, while working fine locally. This solution provides:

1. **Docker-based CI mirror environment** - Runs tests in containers identical to CI
2. **Improved authentication helpers** - More robust magic link authentication with extended timeouts
3. **Parameterized test runner** - Run specific test files with custom configurations
4. **Better error handling** - Enhanced debugging and retry logic

## 🚀 Quick Start

### Run a specific test file (mirrors CI exactly):
```bash
# Run user journeys test
./scripts/run-e2e-ci.sh src/__tests__/e2e/user-journeys.spec.ts

# Run admin workflows test
./scripts/run-e2e-ci.sh src/__tests__/e2e/admin-workflows.spec.ts

# Run benefit dispute test
./scripts/run-e2e-ci.sh src/__tests__/e2e/benefit-dispute-journey.spec.ts

# Run with specific config and browser
./scripts/run-e2e-ci.sh src/__tests__/e2e/user-journeys.spec.ts playwright-conservative.config.ts "Mobile Chrome"
```

### Using npm scripts:
```bash
# Run E2E tests in CI environment
npm run test:e2e:ci

# Setup E2E database manually
npm run db:setup:e2e
```

## 📁 New Files Created

### Docker Configuration
- `docker-compose.e2e.yml` - Complete E2E testing environment
- `Dockerfile.e2e` - Test runner container (mirrors CI exactly)
- `Dockerfile.e2e-app` - Application container for testing

### Scripts
- `scripts/run-e2e-ci.sh` - Main test runner script with parameters
- `scripts/db-setup-e2e.js` - Database setup for E2E tests

### Enhanced Files
- `src/__tests__/e2e/auth-helpers.ts` - Improved authentication with CI-friendly timeouts
- `package.json` - Added new npm scripts

## 🔧 How It Works

### 1. CI Mirror Environment
The Docker setup creates an environment identical to GitHub Actions:
- PostgreSQL 15 database
- Node.js 20 Alpine
- Same environment variables
- Same network configuration
- Same Playwright browser setup

### 2. Enhanced Authentication
The auth helpers now include:
- Extended timeouts for CI environment (45s vs 25s)
- Better error detection and handling
- Network idle state waiting
- Improved retry logic with longer waits
- More detailed logging for debugging

### 3. Parameterized Testing
The script accepts parameters:
```bash
./scripts/run-e2e-ci.sh [test-file] [config] [project]
```

Examples:
- `./scripts/run-e2e-ci.sh` - Runs default essential tests
- `./scripts/run-e2e-ci.sh src/__tests__/e2e/user-journeys.spec.ts` - Specific test
- `./scripts/run-e2e-ci.sh src/__tests__/e2e/admin-workflows.spec.ts playwright.config.ts chromium` - Full params

## 🐛 Debugging Failed Tests

### View container logs:
```bash
# After a test run, check logs
docker-compose -f docker-compose.e2e.yml logs e2e-runner
docker-compose -f docker-compose.e2e.yml logs app-e2e
```

### Manual debugging:
```bash
# Start services without running tests
docker-compose -f docker-compose.e2e.yml up postgres-e2e app-e2e

# Run tests manually in a separate terminal
docker-compose -f docker-compose.e2e.yml run --rm e2e-runner bash
```

### Check test artifacts:
Test results, screenshots, and traces are saved in `test-results/` directory.

## 🔍 Key Improvements Made

### Authentication Fixes
1. **Extended timeouts**: 45s for button visibility (was 25s)
2. **Network idle waiting**: Ensures page fully loads before proceeding
3. **Better error detection**: Checks for error states before waiting for buttons
4. **Enhanced retry logic**: More robust retry with longer waits
5. **Improved logging**: Better debugging information

### Environment Consistency
1. **Exact CI mirror**: Same OS, Node version, database setup
2. **Isolated environment**: No conflicts with local development
3. **Proper health checks**: Ensures services are ready before testing
4. **Clean state**: Fresh database and app state for each run

## 📊 Test Categories Supported

The solution supports all E2E test categories from CI:
- Essential & User Journey Tests
- Admin Workflow Tests  
- Mobile & Device Tests
- Accessibility & Benefit Dispute Tests

## 🛠️ Maintenance

### Updating the environment:
1. Modify `docker-compose.e2e.yml` for service changes
2. Update `Dockerfile.e2e` for test runner dependencies
3. Adjust `scripts/db-setup-e2e.js` for database schema changes

### Adding new test categories:
1. Add test files to `src/__tests__/e2e/`
2. Run with: `./scripts/run-e2e-ci.sh path/to/new-test.spec.ts`

## 🎉 Expected Results

With these improvements, the previously failing tests should now pass:
- ✅ User Benefit Ranking Journey
- ✅ Super Admin Workflow  
- ✅ Complete Benefit Dispute Journey
- ✅ All authentication flows

The solution provides a reliable way to reproduce and debug CI issues locally.

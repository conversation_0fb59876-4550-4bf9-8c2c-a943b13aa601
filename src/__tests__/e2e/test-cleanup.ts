/**
 * Shared cleanup utilities for E2E tests
 * Ensures proper cleanup of test data after each test
 */

import { query } from '@/lib/local-db'

/**
 * Clean up test data for specific test users
 * @param testEmails - Array of test email patterns to clean up
 */
export async function cleanupTestData(testEmails: string[] = [
  '%@industries.e2e',
  '%@techcorp.e2e', 
  '%@startup.e2e',
  '%@e2e.test'
]) {
  try {
    console.log('🧹 Cleaning up test data...')
    
    // Build email pattern for SQL LIKE queries
    const emailPattern = testEmails.join('\' OR email LIKE \'')
    const identifierPattern = testEmails.join('\' OR identifier LIKE \'')

    const cleanupQueries = [
      // Clean up user benefit rankings for test users
      `DELETE FROM user_benefit_rankings WHERE user_id IN (SELECT id FROM users WHERE email LIKE '${emailPattern}')`,

      // Clean up any test sessions
      `DELETE FROM user_sessions WHERE user_id IN (SELECT id FROM users WHERE email LIKE '${emailPattern}')`,

      // Clean up magic link tokens for test users
      `DELETE FROM magic_link_tokens WHERE email LIKE '${emailPattern}'`,

      // Clean up rate limiting data
      `DELETE FROM magic_link_rate_limits WHERE email LIKE '${emailPattern}'`,
      `DELETE FROM rate_limits WHERE identifier LIKE '${identifierPattern}'`,
    ]

    let totalRowsDeleted = 0
    for (const sql of cleanupQueries) {
      try {
        const result = await query(sql)
        const rowCount = result.rowCount || 0
        totalRowsDeleted += rowCount
        if (rowCount > 0) {
          console.log(`🗑️ Cleaned up ${rowCount} rows: ${sql.split(' ')[1]}`)
        }
      } catch (error) {
        console.warn('Test cleanup query failed (may be expected):', error)
      }
    }
    
    if (totalRowsDeleted > 0) {
      console.log(`✅ Test data cleanup complete - removed ${totalRowsDeleted} rows`)
    } else {
      console.log('✅ Test data cleanup complete - no data to clean')
    }
  } catch (error) {
    console.warn('Test data cleanup failed:', error)
  }
}

/**
 * Clean up test data for a specific user email
 * @param email - Specific email to clean up
 */
export async function cleanupUserTestData(email: string) {
  await cleanupTestData([email])
}

/**
 * Clean up all E2E test data (comprehensive cleanup)
 */
export async function cleanupAllE2ETestData() {
  try {
    console.log('🧹 Performing comprehensive E2E test data cleanup...')
    
    const cleanupQueries = [
      // Clear rate limiting data for e2e test emails
      'DELETE FROM magic_link_rate_limits WHERE email LIKE \'%@e2e.test\' OR email LIKE \'%@%.e2e\'',
      'DELETE FROM rate_limits WHERE identifier LIKE \'%@e2e.test\' OR identifier LIKE \'%@%.e2e\'',
      
      // Clear test data in dependency order (foreign keys)
      'DELETE FROM user_benefit_rankings WHERE user_id IN (SELECT id FROM users WHERE email LIKE \'%@e2e.test\' OR email LIKE \'%@%.e2e\')',
      'DELETE FROM company_benefits WHERE company_id IN (SELECT id FROM companies WHERE domain LIKE \'%.e2e\')',
      'DELETE FROM company_locations WHERE company_id IN (SELECT id FROM companies WHERE domain LIKE \'%.e2e\')',
      'DELETE FROM user_sessions WHERE user_id IN (SELECT id FROM users WHERE email LIKE \'%@e2e.test\' OR email LIKE \'%@%.e2e\')',
      'DELETE FROM magic_link_tokens WHERE email LIKE \'%@e2e.test\' OR email LIKE \'%@%.e2e\'',
      
      // Clear user and company data
      'DELETE FROM users WHERE email LIKE \'%@e2e.test\' OR email LIKE \'%@%.e2e\'',
      'DELETE FROM companies WHERE domain LIKE \'%.e2e\'',
      
      // Clear test benefits and categories
      'DELETE FROM benefits WHERE name LIKE \'E2E %\'',
      'DELETE FROM benefit_categories WHERE name LIKE \'e2e_%\'',
      
      // Clear any remaining test tokens with test- prefix
      'DELETE FROM magic_link_tokens WHERE token LIKE \'test-token-%\'',
      
      // Clear any orphaned sessions
      'DELETE FROM user_sessions WHERE user_id NOT IN (SELECT id FROM users)',
    ]

    let totalRowsDeleted = 0
    for (const sql of cleanupQueries) {
      try {
        const result = await query(sql)
        const rowCount = result.rowCount || 0
        totalRowsDeleted += rowCount
        if (rowCount > 0) {
          console.log(`🗑️ Cleaned up ${rowCount} rows from ${sql.split(' ')[3]}`)
        }
      } catch (error) {
        console.warn('Cleanup query failed (may be expected):', sql, error)
      }
    }
    
    console.log(`✅ Comprehensive E2E cleanup complete - removed ${totalRowsDeleted} rows`)
  } catch (error) {
    console.warn('Comprehensive E2E cleanup failed:', error)
  }
}

import { randomBytes } from 'crypto'
import { v4 as uuidv4 } from 'uuid'
import { cookies } from 'next/headers'
import { query } from './local-db'
import { EmailOptions } from './email'
import { setSession } from './session-storage'
import { logUserRegistered } from './activity-logger'
import { autoMatchUserToCompany } from './user-discovery'

// Create user from magic link (sign-up flow) - moved to top to avoid TDZ
const createUserFromMagicLink = async (userData: CreateUserData): Promise<MagicLinkUser> => {
  const { email, firstName, lastName, role = 'user', paymentStatus = 'free' } = userData

  const result = await query(
    `INSERT INTO users (email, first_name, last_name, role, payment_status, email_verified)
     VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
    [email, firstName, lastName, role, paymentStatus, true]
  )

  const user = result.rows[0] as MagicLinkUser

  // Log the user registration activity
  const userName = `${firstName || ''} ${lastName || ''}`.trim() || undefined
  await logUserRegistered(
    user.id,
    user.email,
    userName
  )

  // Automatically match user to company based on email domain
  try {
    const matchResult = await autoMatchUserToCompany(email, firstName || 'User')
    if (matchResult.matched) {
      console.log(`✅ User ${email} automatically matched to company ${matchResult.companyName}`)
    }
  } catch (error) {
    console.error('Error during automatic company matching:', error)
    // Don't fail user creation if company matching fails
  }

  return user
}

export interface MagicLinkUser {
  id: string
  email: string
  firstName: string | null
  lastName: string | null
  role: string
  paymentStatus: 'free' | 'paying'
  emailVerified: boolean
  createdAt: string
}

export interface CreateUserData {
  email: string
  firstName?: string
  lastName?: string
  role?: string
  paymentStatus?: 'free' | 'paying'
}

export interface MagicLinkTokenData {
  token: string
  email: string
  userData?: CreateUserData
  expiresAt: Date
}

// Generate cryptographically secure token
export function generateMagicLinkToken(): string {
  return randomBytes(32).toString('hex')
}

// Create magic link token for sign-in
export async function createSignInMagicLink(email: string): Promise<string> {
  const token = generateMagicLinkToken()
  const expiresAt = new Date(Date.now() + 30 * 60 * 1000) // 30 minutes

  // Check if user exists
  const userResult = await query(
    'SELECT id FROM users WHERE email = $1',
    [email.toLowerCase()]
  )

  if (userResult.rows.length === 0) {
    throw new Error('No account found with this email address')
  }

  // Store token
  await query(
    'INSERT INTO magic_link_tokens (token, email, expires_at) VALUES ($1, $2, $3)',
    [token, email.toLowerCase(), expiresAt.toISOString()]
  )

  return token
}

// Create magic link token for sign-up
export async function createSignUpMagicLink(userData: CreateUserData): Promise<string> {
  const token = generateMagicLinkToken()
  const expiresAt = new Date(Date.now() + 30 * 60 * 1000) // 30 minutes

  // Check if user already exists
  const existingUserResult = await query(
    'SELECT id FROM users WHERE email = $1',
    [userData.email.toLowerCase()]
  )

  if (existingUserResult.rows.length > 0) {
    throw new Error('An account with this email already exists')
  }

  // Store token with user data
  await query(
    'INSERT INTO magic_link_tokens (token, email, user_data, expires_at) VALUES ($1, $2, $3, $4)',
    [token, userData.email.toLowerCase(), JSON.stringify(userData), expiresAt.toISOString()]
  )

  return token
}



// Verify magic link token and create/authenticate user
export async function verifyMagicLinkToken(token: string): Promise<MagicLinkUser> {
  // Get token data
  const tokenResult = await query(
    'SELECT * FROM magic_link_tokens WHERE token = $1 AND used_at IS NULL',
    [token]
  )

  if (tokenResult.rows.length === 0) {
    throw new Error('Invalid or expired magic link')
  }

  const tokenData = tokenResult.rows[0]

  // Check if token is expired
  if (new Date(tokenData.expires_at) < new Date()) {
    throw new Error('Magic link has expired')
  }

  let user: MagicLinkUser

  // Check if this is a sign-up flow (has user_data)
  if (tokenData.user_data) {
    // Create new user
    const userData = tokenData.user_data // Already parsed by PostgreSQL JSONB
    user = await createUserFromMagicLink(userData)
  } else {
    // Sign in existing user
    const userResult = await query(
      'SELECT * FROM users WHERE email = $1',
      [tokenData.email]
    )

    if (userResult.rows.length === 0) {
      throw new Error('User not found')
    }

    const userData = userResult.rows[0]
    user = {
      id: userData.id,
      email: userData.email,
      firstName: userData.first_name,
      lastName: userData.last_name,
      role: userData.role,
      paymentStatus: userData.payment_status || 'free',
      emailVerified: true, // Magic link verifies email
      createdAt: userData.created_at,
    }

    // Check if user doesn't have a company_id and try to auto-match
    if (!userData.company_id) {
      try {
        const matchResult = await autoMatchUserToCompany(userData.email, userData.first_name || 'User')
        if (matchResult.matched) {
          console.log(`✅ Existing user ${userData.email} automatically matched to company ${matchResult.companyName}`)
        }
      } catch (error) {
        console.error('Error during automatic company matching for existing user:', error)
        // Don't fail sign-in if company matching fails
      }
    }
  }

  // Mark token as used ONLY after successful user creation/authentication
  await query(
    'UPDATE magic_link_tokens SET used_at = NOW() WHERE token = $1',
    [token]
  )

  return user
}



// Create session (reuse existing session logic)
export async function createSession(userId: string): Promise<string> {
  const sessionToken = uuidv4()
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days

  // Store session using unified session storage
  await setSession(sessionToken, userId, expiresAt)

  // Set cookie
  const cookieStore = await cookies()
  cookieStore.set('session_token', sessionToken, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    expires: expiresAt,
  })

  return sessionToken
}

// Rate limiting for magic link requests
export async function checkRateLimit(email: string): Promise<boolean> {
  // Check if rate limiting is disabled (for testing)
  const rateLimitingDisabled = process.env.DISABLE_RATE_LIMITING === 'true'

  // Also disable rate limiting for E2E test emails
  const isE2ETestEmail = email.includes('@e2e.test') || email.includes('.e2e')

  if (rateLimitingDisabled || isE2ETestEmail) {
    return true
  }

  const windowStart = new Date(Date.now() - 60 * 60 * 1000) // 1 hour window

  // Clean up old rate limit entries
  await query(
    'DELETE FROM magic_link_rate_limits WHERE window_start < $1',
    [windowStart.toISOString()]
  )

  // Check current rate limit
  const rateLimitResult = await query(
    'SELECT request_count FROM magic_link_rate_limits WHERE email = $1 AND window_start >= $2',
    [email.toLowerCase(), windowStart.toISOString()]
  )

  if (rateLimitResult.rows.length > 0) {
    const requestCount = rateLimitResult.rows[0].request_count

    if (requestCount >= 5) { // Max 5 requests per hour
      return false
    }

    // Increment request count
    await query(
      'UPDATE magic_link_rate_limits SET request_count = request_count + 1 WHERE email = $1',
      [email.toLowerCase()]
    )
  } else {
    // Create new rate limit entry
    await query(
      'INSERT INTO magic_link_rate_limits (email, request_count, window_start) VALUES ($1, 1, $2)',
      [email.toLowerCase(), windowStart.toISOString()]
    )
  }

  return true
}

// Create magic link email for sign-in
// Note: Uses URL fragment (#) instead of query parameter (?token=) to prevent
// corporate email gateways and link scanners from pre-opening the link
export function createSignInMagicLinkEmail(email: string, token: string): EmailOptions {
  const magicLinkUrl = `${process.env.APP_URL || 'http://localhost:3000'}/auth/magic-link#${token}`
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Sign in to BenefitLens</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { padding: 30px 20px; background: #f9fafb; border-radius: 0 0 8px 8px; }
        .button {
          display: inline-block;
          background: #2563eb !important;
          color: #ffffff !important;
          padding: 12px 24px;
          text-decoration: none !important;
          border-radius: 6px;
          margin: 20px 0;
          font-weight: bold !important;
          font-family: Arial, sans-serif !important;
          font-size: 16px !important;
          line-height: 1.4 !important;
          border: none !important;
        }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Sign in to BenefitLens</h1>
        </div>
        
        <div class="content">
          <p>Hello,</p>
          
          <p>Click the button below to sign in to your BenefitLens account:</p>
          
          <div style="text-align: center;">
            <a href="${magicLinkUrl}" class="button" style="display: inline-block; background: #2563eb !important; color: #ffffff !important; padding: 12px 24px; text-decoration: none !important; border-radius: 6px; margin: 20px 0; font-weight: bold !important; font-family: Arial, sans-serif !important; font-size: 16px !important; line-height: 1.4 !important; border: none !important;">Sign In to BenefitLens</a>
          </div>
          
          <p><strong>This link will expire in 30 minutes</strong> for your security.</p>
          
          <p>If you didn't request this sign-in link, you can safely ignore this email.</p>
          
          <div class="footer">
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p style="word-break: break-all;">${magicLinkUrl}</p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
    Sign in to BenefitLens
    
    Hello,
    
    Click the link below to sign in to your BenefitLens account:
    
    ${magicLinkUrl}
    
    This link will expire in 30 minutes for your security.
    
    If you didn't request this sign-in link, you can safely ignore this email.
  `

  return {
    to: email,
    subject: 'Sign in to BenefitLens',
    html,
    text,
  }
}

// Create magic link email for sign-up
// Note: Uses URL fragment (#) instead of query parameter (?token=) to prevent
// corporate email gateways and link scanners from pre-opening the link
export function createSignUpMagicLinkEmail(email: string, firstName: string | undefined, token: string): EmailOptions {
  const magicLinkUrl = `${process.env.APP_URL || 'http://localhost:3000'}/auth/magic-link#${token}`
  const name = firstName || 'there'

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Complete your BenefitLens account</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { padding: 30px 20px; background: #f9fafb; border-radius: 0 0 8px 8px; }
        .button {
          display: inline-block;
          background: #2563eb !important;
          color: #ffffff !important;
          padding: 12px 24px;
          text-decoration: none !important;
          border-radius: 6px;
          margin: 20px 0;
          font-weight: bold !important;
          font-family: Arial, sans-serif !important;
          font-size: 16px !important;
          line-height: 1.4 !important;
          border: none !important;
        }
        .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        .highlight { background: #e0f2fe; padding: 15px; border-radius: 6px; margin: 15px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Welcome to BenefitLens!</h1>
        </div>

        <div class="content">
          <p>Hello ${name},</p>

          <p>Welcome to BenefitLens! Click the button below to complete your account creation and start exploring company benefits:</p>

          <div style="text-align: center;">
            <a href="${magicLinkUrl}" class="button" style="display: inline-block; background: #2563eb !important; color: #ffffff !important; padding: 12px 24px; text-decoration: none !important; border-radius: 6px; margin: 20px 0; font-weight: bold !important; font-family: Arial, sans-serif !important; font-size: 16px !important; line-height: 1.4 !important; border: none !important;">Complete Account Setup</a>
          </div>

          <div class="highlight">
            <h3>What you can do with BenefitLens:</h3>
            <ul>
              <li>🏢 Discover companies and their benefits</li>
              <li>✅ Verify benefits for your company</li>
              <li>💾 Save companies you're interested in</li>
              <li>📊 Compare benefits across companies</li>
            </ul>
          </div>

          <p><strong>This link will expire in 30 minutes</strong> for your security.</p>

          <p>If you didn't create this account, you can safely ignore this email.</p>

          <div class="footer">
            <p>If the button doesn't work, copy and paste this link into your browser:</p>
            <p style="word-break: break-all;">${magicLinkUrl}</p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
    Welcome to BenefitLens!

    Hello ${name},

    Welcome to BenefitLens! Click the link below to complete your account creation:

    ${magicLinkUrl}

    What you can do with BenefitLens:
    - Discover companies and their benefits
    - Verify benefits for your company
    - Save companies you're interested in
    - Compare benefits across companies

    This link will expire in 30 minutes for your security.

    If you didn't create this account, you can safely ignore this email.
  `

  return {
    to: email,
    subject: 'Complete your BenefitLens account',
    html,
    text,
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin, isNextRedirectError } from '@/lib/auth'
import { query } from '@/lib/local-db'

/**
 * GET /api/admin/benefit-verifications
 * Get all benefit verifications for admin review
 */
export async function GET(request: NextRequest) {
  try {
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const status = searchParams.get('status') // 'pending', 'approved', 'rejected'
    const companyId = searchParams.get('companyId')

    const offset = (page - 1) * limit

    const whereConditions: string[] = []
    const params: (string | number)[] = []
    let paramIndex = 1

    if (status) {
      whereConditions.push(`bv.status = $${paramIndex}`)
      params.push(status)
      paramIndex++
    }

    if (companyId) {
      whereConditions.push(`c.id = $${paramIndex}`)
      params.push(companyId)
      paramIndex++
    }

    const whereClause = whereConditions.length > 0 
      ? `WHERE ${whereConditions.join(' AND ')}`
      : ''

    // Get benefit verifications with user, company, and benefit details
    const result = await query(
      `SELECT
        bv.id,
        bv.status,
        bv.comment,
        bv.created_at,
        u.email as user_email,
        u.first_name as user_first_name,
        u.last_name as user_last_name,
        c.name as company_name,
        c.id as company_id,
        b.name as benefit_name,
        b.id as benefit_id,
        cb.id as company_benefit_id
       FROM benefit_verifications bv
       JOIN users u ON bv.user_id::uuid = u.id
       JOIN company_benefits cb ON bv.company_benefit_id = cb.id
       JOIN companies c ON cb.company_id = c.id
       JOIN benefits b ON cb.benefit_id = b.id
       ${whereClause}
       ORDER BY bv.created_at DESC
       LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`,
      [...params, limit, offset]
    )

    // Get total count for pagination
    const countResult = await query(
      `SELECT COUNT(*) as total
       FROM benefit_verifications bv
       JOIN company_benefits cb ON bv.company_benefit_id = cb.id
       JOIN companies c ON cb.company_id = c.id
       ${whereClause}`,
      params
    )

    const total = parseInt(countResult.rows[0].total)
    const totalPages = Math.ceil(total / limit)

    return NextResponse.json({
      verifications: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    })

  } catch (error) {
    // Handle authentication and authorization errors properly
    if (isNextRedirectError(error)) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }
    if (error instanceof Error && error.message === 'Admin access required') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Only log actual server errors
    console.error('Error retrieving benefit verifications for admin:', error)
    return NextResponse.json(
      { error: 'Failed to retrieve benefit verifications' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/admin/benefit-verifications
 * Update benefit verification status (admin only)
 */
export async function PUT(request: NextRequest) {
  try {
    await requireAdmin()

    const body = await request.json()
    const { verificationId, status, adminNotes } = body

    if (!verificationId || !status) {
      return NextResponse.json(
        { error: 'Verification ID and status are required' },
        { status: 400 }
      )
    }

    if (!['pending', 'approved', 'rejected'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be pending, approved, or rejected' },
        { status: 400 }
      )
    }

    // Update the verification
    const result = await query(
      `UPDATE benefit_verifications
       SET status = $1, admin_notes = $2, updated_at = NOW()
       WHERE id = $3
       RETURNING *`,
      [status, adminNotes || null, verificationId]
    )

    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Verification not found' },
        { status: 404 }
      )
    }

    const verification = result.rows[0]

    return NextResponse.json({
      success: true,
      verification,
      message: `Verification ${status} successfully`
    })

  } catch (error) {
    console.error('Error updating benefit verification:', error)
    return NextResponse.json(
      { error: 'Failed to update benefit verification' },
      { status: 500 }
    )
  }
}

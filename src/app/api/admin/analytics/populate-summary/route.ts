import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { populateDailyAnalyticsSummary } from '@/lib/analytics-tracker'

// POST /api/admin/analytics/populate-summary - Populate daily analytics summary
export async function POST(request: NextRequest) {
  try {
    await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const daysBack = parseInt(searchParams.get('days') || '30')
    
    // Calculate start date
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - daysBack)
    
    // Populate summary data
    await populateDailyAnalyticsSummary(startDate)
    
    return NextResponse.json({
      success: true,
      message: `Daily analytics summary populated for the last ${daysBack} days`,
      startDate: startDate.toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0]
    })
    
  } catch (error) {
    console.error('Error populating analytics summary:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to populate analytics summary' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/local-db'
import { requireAdmin } from '@/lib/auth'

// POST /api/admin/benefit-categories/[categoryId]/migrate - Migrate benefits to another category
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ categoryId: string }> }
) {
  try {
    await requireAdmin()
    const { categoryId } = await params
    const body = await request.json()
    const { targetCategoryId } = body
    
    if (!targetCategoryId) {
      return NextResponse.json(
        { error: 'Target category ID is required' },
        { status: 400 }
      )
    }
    
    // Check if source category exists
    const sourceCategory = await query(
      'SELECT * FROM benefit_categories WHERE id = $1',
      [categoryId]
    )
    
    if (sourceCategory.rows.length === 0) {
      return NextResponse.json(
        { error: 'Source category not found' },
        { status: 404 }
      )
    }
    
    // Check if target category exists and is active
    const targetCategory = await query(
      'SELECT * FROM benefit_categories WHERE id = $1',
      [targetCategoryId]
    )
    
    if (targetCategory.rows.length === 0) {
      return NextResponse.json(
        { error: 'Target category not found or inactive' },
        { status: 404 }
      )
    }
    
    // Prevent migration to the same category
    if (categoryId === targetCategoryId) {
      return NextResponse.json(
        { error: 'Cannot migrate to the same category' },
        { status: 400 }
      )
    }
    
    // Get count of benefits to migrate
    const benefitCount = await query(
      'SELECT COUNT(*) as count FROM benefits WHERE category_id = $1',
      [categoryId]
    )
    
    const count = parseInt(benefitCount.rows[0].count)
    
    if (count === 0) {
      return NextResponse.json(
        { message: 'No benefits to migrate', migratedCount: 0 }
      )
    }
    
    // Migrate benefits to target category
    const _result = await query(
      `UPDATE benefits
       SET category_id = $1
       WHERE category_id = $2`,
      [targetCategoryId, categoryId]
    )
    
    return NextResponse.json({
      message: `Successfully migrated ${count} benefits`,
      migratedCount: count,
      sourceCategory: sourceCategory.rows[0].display_name,
      targetCategory: targetCategory.rows[0].display_name
    })
    
  } catch (error) {
    console.error('Error migrating benefits:', error)
    return NextResponse.json(
      { error: 'Failed to migrate benefits' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { getAuthFailureStats } from '@/lib/auth-logger'

// GET /api/admin/auth-logs - Get authentication failure statistics and logs
export async function GET(request: NextRequest) {
  try {
    await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days') || '7')
    
    // Validate days parameter
    if (days < 1 || days > 365) {
      return NextResponse.json(
        { error: 'Days parameter must be between 1 and 365' },
        { status: 400 }
      )
    }
    
    const stats = await getAuthFailureStats(days)
    
    return NextResponse.json(stats)
    
  } catch (error) {
    console.error('Error fetching auth logs:', error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch auth logs' },
      { status: 500 }
    )
  }
}

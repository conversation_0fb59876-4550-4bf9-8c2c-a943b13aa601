import { NextRequest, NextResponse } from 'next/server'
import { createSignUpMagicLink, checkRateLimit, createSignUpMagicLinkEmail } from '@/lib/magic-link-auth'
import { sendEmail } from '@/lib/email'
import { logSignUpRequest, logSignUpFailure, logRateLimitHit } from '@/lib/auth-logger'
import { getRequestContext } from '@/lib/logger'

export async function POST(request: NextRequest) {
  const requestContext = getRequestContext(request)

  try {
    const body = await request.json()
    const { email, firstName, lastName } = body

    if (!email) {
      await logSignUpFailure(
        email || 'unknown',
        'missing_email',
        'Email is required',
        requestContext.ip,
        requestContext.userAgent
      )
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      await logSignUpFailure(
        email,
        'invalid_email',
        'Invalid email format',
        requestContext.ip,
        requestContext.userAgent
      )
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Check rate limit
    const isAllowed = await checkRateLimit(email)
    if (!isAllowed) {
      await logRateLimitHit(
        email,
        'sign_up_request',
        requestContext.ip,
        requestContext.userAgent
      )
      return NextResponse.json(
        { error: 'Too many requests. Please try again later.' },
        { status: 429 }
      )
    }

    try {
      // Create magic link token with user data
      const token = await createSignUpMagicLink({
        email,
        firstName,
        lastName,
      })

      // Send magic link email
      const emailOptions = createSignUpMagicLinkEmail(email, firstName, token)
      await sendEmail(emailOptions)

      console.log('✅ Sign-up magic link sent:', email)

      // Log successful sign-up request
      await logSignUpRequest(email, requestContext.ip, requestContext.userAgent)

      return NextResponse.json({
        success: true,
        message: 'Magic link sent! Please check your email and click the link to complete your account setup.'
      }, { status: 201 })

    } catch (error) {
      if (error instanceof Error && error.message === 'An account with this email already exists') {
        await logSignUpFailure(
          email,
          'user_already_exists',
          error.message,
          requestContext.ip,
          requestContext.userAgent
        )
        return NextResponse.json(
          { error: 'An account with this email already exists. Please sign in instead.' },
          { status: 409 }
        )
      }

      // Log other errors
      await logSignUpFailure(
        email,
        'unknown_error',
        error instanceof Error ? error.message : 'Unknown error',
        requestContext.ip,
        requestContext.userAgent,
        { errorStack: error instanceof Error ? error.stack : undefined }
      )
      throw error
    }

  } catch (error) {
    console.error('Sign up error:', error)

    return NextResponse.json(
      { error: 'Failed to send magic link' },
      { status: 500 }
    )
  }
}

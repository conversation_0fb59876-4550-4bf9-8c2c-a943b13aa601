import { NextRequest, NextResponse } from 'next/server'
import { getAnalyticsAccessInfo } from '@/lib/analytics-access-control'
import { generateDemoTopCompanies } from '@/lib/demo-analytics-generator'
import { getTopCompanies } from '@/lib/analytics-tracker'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '7d'
    const limit = parseInt(searchParams.get('limit') || '10')
    const _benefitFilter = searchParams.get('benefit')

    // Check analytics access level
    const accessInfo = await getAnalyticsAccessInfo()

    // If user is not authenticated or on demo mode, return demo data
    if (accessInfo.level === 'none' || accessInfo.isDemoMode) {
      const demoData = generateDemoTopCompanies(period, limit)
      return NextResponse.json(demoData)
    }

    // Get real top companies data
    const topCompaniesData = await getTopCompanies(period, limit)
    return NextResponse.json(topCompaniesData)

  } catch (error) {
    console.error('Error fetching top companies:', error)
    return NextResponse.json(
      { error: 'Failed to fetch top companies' },
      { status: 500 }
    )
  }
}

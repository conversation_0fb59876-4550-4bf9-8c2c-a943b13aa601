import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { logger } from '@/lib/logger'

/**
 * GET /api/user/profile
 * Get current user's profile information
 */
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user profile from database
    const result = await query(
      'SELECT id, email, first_name, last_name, role, company_id, payment_status, created_at FROM users WHERE id = $1',
      [user.id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const userProfile = result.rows[0]

    logger.info('User profile retrieved', {
      userId: user.id,
      email: userProfile.email
    })

    return NextResponse.json(userProfile)
  } catch (error) {
    logger.error('Error retrieving user profile', { error: error instanceof Error ? error : String(error) })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/user/profile
 * Update current user's profile information
 */
export async function PUT(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { email, first_name, last_name } = body

    // Validate required fields
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Check if email is already taken by another user
    if (email !== user.email) {
      const existingUser = await query(
        'SELECT id FROM users WHERE email = $1 AND id != $2',
        [email, user.id]
      )

      if (existingUser.rows.length > 0) {
        return NextResponse.json(
          { error: 'Email already in use' },
          { status: 409 }
        )
      }
    }

    // Update user profile
    const result = await query(
      `UPDATE users
       SET email = $1, first_name = $2, last_name = $3, updated_at = NOW()
       WHERE id = $4
       RETURNING id, email, first_name, last_name, role, company_id, payment_status, created_at`,
      [email, first_name, last_name, user.id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const updatedUser = result.rows[0]

    logger.info('User profile updated', {
      userId: user.id,
      email: updatedUser.email,
      changes: { email, first_name, last_name }
    })

    return NextResponse.json({ user: updatedUser })
  } catch (error) {
    logger.error('Error updating user profile', { error: error instanceof Error ? error : String(error) })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

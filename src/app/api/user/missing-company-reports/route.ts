import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { logger } from '@/lib/logger'

/**
 * GET /api/user/missing-company-reports
 * Get current user's missing company reports
 */
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's missing company reports
    const result = await query(
      `SELECT
        id,
        user_email,
        email_domain,
        first_name,
        last_name,
        status,
        admin_notes,
        company_id,
        created_at,
        updated_at
       FROM missing_company_reports
       WHERE user_email = $1
       ORDER BY created_at DESC`,
      [user.email]
    )

    logger.info('User missing company reports retrieved', {
      userId: user.id,
      reportCount: result.rows.length
    })

    return NextResponse.json({ reports: result.rows })
  } catch (error) {
    logger.error('Error retrieving user missing company reports', { error: error instanceof Error ? error : String(error) })
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

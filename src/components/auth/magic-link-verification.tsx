'use client'

import { useEffect, useState, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { CheckCircle, XCircle, Loader2, Mail } from 'lucide-react'
import Link from 'next/link'

interface VerificationResult {
  success: boolean
  message?: string
  user?: {
    id: string
    email: string
    firstName: string | null
    lastName: string | null
    role: string
  }
}

export function MagicLinkVerification() {
  const router = useRouter()
  const [token, setToken] = useState<string | null>(null)
  const [isVerifying, setIsVerifying] = useState(false)
  const [isValidating, setIsValidating] = useState(true) // Validating token without consuming it
  const [result, setResult] = useState<VerificationResult | null>(null)
  const [showConfirmation, setShowConfirmation] = useState(false)

  // Extract token from URL fragment on client side
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const hash = window.location.hash
      if (hash && hash.length > 1) {
        const fragmentToken = hash.substring(1) // Remove the # character
        setToken(fragmentToken)
        validateTokenWithoutConsuming(fragmentToken)
      } else {
        // No token found, will show "Invalid Link" state
        setIsValidating(false)
        // Don't set result, let it fall through to "Invalid Link" state
      }
    }
  }, [])

  // Validate token exists and is not expired without consuming it
  const validateTokenWithoutConsuming = async (tokenToValidate: string) => {
    try {
      const response = await fetch('/api/auth/magic-link/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: tokenToValidate }),
      })

      const data = await response.json()

      if (response.ok) {
        setShowConfirmation(true)
      } else {
        setResult({
          success: false,
          message: data.error || 'Invalid or expired magic link'
        })
      }
    } catch (error) {
      console.error('Error validating magic link:', error)
      setResult({
        success: false,
        message: 'An error occurred while validating your magic link'
      })
    } finally {
      setIsValidating(false)
    }
  }

  const verifyMagicLink = useCallback(async (verificationToken: string) => {
    setIsVerifying(true)
    try {
      const response = await fetch('/api/auth/magic-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: verificationToken }),
      })

      const data = await response.json()

      if (response.ok) {
        setResult({
          success: true,
          user: data.user
        })

        // Redirect to dashboard after successful verification
        setTimeout(() => {
          router.push('/dashboard')
        }, 2000)
      } else {
        setResult({
          success: false,
          message: data.error || 'Invalid or expired magic link'
        })
      }
    } catch (error) {
      console.error('Error verifying magic link:', error)
      setResult({
        success: false,
        message: 'An error occurred while verifying your magic link'
      })
    } finally {
      setIsVerifying(false)
    }
  }, [router])

  // Handle user clicking the confirmation button
  const handleConfirmSignIn = () => {
    if (token) {
      setShowConfirmation(false)
      setIsVerifying(true) // Set this immediately to prevent flash of error state
      verifyMagicLink(token)
    }
  }



  return (
    <div className="bg-white shadow-lg rounded-lg p-8">
      {isValidating ? (
        <div className="text-center">
          <Loader2 className="w-12 h-12 text-blue-600 animate-spin mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Validating...
          </h2>
          <p className="text-gray-600">
            Please wait while we validate your magic link.
          </p>
        </div>
      ) : isVerifying ? (
        <div className="text-center">
          <Loader2 className="w-12 h-12 text-blue-600 animate-spin mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Signing In...
          </h2>
          <p className="text-gray-600">
            Please wait while we complete your sign in.
          </p>
        </div>
      ) : showConfirmation ? (
        <div className="text-center">
          <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Confirm Your Sign In
          </h2>
          <p className="text-gray-600 mb-6">
            Your magic link is valid! Click the button below to complete your sign in.
          </p>
          <div className="space-y-3">
            <button
              onClick={handleConfirmSignIn}
              disabled={isVerifying}
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              {isVerifying ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Signing In...</span>
                </>
              ) : (
                <span>Complete Sign In</span>
              )}
            </button>
            <Link
              href="/"
              className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors inline-block text-center"
            >
              Back to Home
            </Link>
          </div>
        </div>
      ) : result ? (
        <div className="text-center">
          {result.success ? (
            <>
              <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Authentication Successful!
              </h2>
              <p className="text-gray-600 mb-6">
                Welcome to BenefitLens! You&apos;re being redirected to your dashboard...
              </p>
              {result.user && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                  <div className="flex items-center justify-center space-x-2">
                    <Mail className="w-5 h-5 text-green-600" />
                    <span className="text-sm text-green-800">
                      Signed in as <strong>{result.user.email}</strong>
                    </span>
                  </div>
                </div>
              )}
              <div className="space-y-3">
                <Link
                  href="/dashboard"
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors inline-block text-center"
                >
                  Go to Dashboard
                </Link>
                <Link
                  href="/"
                  className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors inline-block text-center"
                >
                  Back to Home
                </Link>
              </div>
            </>
          ) : (
            <>
              <XCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Verification Failed
              </h2>
              <p className="text-gray-600 mb-6">
                {result.message || 'The magic link is invalid or has expired.'}
              </p>
              <div className="space-y-3">
                <Link
                  href="/sign-in"
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors inline-block text-center"
                >
                  Try Signing In Again
                </Link>
                <Link
                  href="/"
                  className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors inline-block text-center"
                >
                  Back to Home
                </Link>
              </div>
            </>
          )}
        </div>
      ) : (
        <div className="text-center">
          <XCircle className="w-12 h-12 text-red-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Invalid Link
          </h2>
          <p className="text-gray-600 mb-6">
            This magic link is invalid or missing required parameters.
          </p>
          <Link
            href="/sign-in"
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors inline-block text-center"
          >
            Sign In
          </Link>
        </div>
      )}
    </div>
  )
}

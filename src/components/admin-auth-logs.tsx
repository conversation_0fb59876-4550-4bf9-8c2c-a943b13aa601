'use client'

import { useState, useEffect } from 'react'

import { Badge } from '@/components/ui/badge'
import { <PERSON>ertTriangle, Shield, Clock, User, Globe, Smartphone } from 'lucide-react'

interface AuthLogEntry {
  id: string
  event_type: string
  status: string
  email?: string
  error_type?: string
  error_message?: string
  ip_address?: string
  user_agent?: string
  token_used?: string
  failure_reason?: string
  additional_context?: Record<string, unknown>
  created_at: string
}

interface AuthFailureStats {
  totalFailures: number
  failuresByType: Array<{ event_type: string; count: number }>
  failuresByReason: Array<{ failure_reason: string; count: number }>
  recentFailures: AuthLogEntry[]
}

export function AdminAuthLogs() {
  const [stats, setStats] = useState<AuthFailureStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedPeriod, setSelectedPeriod] = useState('7')

  const fetchAuthLogs = async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/auth-logs?days=${selectedPeriod}`)
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      } else {
        console.error('Failed to fetch auth logs')
      }
    } catch (error) {
      console.error('Error fetching auth logs:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAuthLogs()
  }, [selectedPeriod])

  const getEventTypeColor = (eventType: string) => {
    switch (eventType) {
      case 'sign_in_request':
        return 'bg-blue-100 text-blue-800'
      case 'sign_up_request':
        return 'bg-green-100 text-green-800'
      case 'magic_link_verification':
        return 'bg-purple-100 text-purple-800'
      case 'rate_limit_hit':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatEventType = (eventType: string) => {
    return eventType.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return `${Math.floor(diffInMinutes / 1440)}d ago`
  }

  const getUserAgentInfo = (userAgent?: string) => {
    if (!userAgent) return { device: 'Unknown', browser: 'Unknown' }
    
    const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent)
    const browser = userAgent.includes('Chrome') ? 'Chrome' :
                   userAgent.includes('Firefox') ? 'Firefox' :
                   userAgent.includes('Safari') ? 'Safari' :
                   userAgent.includes('Edge') ? 'Edge' : 'Unknown'
    
    return { device: isMobile ? 'Mobile' : 'Desktop', browser }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-700">Loading authentication logs...</span>
        </div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-700">Failed to load authentication logs</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Authentication Logs</h3>
          <p className="text-sm text-gray-700 mt-1">
            Monitor authentication failures and security events
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
          >
            <option value="1">Last 24 hours</option>
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
          </select>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-red-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-700">Total Failures</p>
              <p className="text-xl font-bold text-gray-900">{stats.totalFailures}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Shield className="w-5 h-5 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-700">Most Common</p>
              <p className="text-sm font-bold text-gray-900">
                {stats.failuresByReason[0]?.failure_reason || 'None'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <User className="w-5 h-5 text-purple-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-700">Sign-in Failures</p>
              <p className="text-xl font-bold text-gray-900">
                {stats.failuresByType.find(t => t.event_type === 'sign_in_request')?.count || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Clock className="w-5 h-5 text-green-600" />
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-700">Rate Limits</p>
              <p className="text-xl font-bold text-gray-900">
                {stats.failuresByType.find(t => t.event_type === 'rate_limit_hit')?.count || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Failure Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Failures by Type</h3>
            <p className="text-sm text-gray-600 mt-1">
              Breakdown of authentication failures by event type
            </p>
          </div>
          <div className="space-y-3">
            {stats.failuresByType.map((item) => (
              <div key={item.event_type} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge className={getEventTypeColor(item.event_type)}>
                    {formatEventType(item.event_type)}
                  </Badge>
                </div>
                <span className="font-medium text-gray-900">{item.count}</span>
              </div>
            ))}
            {stats.failuresByType.length === 0 && (
              <p className="text-gray-500 text-center py-4">No failures in selected period</p>
            )}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Top Failure Reasons</h3>
            <p className="text-sm text-gray-600 mt-1">
              Most common reasons for authentication failures
            </p>
          </div>
          <div className="space-y-3">
            {stats.failuresByReason.slice(0, 5).map((item) => (
              <div key={item.failure_reason} className="flex items-center justify-between">
                <span className="text-sm text-gray-900 flex-1 truncate">
                  {item.failure_reason}
                </span>
                <span className="font-medium text-gray-900 ml-2">{item.count}</span>
              </div>
            ))}
            {stats.failuresByReason.length === 0 && (
              <p className="text-gray-500 text-center py-4">No failure reasons recorded</p>
            )}
          </div>
        </div>
      </div>

      {/* Recent Failures */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Recent Authentication Failures</h3>
          <p className="text-sm text-gray-600 mt-1">
            Latest authentication failures with detailed information
          </p>
        </div>
        <div>
          <div className="space-y-4">
            {stats.recentFailures.map((failure) => {
              const userAgentInfo = getUserAgentInfo(failure.user_agent)
              return (
                <div key={failure.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <Badge className={getEventTypeColor(failure.event_type)}>
                          {formatEventType(failure.event_type)}
                        </Badge>
                        <span className="text-sm text-gray-700">
                          {formatTimeAgo(failure.created_at)}
                        </span>
                      </div>
                      
                      <div className="space-y-1">
                        {failure.email && (
                          <p className="text-sm text-gray-900">
                            <span className="font-medium">Email:</span> {failure.email}
                          </p>
                        )}
                        {failure.failure_reason && (
                          <p className="text-sm text-gray-900">
                            <span className="font-medium">Reason:</span> {failure.failure_reason}
                          </p>
                        )}
                        {failure.error_message && (
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Error:</span> {failure.error_message}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    <div className="mt-3 sm:mt-0 sm:ml-4 flex flex-col sm:items-end space-y-1">
                      {failure.ip_address && (
                        <div className="flex items-center text-xs text-gray-700">
                          <Globe className="w-3 h-3 mr-1" />
                          {failure.ip_address}
                        </div>
                      )}
                      {failure.user_agent && (
                        <div className="flex items-center text-xs text-gray-700">
                          <Smartphone className="w-3 h-3 mr-1" />
                          {userAgentInfo.device} • {userAgentInfo.browser}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )
            })}
            {stats.recentFailures.length === 0 && (
              <p className="text-gray-500 text-center py-8">No recent failures found</p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

#!/bin/bash

# Comprehensive Test Runner Script
# Runs unit, integration, and E2E tests with proper setup and reporting

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TEST_RESULTS_DIR="test-results"
COVERAGE_DIR="coverage"
LOG_FILE="$TEST_RESULTS_DIR/test-run.log"

# Create directories
mkdir -p "$TEST_RESULTS_DIR"
mkdir -p "$COVERAGE_DIR"

# Initialize log file
echo "Test run started at $(date)" > "$LOG_FILE"

# Helper functions
log() {
    echo -e "$1" | tee -a "$LOG_FILE"
}

run_command() {
    local cmd="$1"
    local description="$2"

    log "${BLUE}Running: $description${NC}"
    log "Command: $cmd"

    # Create a temporary file for command output
    local temp_output=$(mktemp)

    # Run command and capture exit code properly
    if eval "$cmd" > "$temp_output" 2>&1; then
        # Append output to log file
        cat "$temp_output" >> "$LOG_FILE"
        rm -f "$temp_output"
        log "${GREEN}✅ $description completed successfully${NC}"
        return 0
    else
        local exit_code=$?
        # Append output to log file
        cat "$temp_output" >> "$LOG_FILE"
        rm -f "$temp_output"
        log "${RED}❌ $description failed with exit code $exit_code${NC}"
        return $exit_code
    fi
}

check_prerequisites() {
    log "${BLUE}Checking prerequisites...${NC}"
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        log "${RED}❌ Node.js is not installed${NC}"
        exit 1
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        log "${RED}❌ npm is not installed${NC}"
        exit 1
    fi
    
    # Check if database is accessible
    if ! docker exec benefitlens-postgres pg_isready -U benefitlens_user -d benefitlens > /dev/null 2>&1; then
        log "${YELLOW}⚠️  Database health check failed - some tests may fail${NC}"
    fi
    
    log "${GREEN}✅ Prerequisites check completed${NC}"
}

setup_test_environment() {
    log "${BLUE}Setting up test environment...${NC}"

    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        run_command "npm install" "Installing dependencies"
    fi

    # Build the application
    run_command "npm run build" "Building application"

    # Set test environment variables
    export NODE_ENV=test
    # Note: Don't set CI=true as it affects how Next.js loads environment variables

    log "${GREEN}✅ Test environment setup completed${NC}"
}

start_test_server() {
    log "${BLUE}Starting test server for all test suites...${NC}"

    # Check if server is already running
    if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
        log "${YELLOW}⚠️  Server already running on port 3000${NC}"
        log "Please stop any existing servers and try again"
        return 1
    fi

    # Ensure environment variables are loaded
    if [ -f ".env.local" ]; then
        log "Loading environment variables from .env.local..."
        export $(grep -v '^#' .env.local | xargs)
    fi

    # Start the development server
    npm run dev > /dev/null 2>&1 &
    TEST_SERVER_PID=$!

    # Wait for server to be ready
    log "Waiting for server to be ready..."
    local attempts=0
    local max_attempts=30
    while [ $attempts -lt $max_attempts ]; do
        if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
            log "${GREEN}✅ Test server is ready on http://localhost:3000${NC}"
            return 0
        fi
        sleep 2
        attempts=$((attempts + 1))
    done

    log "${RED}❌ Server failed to start after ${max_attempts} attempts${NC}"
    if [ ! -z "$TEST_SERVER_PID" ]; then
        kill $TEST_SERVER_PID 2>/dev/null || true
        TEST_SERVER_PID=""
    fi
    return 1
}

stop_test_server() {
    if [ ! -z "$TEST_SERVER_PID" ]; then
        log "${BLUE}Stopping test server...${NC}"
        kill $TEST_SERVER_PID 2>/dev/null || true
        TEST_SERVER_PID=""
        log "${GREEN}✅ Test server stopped${NC}"
    fi
}

run_unit_tests() {
    log "${BLUE}Running Unit Tests...${NC}"

    local start_time=$(date +%s)

    # Clean up build artifacts that interfere with coverage
    rm -rf .next/server/edge* .next/standalone 2>/dev/null || true

    # Run tests with coverage but handle potential coverage issues gracefully
    if run_command "npm run test:unit -- --run --coverage --reporter=json --outputFile=$TEST_RESULTS_DIR/unit-results.json" "Unit tests"; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log "${GREEN}✅ Unit tests completed in ${duration}s${NC}"

        # Move coverage report
        if [ -d "coverage" ]; then
            mv coverage "$COVERAGE_DIR/unit"
        fi

        return 0
    else
        # If coverage fails, try running without coverage
        log "${YELLOW}⚠️  Unit tests with coverage failed, retrying without coverage...${NC}"
        if run_command "npm run test:unit -- --run --reporter=json --outputFile=$TEST_RESULTS_DIR/unit-results.json" "Unit tests (no coverage)"; then
            local end_time=$(date +%s)
            local duration=$((end_time - start_time))
            log "${GREEN}✅ Unit tests completed in ${duration}s (without coverage)${NC}"
            return 0
        else
            log "${RED}❌ Unit tests failed${NC}"
            return 1
        fi
    fi
}

run_integration_tests() {
    log "${BLUE}Running Integration Tests...${NC}"

    local start_time=$(date +%s)

    # Integration tests will use the shared test server
    log "Running integration tests against shared test server..."

    if run_command "npm run test:integration -- --run --reporter=json --outputFile=$TEST_RESULTS_DIR/integration-results.json" "Integration tests"; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log "${GREEN}✅ Integration tests completed in ${duration}s${NC}"
        return 0
    else
        log "${RED}❌ Integration tests failed${NC}"
        return 1
    fi
}

run_e2e_tests() {
    log "${BLUE}Running E2E Tests...${NC}"

    local start_time=$(date +%s)

    # E2E tests will use the shared test server
    log "Running E2E tests against shared test server..."

    if run_command "npm run test:e2e:full -- --reporter=json" "E2E tests"; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        log "${GREEN}✅ E2E tests completed in ${duration}s${NC}"
        return 0
    else
        log "${RED}❌ E2E tests failed${NC}"
        return 1
    fi
}

generate_reports() {
    log "${BLUE}Generating test reports...${NC}"
    
    # Combine test results
    cat > "$TEST_RESULTS_DIR/summary.json" << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "results": {
    "unit": $([ -f "$TEST_RESULTS_DIR/unit-results.json" ] && cat "$TEST_RESULTS_DIR/unit-results.json" || echo "null"),
    "integration": $([ -f "$TEST_RESULTS_DIR/integration-results.json" ] && cat "$TEST_RESULTS_DIR/integration-results.json" || echo "null"),
    "e2e": $([ -f "$TEST_RESULTS_DIR/e2e/results.json" ] && cat "$TEST_RESULTS_DIR/e2e/results.json" || echo "null")
  }
}
EOF
    
    # Generate HTML report
    cat > "$TEST_RESULTS_DIR/report.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>BenefitLens Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .failure { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>BenefitLens Test Report</h1>
        <p>Generated on: $(date)</p>
    </div>
    
    <div class="section">
        <h2>Test Summary</h2>
        <p>Check the individual test result files for detailed information:</p>
        <ul>
            <li><a href="unit-results.json">Unit Test Results</a></li>
            <li><a href="integration-results.json">Integration Test Results</a></li>
            <li><a href="e2e/results.json">E2E Test Results</a></li>
        </ul>
    </div>
    
    <div class="section">
        <h2>Coverage Reports</h2>
        <ul>
            <li><a href="../coverage/unit/index.html">Unit Test Coverage</a></li>
        </ul>
    </div>
    
    <div class="section">
        <h2>Test Log</h2>
        <pre>$(cat "$LOG_FILE")</pre>
    </div>
</body>
</html>
EOF
    
    log "${GREEN}✅ Test reports generated in $TEST_RESULTS_DIR/${NC}"
}

cleanup() {
    log "${BLUE}Cleaning up...${NC}"

    # Stop the shared test server
    stop_test_server

    # Kill playwright processes
    pkill -f "playwright" 2>/dev/null || true

    # Kill any remaining Next.js dev servers that might be hanging
    pkill -f "next-server" 2>/dev/null || true
    pkill -f "next dev" 2>/dev/null || true

    # Clean up temporary files
    rm -f .next/cache/webpack/server-development/*.pack* 2>/dev/null || true
    rm -f /tmp/test-output-* 2>/dev/null || true

    log "${GREEN}✅ Cleanup completed${NC}"
}

main() {
    local start_time=$(date +%s)
    local failed_tests=()

    log "${GREEN}🚀 Starting comprehensive test suite for BenefitLens${NC}"

    # Setup
    check_prerequisites
    setup_test_environment

    # Start shared test server for integration and E2E tests
    if ! start_test_server; then
        log "${RED}❌ Failed to start test server${NC}"
        exit 1
    fi

    # Run tests
    if ! run_unit_tests; then
        failed_tests+=("unit")
    fi

    if ! run_integration_tests; then
        failed_tests+=("integration")
    fi

    if ! run_e2e_tests; then
        failed_tests+=("e2e")
    fi
    
    # Generate reports
    generate_reports
    
    # Cleanup
    cleanup
    
    # Final summary
    local end_time=$(date +%s)
    local total_duration=$((end_time - start_time))
    
    log "\n${BLUE}📊 Test Suite Summary${NC}"
    log "Total duration: ${total_duration}s"
    log "Results directory: $TEST_RESULTS_DIR"
    
    if [ ${#failed_tests[@]} -eq 0 ]; then
        log "${GREEN}🎉 All tests passed successfully!${NC}"
        log "${GREEN}✅ BenefitLens is ready for production deployment${NC}"
        exit 0
    else
        log "${RED}❌ Some tests failed: ${failed_tests[*]}${NC}"
        log "${RED}🚫 BenefitLens is NOT ready for production deployment${NC}"
        exit 1
    fi
}

# Handle script interruption - only cleanup on EXIT, not on signals during build
trap cleanup EXIT

# Handle manual interruption more gracefully
trap 'log "${YELLOW}⚠️  Test suite interrupted by user${NC}"; cleanup; exit 130' INT TERM

# Run main function
main "$@"

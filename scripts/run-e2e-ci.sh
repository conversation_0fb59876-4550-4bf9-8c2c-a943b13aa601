#!/bin/bash

# E2E Test Runner Script - Mirrors CI Environment Locally
# Usage: ./scripts/run-e2e-ci.sh [test-file] [config] [project]
# Examples:
#   ./scripts/run-e2e-ci.sh src/__tests__/e2e/user-journeys.spec.ts
#   ./scripts/run-e2e-ci.sh src/__tests__/e2e/admin-workflows.spec.ts playwright-conservative.config.ts chromium
#   ./scripts/run-e2e-ci.sh src/__tests__/e2e/benefit-dispute-journey.spec.ts playwright.config.ts "Mobile Chrome"

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
DEFAULT_TEST_FILE="src/__tests__/e2e/essential-tests.spec.ts"
DEFAULT_CONFIG="playwright-conservative.config.ts"
DEFAULT_PROJECT="chromium"

# Parse arguments
TEST_FILE=${1:-$DEFAULT_TEST_FILE}
TEST_CONFIG=${2:-$DEFAULT_CONFIG}
TEST_PROJECT=${3:-$DEFAULT_PROJECT}

echo -e "${BLUE}🎭 BenefitLens E2E Test Runner (CI Mirror)${NC}"
echo -e "${BLUE}===========================================${NC}"
echo ""
echo -e "${YELLOW}Configuration:${NC}"
echo -e "  Test file: ${GREEN}$TEST_FILE${NC}"
echo -e "  Config: ${GREEN}$TEST_CONFIG${NC}"
echo -e "  Project: ${GREEN}$TEST_PROJECT${NC}"
echo ""

# Validate test file exists
if [ ! -f "$TEST_FILE" ]; then
    echo -e "${RED}❌ Error: Test file '$TEST_FILE' not found${NC}"
    echo -e "${YELLOW}Available test files:${NC}"
    find src/__tests__/e2e -name "*.spec.ts" | sort
    exit 1
fi

# Validate config file exists
if [ ! -f "$TEST_CONFIG" ]; then
    echo -e "${RED}❌ Error: Config file '$TEST_CONFIG' not found${NC}"
    echo -e "${YELLOW}Available config files:${NC}"
    find . -name "playwright*.config.ts" | sort
    exit 1
fi

# Function to cleanup on exit
cleanup() {
    echo -e "\n${YELLOW}🧹 Cleaning up containers...${NC}"
    docker-compose -f docker-compose.e2e.yml down --volumes --remove-orphans 2>/dev/null || true
}

# Set trap to cleanup on script exit
trap cleanup EXIT

# Stop any existing containers
echo -e "${YELLOW}🛑 Stopping any existing E2E containers...${NC}"
docker-compose -f docker-compose.e2e.yml down --volumes --remove-orphans 2>/dev/null || true

# Build and start the E2E environment
echo -e "${BLUE}🔨 Building E2E environment...${NC}"
export TEST_FILE="$TEST_FILE"
export TEST_CONFIG="$TEST_CONFIG"
export TEST_PROJECT="$TEST_PROJECT"

# Build the containers
docker-compose -f docker-compose.e2e.yml build --no-cache

# Start the services
echo -e "${BLUE}🚀 Starting E2E services...${NC}"
docker-compose -f docker-compose.e2e.yml up --abort-on-container-exit --exit-code-from e2e-runner

# Capture the exit code
EXIT_CODE=$?

if [ $EXIT_CODE -eq 0 ]; then
    echo -e "\n${GREEN}✅ E2E tests completed successfully!${NC}"
else
    echo -e "\n${RED}❌ E2E tests failed with exit code $EXIT_CODE${NC}"
    
    # Show logs for debugging
    echo -e "\n${YELLOW}📋 Container logs for debugging:${NC}"
    echo -e "${YELLOW}================================${NC}"
    docker-compose -f docker-compose.e2e.yml logs e2e-runner
fi

exit $EXIT_CODE

# Local secrets for GitHub Actions testing
# These are used only for local CI testing with 'act'

TEST_DATABASE_URL=postgresql://benefitlens_user:benefitlens_password@localhost:5432/benefitlens
NEXTAUTH_SECRET=local-test-secret-for-ci-testing
NEXTAUTH_URL=http://localhost:3000
SESSION_SECRET=local-session-secret-for-ci-testing
LHCI_GITHUB_APP_TOKEN=dummy-token-for-local-testing

# Note: These are dummy values for local testing only
# Real secrets are stored in GitHub repository secrets
